@echo off
:: 设置代码页为UTF-8，同时处理可能的错误
chcp 65001 >nul 2>&1
if errorlevel 1 (
    echo Setting UTF-8 encoding failed, using default encoding
)

:: 设置控制台标题
title Military Target Dataset Generation Platform - Startup Script

:: 设置环境变量确保Python使用UTF-8编码
set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1
set LANG=zh_CN.UTF-8

:: ================= 华为云 PyPI 镜像设置 =================
set "PIP_MIRROR=-i https://repo.huaweicloud.com/repository/pypi/simple/ --trusted-host repo.huaweicloud.com"

echo ========================================
echo    Military Target Dataset Generation Platform
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not found, please install Python 3.8+
    pause
    exit /b 1
)

echo [INFO] Python environment detected
echo.

:: 检查依赖是否安装
echo [INFO] Checking backend dependencies...
python -c "import fastapi, uvicorn, pydantic_settings, diffusers, transformers" >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Backend dependencies not fully installed, installing...
    echo [TIP] First run requires dependency installation, please wait...
    cd backend
    echo [INFO] Installing core dependencies...
    pip install %PIP_MIRROR% pydantic-settings>=2.0.3 accelerate>=0.20.0
    echo [INFO] Installing full dependencies...
    pip install %PIP_MIRROR% -r requirements.txt
    if errorlevel 1 (
        echo [ERROR] Backend dependency installation failed, please check network connection
        pause
        exit /b 1
    )
    cd ..
    echo [INFO] Backend dependencies installation completed
) else (
    echo [INFO] Backend dependencies check passed
)

echo [INFO] Checking frontend dependencies...
python -c "import PyQt6, requests" >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Frontend dependencies not fully installed, installing...
    echo [TIP] First run requires dependency installation, please wait...
    cd frontend
    pip install %PIP_MIRROR% -r requirements.txt
    if errorlevel 1 (
        echo [ERROR] Frontend dependency installation failed, please check network connection
        pause
        exit /b 1
    )
    cd ..
    echo [INFO] Frontend dependencies installation completed
) else (
    echo [INFO] Frontend dependencies check passed
)

echo.
echo [INFO] All dependency checks completed
echo.

:: ================= 启动平台（后端 + 前端）=================
echo [STARTUP] Starting platform (backend & frontend)...
cd /d %~dp0
python start.py

echo.
echo ========================================
echo [COMPLETED] Platform process exited.
echo Press any key to close this window...
echo ========================================
pause >nul 