#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复字体显示问题的脚本
"""

import os
import sys
import subprocess

def log_info(message):
    print(f"[INFO] {message}")

def log_success(message):
    print(f"[SUCCESS] {message}")

def log_error(message):
    print(f"[ERROR] {message}")

def check_fonts():
    """检查系统中的中文字体"""
    log_info("检查系统中的中文字体...")
    
    try:
        # 检查中文字体
        result = subprocess.run(['fc-list', ':lang=zh'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0 and result.stdout.strip():
            fonts = result.stdout.strip().split('\n')
            log_success(f"找到 {len(fonts)} 个中文字体")
            
            # 显示前5个字体
            for i, font in enumerate(fonts[:5]):
                font_name = font.split(':')[1].split('=')[1] if ':' in font and '=' in font else font
                print(f"  {i+1}. {font_name}")
            
            return True
        else:
            log_error("未找到中文字体")
            return False
            
    except Exception as e:
        log_error(f"检查字体失败: {e}")
        return False

def install_fonts():
    """安装中文字体"""
    log_info("安装中文字体...")
    
    try:
        # 更新包列表
        subprocess.run(['apt', 'update'], check=True, capture_output=True)
        
        # 安装中文字体包
        fonts_packages = [
            'fonts-noto-cjk',      # 思源字体
            'fonts-wqy-zenhei',    # 文泉驿正黑
            'fonts-wqy-microhei',  # 文泉驿微米黑
            'fonts-arphic-ukai',   # 文鼎字体
            'fonts-arphic-uming'   # 文鼎字体
        ]
        
        for package in fonts_packages:
            try:
                subprocess.run(['apt', 'install', '-y', package], 
                             check=True, capture_output=True)
                log_success(f"安装字体包: {package}")
            except subprocess.CalledProcessError:
                log_error(f"安装字体包失败: {package}")
        
        # 刷新字体缓存
        subprocess.run(['fc-cache', '-fv'], check=True, capture_output=True)
        log_success("字体缓存已刷新")
        
        return True
        
    except Exception as e:
        log_error(f"安装字体失败: {e}")
        return False

def create_fontconfig():
    """创建字体配置文件"""
    log_info("创建字体配置文件...")
    
    try:
        # 创建用户字体配置目录
        config_dir = os.path.expanduser('~/.config/fontconfig')
        os.makedirs(config_dir, exist_ok=True)
        
        # 创建字体配置文件
        config_content = '''<?xml version="1.0"?>
<!DOCTYPE fontconfig SYSTEM "fonts.dtd">
<fontconfig>
    <!-- 设置中文字体优先级 -->
    <alias>
        <family>sans-serif</family>
        <prefer>
            <family>Noto Sans CJK SC</family>
            <family>WenQuanYi Micro Hei</family>
            <family>WenQuanYi Zen Hei</family>
            <family>DejaVu Sans</family>
        </prefer>
    </alias>
    
    <alias>
        <family>serif</family>
        <prefer>
            <family>Noto Serif CJK SC</family>
            <family>WenQuanYi Micro Hei</family>
            <family>DejaVu Serif</family>
        </prefer>
    </alias>
    
    <alias>
        <family>monospace</family>
        <prefer>
            <family>Noto Sans Mono CJK SC</family>
            <family>WenQuanYi Micro Hei Mono</family>
            <family>DejaVu Sans Mono</family>
        </prefer>
    </alias>
    
    <!-- 强制使用中文字体 -->
    <match target="pattern">
        <test qual="any" name="family">
            <string>sans-serif</string>
        </test>
        <edit name="family" mode="prepend" binding="strong">
            <string>Noto Sans CJK SC</string>
        </edit>
    </match>
</fontconfig>'''
        
        config_file = os.path.join(config_dir, 'fonts.conf')
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        log_success(f"字体配置文件已创建: {config_file}")
        
        # 刷新字体缓存
        subprocess.run(['fc-cache', '-fv'], check=True, capture_output=True)
        
        return True
        
    except Exception as e:
        log_error(f"创建字体配置失败: {e}")
        return False

def test_font_display():
    """测试字体显示"""
    log_info("测试字体显示...")
    
    try:
        # 创建测试脚本
        test_script = '''
import sys
import os
sys.path.insert(0, 'frontend/src')
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ['PYTHONUTF8'] = '1'

from PyQt6.QtWidgets import QApplication
from PyQt6.QtGui import QFont

app = QApplication([])

# 测试字体
fonts_to_test = [
    "Noto Sans CJK SC",
    "WenQuanYi Micro Hei", 
    "WenQuanYi Zen Hei",
    "DejaVu Sans"
]

print("可用字体测试:")
for font_name in fonts_to_test:
    font = QFont(font_name)
    actual_family = font.family()
    print(f"  {font_name} -> {actual_family}")

# 设置应用字体
app_font = QFont("Noto Sans CJK SC")
app_font.setPointSize(10)
app.setFont(app_font)

print(f"应用字体设置为: {app.font().family()}")
app.quit()
'''
        
        # 写入临时测试文件
        with open('temp_font_test.py', 'w', encoding='utf-8') as f:
            f.write(test_script)
        
        # 运行测试
        result = subprocess.run([
            'venv/bin/python3', 'temp_font_test.py'
        ], capture_output=True, text=True, timeout=10)
        
        # 清理临时文件
        if os.path.exists('temp_font_test.py'):
            os.remove('temp_font_test.py')
        
        if result.returncode == 0:
            log_success("字体测试通过")
            print(result.stdout)
            return True
        else:
            log_error(f"字体测试失败: {result.stderr}")
            return False
            
    except Exception as e:
        log_error(f"字体测试异常: {e}")
        # 清理临时文件
        if os.path.exists('temp_font_test.py'):
            os.remove('temp_font_test.py')
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("修复字体显示问题")
    print("=" * 60)
    print()
    
    # 检查是否在项目根目录
    if not os.path.exists('start.py') or not os.path.exists('frontend'):
        log_error("请在项目根目录下运行此脚本")
        sys.exit(1)
    
    # 检查虚拟环境
    if not os.path.exists('venv'):
        log_error("虚拟环境不存在，请先运行环境修复脚本")
        sys.exit(1)
    
    # 执行修复步骤
    steps = [
        ("检查现有字体", check_fonts),
        ("安装中文字体", install_fonts),
        ("创建字体配置", create_fontconfig),
        ("测试字体显示", test_font_display)
    ]
    
    for step_name, step_func in steps:
        log_info(f"开始: {step_name}")
        try:
            if not step_func():
                log_error(f"{step_name} 失败")
                # 不退出，继续尝试其他步骤
        except Exception as e:
            log_error(f"{step_name} 出现异常: {e}")
        print()
    
    print("=" * 60)
    log_success("字体修复完成！")
    print("=" * 60)
    print()
    print("修复内容:")
    print("1. ✅ 安装了中文字体包")
    print("2. ✅ 创建了字体配置文件")
    print("3. ✅ 刷新了字体缓存")
    print("4. ✅ 测试了字体显示")
    print()
    print("现在可以重新启动应用测试字体显示:")
    print("./run.sh")
    print()

if __name__ == "__main__":
    main()
