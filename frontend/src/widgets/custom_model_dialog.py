"""
自定义模型管理对话框
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QListWidget,
    QListWidgetItem, QMessageBox, QInputDialog, QFileDialog, QGroupBox,
    QFormLayout, QLineEdit, QComboBox, QTextEdit, QCheckBox, QSpinBox,
    QDoubleSpinBox, QTabWidget, QWidget, QSplitter, QFrame
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont

from services.custom_model_client import CustomModelClient
from styles.button_styles import ButtonStyleManager
import logging

logger = logging.getLogger(__name__)

class CustomModelListWorker(QThread):
    """自定义模型列表加载工作线程"""
    finished = pyqtSignal(dict)
    error = pyqtSignal(str)
    
    def __init__(self, client):
        super().__init__()
        self.client = client
    
    def run(self):
        try:
            result = self.client.list_custom_models()
            self.finished.emit(result)
        except Exception as e:
            self.error.emit(str(e))

class CustomModelDialog(QDialog):
    """自定义模型管理对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.client = CustomModelClient()
        self.current_model = None
        
        self.setWindowTitle("自定义模型管理")
        self.setModal(True)
        self.resize(900, 700)
        
        self.setup_ui()
        self.load_models()
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 模型列表标签页
        models_tab = self.create_models_tab()
        tab_widget.addTab(models_tab, "模型列表")
        
        # 添加模型标签页
        add_tab = self.create_add_model_tab()
        tab_widget.addTab(add_tab, "添加模型")
        
        # 统计信息标签页
        stats_tab = self.create_statistics_tab()
        tab_widget.addTab(stats_tab, "统计信息")
        
        layout.addWidget(tab_widget)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        self.refresh_btn = ButtonStyleManager.create_button("刷新", "secondary")
        self.refresh_btn.clicked.connect(self.load_models)
        button_layout.addWidget(self.refresh_btn)
        
        button_layout.addStretch()
        
        close_btn = ButtonStyleManager.create_button("关闭", "secondary")
        close_btn.clicked.connect(self.close)
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
    
    def create_models_tab(self):
        """创建模型列表标签页"""
        tab = QWidget()
        layout = QHBoxLayout(tab)
        
        # 左侧：模型列表
        left_panel = QFrame()
        left_layout = QVBoxLayout(left_panel)
        
        # 模型列表
        self.model_list = QListWidget()
        self.model_list.itemClicked.connect(self.on_model_selected)
        left_layout.addWidget(QLabel("自定义模型列表:"))
        left_layout.addWidget(self.model_list)
        
        # 模型操作按钮
        model_ops_layout = QHBoxLayout()
        
        self.enable_btn = ButtonStyleManager.create_button("启用", "success")
        self.enable_btn.clicked.connect(self.enable_model)
        self.enable_btn.setEnabled(False)
        model_ops_layout.addWidget(self.enable_btn)
        
        self.disable_btn = ButtonStyleManager.create_button("禁用", "warning")
        self.disable_btn.clicked.connect(self.disable_model)
        self.disable_btn.setEnabled(False)
        model_ops_layout.addWidget(self.disable_btn)
        
        self.validate_btn = ButtonStyleManager.create_button("验证", "info")
        self.validate_btn.clicked.connect(self.validate_model)
        self.validate_btn.setEnabled(False)
        model_ops_layout.addWidget(self.validate_btn)
        
        self.remove_btn = ButtonStyleManager.create_button("删除", "danger")
        self.remove_btn.clicked.connect(self.remove_model)
        self.remove_btn.setEnabled(False)
        model_ops_layout.addWidget(self.remove_btn)
        
        left_layout.addLayout(model_ops_layout)
        
        # 右侧：模型详情
        right_panel = QFrame()
        right_layout = QVBoxLayout(right_panel)
        
        right_layout.addWidget(QLabel("模型详情:"))
        self.model_details = QTextEdit()
        self.model_details.setReadOnly(True)
        right_layout.addWidget(self.model_details)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 1)
        
        layout.addWidget(splitter)
        
        return tab
    
    def create_add_model_tab(self):
        """创建添加模型标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 添加模型表单
        form_group = QGroupBox("添加新的自定义模型")
        form_layout = QFormLayout(form_group)
        
        self.model_key_input = QLineEdit()
        self.model_key_input.setPlaceholderText("例如: my-custom-model")
        form_layout.addRow("模型键名*:", self.model_key_input)
        
        self.model_name_input = QLineEdit()
        self.model_name_input.setPlaceholderText("例如: 我的自定义模型")
        form_layout.addRow("模型名称*:", self.model_name_input)
        
        self.model_path_input = QLineEdit()
        self.model_path_input.setPlaceholderText("本地路径或HuggingFace模型ID")
        form_layout.addRow("模型路径*:", self.model_path_input)
        
        # 浏览按钮
        path_layout = QHBoxLayout()
        path_layout.addWidget(self.model_path_input)
        browse_btn = ButtonStyleManager.create_button("浏览", "secondary")
        browse_btn.clicked.connect(self.browse_model_path)
        path_layout.addWidget(browse_btn)
        form_layout.addRow("", path_layout)
        
        self.description_input = QTextEdit()
        self.description_input.setMaximumHeight(80)
        self.description_input.setPlaceholderText("模型描述（可选）")
        form_layout.addRow("描述:", self.description_input)
        
        self.model_type_combo = QComboBox()
        self.model_type_combo.addItems(["diffusers", "safetensors", "checkpoint"])
        form_layout.addRow("模型类型:", self.model_type_combo)
        
        # 高级参数
        advanced_group = QGroupBox("高级参数（可选）")
        advanced_layout = QFormLayout(advanced_group)
        
        # CFG范围
        cfg_layout = QHBoxLayout()
        self.cfg_min_spin = QDoubleSpinBox()
        self.cfg_min_spin.setRange(1.0, 20.0)
        self.cfg_min_spin.setValue(7.0)
        self.cfg_max_spin = QDoubleSpinBox()
        self.cfg_max_spin.setRange(1.0, 20.0)
        self.cfg_max_spin.setValue(12.0)
        cfg_layout.addWidget(self.cfg_min_spin)
        cfg_layout.addWidget(QLabel("到"))
        cfg_layout.addWidget(self.cfg_max_spin)
        cfg_layout.addStretch()
        advanced_layout.addRow("CFG范围:", cfg_layout)
        
        # 步数范围
        steps_layout = QHBoxLayout()
        self.steps_min_spin = QSpinBox()
        self.steps_min_spin.setRange(1, 200)
        self.steps_min_spin.setValue(20)
        self.steps_max_spin = QSpinBox()
        self.steps_max_spin.setRange(1, 200)
        self.steps_max_spin.setValue(50)
        steps_layout.addWidget(self.steps_min_spin)
        steps_layout.addWidget(QLabel("到"))
        steps_layout.addWidget(self.steps_max_spin)
        steps_layout.addStretch()
        advanced_layout.addRow("步数范围:", steps_layout)
        
        self.target_size_combo = QComboBox()
        self.target_size_combo.addItems(["small", "medium", "large", "controllable"])
        self.target_size_combo.setCurrentText("medium")
        advanced_layout.addRow("目标大小偏好:", self.target_size_combo)
        
        layout.addWidget(form_group)
        layout.addWidget(advanced_group)
        
        # 添加按钮
        add_layout = QHBoxLayout()
        add_layout.addStretch()
        
        self.add_model_btn = ButtonStyleManager.create_button("添加模型", "primary")
        self.add_model_btn.clicked.connect(self.add_model)
        add_layout.addWidget(self.add_model_btn)
        
        self.import_btn = ButtonStyleManager.create_button("从目录导入", "info")
        self.import_btn.clicked.connect(self.import_from_directory)
        add_layout.addWidget(self.import_btn)
        
        layout.addLayout(add_layout)
        layout.addStretch()
        
        return tab
    
    def create_statistics_tab(self):
        """创建统计信息标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        self.stats_text = QTextEdit()
        self.stats_text.setReadOnly(True)
        layout.addWidget(self.stats_text)
        
        return tab
    
    def load_models(self):
        """加载自定义模型列表"""
        self.refresh_btn.setEnabled(False)
        self.refresh_btn.setText("加载中...")
        
        self.model_worker = CustomModelListWorker(self.client)
        self.model_worker.finished.connect(self.on_models_loaded)
        self.model_worker.error.connect(self.on_models_error)
        self.model_worker.start()
    
    def on_models_loaded(self, result):
        """模型列表加载完成"""
        self.refresh_btn.setEnabled(True)
        self.refresh_btn.setText("刷新")
        
        if result.get("success"):
            models = result.get("data", {}).get("models", {})
            self.update_model_list(models)
            self.load_statistics()
        else:
            QMessageBox.warning(self, "错误", f"加载自定义模型失败: {result.get('message')}")
    
    def on_models_error(self, error_message):
        """模型列表加载错误"""
        self.refresh_btn.setEnabled(True)
        self.refresh_btn.setText("刷新")
        QMessageBox.critical(self, "错误", f"加载自定义模型失败: {error_message}")
    
    def update_model_list(self, models):
        """更新模型列表显示"""
        self.model_list.clear()
        
        for model_key, config in models.items():
            item = QListWidgetItem()
            model_name = config.get("model_name", model_key)
            enabled = config.get("enabled", True)
            status = "✓" if enabled else "✗"
            
            item.setText(f"{status} {model_name} ({model_key})")
            item.setData(Qt.ItemDataRole.UserRole, {"key": model_key, "config": config})
            
            self.model_list.addItem(item)

    def on_model_selected(self, item):
        """模型被选中"""
        data = item.data(Qt.ItemDataRole.UserRole)
        self.current_model = data

        # 启用操作按钮
        self.enable_btn.setEnabled(True)
        self.disable_btn.setEnabled(True)
        self.validate_btn.setEnabled(True)
        self.remove_btn.setEnabled(True)

        # 显示模型详情
        self.show_model_details(data["config"])

    def show_model_details(self, config):
        """显示模型详细信息"""
        details = f"模型键名: {config.get('model_key', '')}\n"
        details += f"模型名称: {config.get('model_name', '')}\n"
        details += f"模型路径: {config.get('model_path', '')}\n"
        details += f"模型类型: {config.get('model_type', '')}\n"
        details += f"状态: {'启用' if config.get('enabled', True) else '禁用'}\n"
        details += f"是否本地: {'是' if config.get('is_local', False) else '否'}\n"

        if config.get('description'):
            details += f"描述: {config.get('description')}\n"

        details += f"CFG范围: {config.get('cfg_scale_range', [])}\n"
        details += f"步数范围: {config.get('steps_range', [])}\n"
        details += f"目标大小偏好: {config.get('target_size_bias', '')}\n"
        details += f"推荐用途: {', '.join(config.get('recommended_for', []))}\n"

        if config.get('added_at'):
            details += f"添加时间: {config.get('added_at')[:19]}\n"
        if config.get('updated_at'):
            details += f"更新时间: {config.get('updated_at')[:19]}\n"

        self.model_details.setPlainText(details)

    def enable_model(self):
        """启用模型"""
        if not self.current_model:
            return

        model_key = self.current_model["key"]
        try:
            result = self.client.enable_custom_model(model_key)
            if result.get("success"):
                QMessageBox.information(self, "成功", "模型启用成功！")
                self.load_models()
            else:
                QMessageBox.warning(self, "失败", f"启用模型失败: {result.get('message')}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"启用模型失败: {str(e)}")

    def disable_model(self):
        """禁用模型"""
        if not self.current_model:
            return

        model_key = self.current_model["key"]
        try:
            result = self.client.disable_custom_model(model_key)
            if result.get("success"):
                QMessageBox.information(self, "成功", "模型禁用成功！")
                self.load_models()
            else:
                QMessageBox.warning(self, "失败", f"禁用模型失败: {result.get('message')}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"禁用模型失败: {str(e)}")

    def validate_model(self):
        """验证模型"""
        if not self.current_model:
            return

        model_key = self.current_model["key"]
        try:
            result = self.client.validate_custom_model(model_key)
            if result.get("success"):
                is_valid = result.get("data", {}).get("is_valid", False)
                if is_valid:
                    QMessageBox.information(self, "验证结果", "✓ 模型验证通过！")
                else:
                    QMessageBox.warning(self, "验证结果", f"✗ 模型验证失败:\n{result.get('message')}")
            else:
                QMessageBox.warning(self, "验证失败", f"验证模型失败: {result.get('message')}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"验证模型失败: {str(e)}")

    def remove_model(self):
        """删除模型"""
        if not self.current_model:
            return

        model_key = self.current_model["key"]
        model_name = self.current_model["config"].get("model_name", model_key)

        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除自定义模型 '{model_name}' 吗？\n\n"
            f"是否同时删除模型文件？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No | QMessageBox.StandardButton.Cancel,
            QMessageBox.StandardButton.Cancel
        )

        if reply == QMessageBox.StandardButton.Cancel:
            return

        delete_files = reply == QMessageBox.StandardButton.Yes

        try:
            result = self.client.remove_custom_model(model_key, delete_files)
            if result.get("success"):
                QMessageBox.information(self, "成功", "模型删除成功！")
                self.load_models()
                self.current_model = None
                self.model_details.clear()

                # 禁用操作按钮
                self.enable_btn.setEnabled(False)
                self.disable_btn.setEnabled(False)
                self.validate_btn.setEnabled(False)
                self.remove_btn.setEnabled(False)
            else:
                QMessageBox.warning(self, "失败", f"删除模型失败: {result.get('message')}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"删除模型失败: {str(e)}")

    def browse_model_path(self):
        """浏览模型路径"""
        path = QFileDialog.getExistingDirectory(self, "选择模型目录")
        if path:
            self.model_path_input.setText(path)

    def add_model(self):
        """添加模型"""
        # 验证输入
        model_key = self.model_key_input.text().strip()
        model_name = self.model_name_input.text().strip()
        model_path = self.model_path_input.text().strip()

        if not model_key or not model_name or not model_path:
            QMessageBox.warning(self, "输入错误", "请填写所有必填字段（模型键名、模型名称、模型路径）")
            return

        # 准备参数
        description = self.description_input.toPlainText().strip()
        model_type = self.model_type_combo.currentText()
        cfg_scale_range = [self.cfg_min_spin.value(), self.cfg_max_spin.value()]
        steps_range = [self.steps_min_spin.value(), self.steps_max_spin.value()]
        target_size_bias = self.target_size_combo.currentText()

        try:
            result = self.client.add_custom_model(
                model_key=model_key,
                model_name=model_name,
                model_path=model_path,
                description=description,
                model_type=model_type,
                cfg_scale_range=cfg_scale_range,
                steps_range=steps_range,
                target_size_bias=target_size_bias,
                recommended_for=["自定义场景"]
            )

            if result.get("success"):
                QMessageBox.information(self, "成功", "自定义模型添加成功！")
                self.load_models()
                self.clear_add_form()
            else:
                QMessageBox.warning(self, "失败", f"添加模型失败: {result.get('message')}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"添加模型失败: {str(e)}")

    def import_from_directory(self):
        """从目录导入模型"""
        source_dir = QFileDialog.getExistingDirectory(self, "选择模型目录")
        if not source_dir:
            return

        model_key, ok1 = QInputDialog.getText(self, "导入模型", "请输入模型键名:")
        if not ok1 or not model_key.strip():
            return

        model_name, ok2 = QInputDialog.getText(self, "导入模型", "请输入模型名称:")
        if not ok2 or not model_name.strip():
            return

        try:
            result = self.client.import_model_from_directory(
                source_dir, model_key.strip(), model_name.strip()
            )

            if result.get("success"):
                QMessageBox.information(self, "成功", "模型导入成功！")
                self.load_models()
            else:
                QMessageBox.warning(self, "失败", f"导入模型失败: {result.get('message')}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导入模型失败: {str(e)}")

    def clear_add_form(self):
        """清空添加表单"""
        self.model_key_input.clear()
        self.model_name_input.clear()
        self.model_path_input.clear()
        self.description_input.clear()
        self.model_type_combo.setCurrentText("diffusers")
        self.cfg_min_spin.setValue(7.0)
        self.cfg_max_spin.setValue(12.0)
        self.steps_min_spin.setValue(20)
        self.steps_max_spin.setValue(50)
        self.target_size_combo.setCurrentText("medium")

    def load_statistics(self):
        """加载统计信息"""
        try:
            result = self.client.get_statistics()
            if result.get("success"):
                data = result.get("data", {})

                stats_text = f"自定义模型统计信息\n"
                stats_text += f"=" * 30 + "\n\n"
                stats_text += f"总模型数量: {data.get('total_models', 0)}\n"
                stats_text += f"启用模型: {data.get('enabled_models', 0)}\n"
                stats_text += f"禁用模型: {data.get('disabled_models', 0)}\n\n"

                stats_text += f"按类型统计:\n"
                type_stats = data.get('type_statistics', {})
                for model_type, count in type_stats.items():
                    stats_text += f"  {model_type}: {count} 个\n"

                stats_text += f"\n存储信息:\n"
                stats_text += f"总大小: {data.get('total_size_mb', 0)} MB\n"
                stats_text += f"存储目录: {data.get('models_directory', '')}\n"

                self.stats_text.setPlainText(stats_text)
            else:
                self.stats_text.setPlainText("无法获取统计信息")
        except Exception as e:
            self.stats_text.setPlainText(f"获取统计信息失败: {str(e)}")

    def closeEvent(self, event):
        """关闭事件"""
        if hasattr(self, 'client'):
            self.client.close()
        event.accept()
