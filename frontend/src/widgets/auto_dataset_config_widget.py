"""
自动数据集配置界面
管理AI生成图片的自动数据集配置
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton,
    QCheckBox, QSpinBox, QComboBox, QGroupBox, QFormLayout, QTextEdit,
    QMessageBox, QFrame, QScrollArea
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont
import requests
import json
import logging

from styles.button_styles import ButtonStyleManager
from styles.dataset_styles import apply_dataset_styles, setup_chinese_font

logger = logging.getLogger(__name__)

class AutoDatasetConfigWidget(QWidget):
    """自动数据集配置界面"""
    
    config_updated = pyqtSignal(dict)  # 配置更新信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.api_base_url = "http://127.0.0.1:8000/api/v1/auto-dataset"
        
        # 设置中文字体
        setup_chinese_font(self)
        
        self.setup_ui()
        self.load_config()
        
        # 定时刷新状态
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.load_status)
        self.status_timer.start(30000)  # 每30秒刷新一次
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("AI生成图片自动数据集管理")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        # 内容容器
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(15)
        
        # 基本配置
        self.create_basic_config_section(content_layout)
        
        # 高级配置
        self.create_advanced_config_section(content_layout)
        
        # 状态信息
        self.create_status_section(content_layout)
        
        # 操作按钮
        self.create_action_buttons(content_layout)
        
        content_layout.addStretch()
        
        scroll_area.setWidget(content_widget)
        layout.addWidget(scroll_area)
    
    def create_basic_config_section(self, layout):
        """创建基本配置部分"""
        basic_group = QGroupBox("基本配置")
        basic_layout = QFormLayout(basic_group)
        
        # 启用自动添加
        self.auto_add_checkbox = QCheckBox("自动添加AI生成的图片到数据集")
        self.auto_add_checkbox.setChecked(True)
        basic_layout.addRow("自动添加:", self.auto_add_checkbox)
        
        # 默认数据集名称
        self.dataset_name_edit = QLineEdit()
        self.dataset_name_edit.setText("AI_Generated_Images")
        self.dataset_name_edit.setPlaceholderText("输入默认数据集名称")
        apply_dataset_styles(self.dataset_name_edit, "form_input")
        basic_layout.addRow("数据集名称:", self.dataset_name_edit)
        
        # 命名格式
        self.naming_format_combo = QComboBox()
        self.naming_format_combo.addItems(["timestamp", "sequential"])
        self.naming_format_combo.setCurrentText("timestamp")
        basic_layout.addRow("命名格式:", self.naming_format_combo)
        
        layout.addWidget(basic_group)
    
    def create_advanced_config_section(self, layout):
        """创建高级配置部分"""
        advanced_group = QGroupBox("高级配置")
        advanced_layout = QFormLayout(advanced_group)
        
        # 按日期创建数据集
        self.daily_datasets_checkbox = QCheckBox("按日期创建数据集")
        self.daily_datasets_checkbox.setToolTip("每天创建一个新的数据集")
        advanced_layout.addRow("日期分组:", self.daily_datasets_checkbox)
        
        # 按军事目标分类
        self.organize_by_target_checkbox = QCheckBox("按军事目标自动分类")
        self.organize_by_target_checkbox.setToolTip("为不同的军事目标创建单独的数据集")
        advanced_layout.addRow("目标分类:", self.organize_by_target_checkbox)
        
        # 每个数据集最大图片数
        self.max_images_spinbox = QSpinBox()
        self.max_images_spinbox.setRange(1, 10000)
        self.max_images_spinbox.setValue(1000)
        self.max_images_spinbox.setSuffix(" 张")
        advanced_layout.addRow("最大图片数:", self.max_images_spinbox)
        
        layout.addWidget(advanced_group)
    
    def create_status_section(self, layout):
        """创建状态信息部分"""
        status_group = QGroupBox("状态信息")
        status_layout = QVBoxLayout(status_group)
        
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(150)
        self.status_text.setReadOnly(True)
        apply_dataset_styles(self.status_text, "detail_text_edit")
        status_layout.addWidget(self.status_text)
        
        layout.addWidget(status_group)
    
    def create_action_buttons(self, layout):
        """创建操作按钮"""
        button_layout = QHBoxLayout()
        
        # 刷新配置
        self.refresh_btn = ButtonStyleManager.create_button("刷新配置", "secondary")
        self.refresh_btn.clicked.connect(self.load_config)
        button_layout.addWidget(self.refresh_btn)
        
        # 保存配置
        self.save_btn = ButtonStyleManager.create_button("保存配置", "primary")
        self.save_btn.clicked.connect(self.save_config)
        button_layout.addWidget(self.save_btn)
        
        # 创建默认数据集
        self.create_default_btn = ButtonStyleManager.create_button("创建默认数据集", "success")
        self.create_default_btn.clicked.connect(self.create_default_dataset)
        button_layout.addWidget(self.create_default_btn)
        
        # 清理旧数据集
        self.cleanup_btn = ButtonStyleManager.create_button("清理旧数据集", "warning")
        self.cleanup_btn.clicked.connect(self.cleanup_old_datasets)
        button_layout.addWidget(self.cleanup_btn)
        
        # 测试功能
        self.test_btn = ButtonStyleManager.create_button("测试功能", "info")
        self.test_btn.clicked.connect(self.test_functionality)
        button_layout.addWidget(self.test_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
    
    def load_config(self):
        """加载配置"""
        try:
            response = requests.get(f"{self.api_base_url}/config", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    config = data.get("data", {})
                    self.update_ui_from_config(config)
                    logger.info("配置加载成功")
                else:
                    QMessageBox.warning(self, "错误", "加载配置失败")
            else:
                QMessageBox.warning(self, "错误", f"请求失败: {response.status_code}")
        except Exception as e:
            logger.error(f"加载配置失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"加载配置失败: {str(e)}")
    
    def update_ui_from_config(self, config):
        """从配置更新UI"""
        self.auto_add_checkbox.setChecked(config.get("auto_add_to_dataset", True))
        self.dataset_name_edit.setText(config.get("default_dataset_name", "AI_Generated_Images"))
        self.naming_format_combo.setCurrentText(config.get("dataset_naming_format", "timestamp"))
        self.daily_datasets_checkbox.setChecked(config.get("create_daily_datasets", False))
        self.organize_by_target_checkbox.setChecked(config.get("auto_organize_by_target", False))
        self.max_images_spinbox.setValue(config.get("max_images_per_dataset", 1000))
    
    def save_config(self):
        """保存配置"""
        try:
            config_data = {
                "auto_add_to_dataset": self.auto_add_checkbox.isChecked(),
                "default_dataset_name": self.dataset_name_edit.text().strip(),
                "dataset_naming_format": self.naming_format_combo.currentText(),
                "create_daily_datasets": self.daily_datasets_checkbox.isChecked(),
                "auto_organize_by_target": self.organize_by_target_checkbox.isChecked(),
                "max_images_per_dataset": self.max_images_spinbox.value()
            }
            
            response = requests.post(
                f"{self.api_base_url}/config",
                json=config_data,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    QMessageBox.information(self, "成功", "配置保存成功！")
                    self.config_updated.emit(config_data)
                    self.load_status()  # 刷新状态
                else:
                    QMessageBox.warning(self, "错误", f"保存失败: {data.get('message')}")
            else:
                QMessageBox.warning(self, "错误", f"请求失败: {response.status_code}")
                
        except Exception as e:
            logger.error(f"保存配置失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"保存配置失败: {str(e)}")
    
    def load_status(self):
        """加载状态信息"""
        try:
            response = requests.get(f"{self.api_base_url}/status", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    status = data.get("data", {})
                    self.update_status_display(status)
        except Exception as e:
            logger.warning(f"加载状态失败: {str(e)}")
    
    def update_status_display(self, status):
        """更新状态显示"""
        status_text = f"🤖 自动添加状态: {'启用' if status.get('enabled') else '禁用'}\n"
        status_text += f"📁 默认数据集: {status.get('default_dataset_name', 'N/A')}\n"
        status_text += f"🏷️ 命名格式: {status.get('naming_format', 'N/A')}\n"
        status_text += f"📊 AI数据集总数: {status.get('total_ai_datasets', 0)} 个\n"
        status_text += f"🖼️ AI图片总数: {status.get('total_ai_images', 0)} 张\n"
        
        latest_dataset = status.get('latest_dataset')
        if latest_dataset:
            status_text += f"📅 最新数据集: {latest_dataset.get('folder_name', 'N/A')}\n"
            status_text += f"   创建时间: {latest_dataset.get('created_at', 'N/A')[:19]}\n"
            status_text += f"   图片数量: {latest_dataset.get('total_images', 0)} 张"
        else:
            status_text += "📅 暂无AI生成数据集"
        
        self.status_text.setPlainText(status_text)
    
    def create_default_dataset(self):
        """创建默认数据集"""
        try:
            response = requests.post(f"{self.api_base_url}/create-default", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    dataset_info = data.get("data", {})
                    QMessageBox.information(
                        self, "成功", 
                        f"默认数据集创建成功！\n\n数据集: {dataset_info.get('dataset_folder')}"
                    )
                    self.load_status()
                else:
                    QMessageBox.warning(self, "错误", f"创建失败: {data.get('message')}")
            else:
                QMessageBox.warning(self, "错误", f"请求失败: {response.status_code}")
        except Exception as e:
            logger.error(f"创建默认数据集失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"创建失败: {str(e)}")
    
    def cleanup_old_datasets(self):
        """清理旧数据集"""
        try:
            reply = QMessageBox.question(
                self, "确认清理",
                "确定要清理旧的AI生成数据集吗？\n\n这将保留最新的10个数据集，删除其余的。",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                response = requests.post(
                    f"{self.api_base_url}/cleanup",
                    json={"max_datasets": 10},
                    timeout=30
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("success"):
                        QMessageBox.information(self, "成功", "数据集清理完成！")
                        self.load_status()
                    else:
                        QMessageBox.warning(self, "错误", f"清理失败: {data.get('message')}")
                else:
                    QMessageBox.warning(self, "错误", f"请求失败: {response.status_code}")
                    
        except Exception as e:
            logger.error(f"清理数据集失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"清理失败: {str(e)}")
    
    def test_functionality(self):
        """测试功能"""
        try:
            response = requests.post(f"{self.api_base_url}/test", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    QMessageBox.information(self, "测试成功", "自动数据集管理功能测试通过！")
                else:
                    QMessageBox.warning(self, "测试失败", f"测试失败: {data.get('message')}")
            else:
                QMessageBox.warning(self, "错误", f"请求失败: {response.status_code}")
        except Exception as e:
            logger.error(f"测试功能失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"测试失败: {str(e)}")
