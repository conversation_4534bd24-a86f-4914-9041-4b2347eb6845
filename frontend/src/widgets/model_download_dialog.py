"""
模型下载管理对话框
"""

import os
import webbrowser
from pathlib import Path
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTextEdit, QGroupBox, QProgressBar, QMessageBox, QScrollArea,
    QWidget, QFrame, QApplication
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QPixmap, QIcon
from styles.button_styles import ButtonStyleManager

import logging

logger = logging.getLogger(__name__)


class ModelStatusWorker(QThread):
    """模型状态检查工作线程"""
    status_updated = pyqtSignal(dict)
    error = pyqtSignal(str)

    def __init__(self, ai_client, model_key):
        super().__init__()
        self.ai_client = ai_client
        self.model_key = model_key

    def run(self):
        try:
            # 获取模型下载状态
            status = self.ai_client.get_model_download_status(self.model_key)
            
            # 获取模型下载信息
            download_info = self.ai_client.get_model_download_info(self.model_key)
            
            # 合并信息
            result = {
                "status": status,
                "download_info": download_info
            }
            
            self.status_updated.emit(result)
            
        except Exception as e:
            self.error.emit(str(e))


class ModelSetupWorker(QThread):
    """模型设置工作线程"""
    progress = pyqtSignal(str)
    finished = pyqtSignal(dict)
    error = pyqtSignal(str)

    def __init__(self, ai_client, model_key):
        super().__init__()
        self.ai_client = ai_client
        self.model_key = model_key

    def run(self):
        try:
            self.progress.emit("正在创建模型配置文件...")
            result = self.ai_client.setup_model_offline(self.model_key)
            self.finished.emit(result)
            
        except Exception as e:
            self.error.emit(str(e))


class ModelDownloadDialog(QDialog):
    """模型下载管理对话框"""
    
    def __init__(self, parent=None, ai_client=None, model_key=None):
        super().__init__(parent)
        self.ai_client = ai_client
        self.model_key = model_key
        self.model_status = {}
        self.download_info = {}
        self.connection_error_count = 0  # 连接错误计数器

        self.setWindowTitle(f"模型下载管理 - {model_key}")
        self.setModal(True)
        self.resize(700, 600)

        self.setup_ui()
        self.load_model_status()

        # 定时刷新状态
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_status)
        self.refresh_timer.start(10000)  # 每10秒刷新一次，减少频率

    def setup_ui(self):
        """设置UI界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel(f"模型管理: {self.model_key}")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # 状态信息组
        self.status_group = QGroupBox("模型状态")
        self.status_layout = QVBoxLayout(self.status_group)
        
        self.status_label = QLabel("正在检查模型状态...")
        self.status_label.setWordWrap(True)
        self.status_layout.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_layout.addWidget(self.progress_bar)
        
        scroll_layout.addWidget(self.status_group)
        
        # 下载信息组
        self.download_group = QGroupBox("下载信息")
        self.download_layout = QVBoxLayout(self.download_group)
        
        self.download_info_text = QTextEdit()
        self.download_info_text.setMaximumHeight(200)
        self.download_info_text.setReadOnly(True)
        self.download_layout.addWidget(self.download_info_text)
        
        scroll_layout.addWidget(self.download_group)
        
        # 操作按钮组
        self.actions_group = QGroupBox("操作")
        self.actions_layout = QVBoxLayout(self.actions_group)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        
        self.refresh_btn = ButtonStyleManager.create_button("刷新状态", "secondary")
        self.refresh_btn.clicked.connect(self.refresh_status)
        button_layout.addWidget(self.refresh_btn)

        self.open_folder_btn = ButtonStyleManager.create_button("打开模型目录", "primary")
        self.open_folder_btn.clicked.connect(self.open_model_folder)
        button_layout.addWidget(self.open_folder_btn)

        self.setup_btn = ButtonStyleManager.create_button("设置离线配置", "secondary")
        self.setup_btn.clicked.connect(self.setup_offline_config)
        self.setup_btn.setEnabled(False)
        button_layout.addWidget(self.setup_btn)
        
        button_layout.addStretch()
        self.actions_layout.addLayout(button_layout)
        
        # 下载链接按钮
        self.download_links_layout = QVBoxLayout()
        self.actions_layout.addLayout(self.download_links_layout)
        
        scroll_layout.addWidget(self.actions_group)
        
        # 设置滚动区域
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)
        
        # 底部按钮
        bottom_layout = QHBoxLayout()
        bottom_layout.addStretch()
        
        self.close_btn = ButtonStyleManager.create_button("关闭", "secondary", min_width=70)
        self.close_btn.clicked.connect(self.accept)
        bottom_layout.addWidget(self.close_btn)
        
        layout.addLayout(bottom_layout)

    def load_model_status(self):
        """加载模型状态"""
        if not self.ai_client:
            self.status_label.setText("❌ AI服务客户端未初始化")
            return
        
        self.status_label.setText("🔄 正在检查模型状态...")
        self.refresh_btn.setEnabled(False)
        
        # 启动状态检查工作线程
        self.status_worker = ModelStatusWorker(self.ai_client, self.model_key)
        self.status_worker.status_updated.connect(self.on_status_updated)
        self.status_worker.error.connect(self.on_status_error)
        self.status_worker.start()

    def on_status_updated(self, result):
        """状态更新完成"""
        self.refresh_btn.setEnabled(True)

        # 重置连接错误计数器
        self.connection_error_count = 0

        self.model_status = result.get("status", {})
        self.download_info = result.get("download_info", {})

        self.update_status_display()
        self.update_download_info_display()
        self.update_action_buttons()

    def on_status_error(self, error_message):
        """状态检查错误"""
        self.refresh_btn.setEnabled(True)

        # 如果是连接错误，增加错误计数
        if "连接" in error_message or "Connection" in error_message:
            self.connection_error_count += 1

            # 连续3次连接失败后停止定时器
            if self.connection_error_count >= 3:
                if hasattr(self, 'refresh_timer'):
                    self.refresh_timer.stop()
                self.status_label.setText(f"❌ 无法连接到后端服务\n请确保后端服务正在运行\n\n点击'刷新状态'按钮手动重试")
            else:
                self.status_label.setText(f"❌ 连接失败 ({self.connection_error_count}/3)\n正在重试...")
        else:
            self.status_label.setText(f"❌ 检查状态失败: {error_message}")

    def update_status_display(self):
        """更新状态显示"""
        local_exists = self.model_status.get("local_exists", False)
        files_found = self.model_status.get("files_found", [])
        download_required = self.model_status.get("download_required", True)
        setup_required = self.model_status.get("setup_required", False)
        
        status_text = ""
        
        if local_exists:
            status_text += "✅ 本地模型文件已存在\n"
            status_text += f"📁 找到文件: {', '.join(files_found)}\n"
            
            if setup_required:
                status_text += "⚠️ 需要设置离线配置文件\n"
                missing_configs = self.model_status.get("missing_configs", [])
                if missing_configs:
                    status_text += f"缺失配置: {', '.join(missing_configs)}\n"
            else:
                status_text += "✅ 配置文件完整，模型可以使用\n"
        else:
            status_text += "❌ 本地模型文件不存在\n"
            if download_required:
                status_text += "📥 需要手动下载模型文件\n"
        
        self.status_label.setText(status_text)

    def update_download_info_display(self):
        """更新下载信息显示"""
        if not self.download_info:
            self.download_info_text.setText("暂无下载信息")
            return
        
        info_text = ""
        
        # 模型基本信息
        model_name = self.download_info.get("model_name", "未知模型")
        description = self.download_info.get("description", "")
        
        info_text += f"模型名称: {model_name}\n"
        if description:
            info_text += f"描述: {description}\n"
        
        # 文件信息
        file_info = self.download_info.get("file_info", {})
        if file_info:
            info_text += "\n文件信息:\n"
            main_file = file_info.get("main_file")
            if main_file:
                file_size = file_info.get("file_size", "未知大小")
                info_text += f"• 主文件: {main_file} ({file_size})\n"
            
            alt_file = file_info.get("alternative_file")
            if alt_file:
                alt_size = file_info.get("alternative_size", "未知大小")
                info_text += f"• 备选文件: {alt_file} ({alt_size})\n"
            
            note = file_info.get("note")
            if note:
                info_text += f"• 说明: {note}\n"
        
        # 本地路径
        local_path = self.download_info.get("local_path")
        if local_path:
            info_text += f"\n本地路径: {local_path}\n"
        
        # 设置说明
        setup_instructions = self.download_info.get("setup_instructions", [])
        if setup_instructions:
            info_text += "\n设置步骤:\n"
            for i, instruction in enumerate(setup_instructions, 1):
                info_text += f"{instruction}\n"
        
        self.download_info_text.setText(info_text)

    def update_action_buttons(self):
        """更新操作按钮状态"""
        local_exists = self.model_status.get("local_exists", False)
        setup_required = self.model_status.get("setup_required", False)
        
        # 设置离线配置按钮
        self.setup_btn.setEnabled(local_exists and setup_required)
        
        # 清除之前的下载链接按钮
        for i in reversed(range(self.download_links_layout.count())):
            child = self.download_links_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
        
        # 添加下载链接按钮
        download_sources = self.download_info.get("download_sources", [])
        if download_sources:
            links_label = QLabel("下载链接:")
            links_label.setStyleSheet("font-weight: bold; margin-top: 10px;")
            self.download_links_layout.addWidget(links_label)
            
            for source in download_sources:
                name = source.get("name", "未知来源")
                url = source.get("url", "")
                instructions = source.get("instructions", "")
                
                if url:
                    btn = ButtonStyleManager.create_button(f"打开 {name}", "small")
                    btn.setToolTip(f"{instructions}\n点击打开: {url}")
                    btn.clicked.connect(lambda checked, u=url: webbrowser.open(u))
                    self.download_links_layout.addWidget(btn)

    def refresh_status(self):
        """刷新状态"""
        # 重置错误计数器并重新启动定时器
        self.connection_error_count = 0
        if hasattr(self, 'refresh_timer') and not self.refresh_timer.isActive():
            self.refresh_timer.start(10000)

        self.load_model_status()

    def open_model_folder(self):
        """打开模型目录"""
        model_dir = Path("models") / self.model_key
        
        # 确保目录存在
        model_dir.mkdir(parents=True, exist_ok=True)
        
        # 打开目录
        if os.name == 'nt':  # Windows
            os.startfile(str(model_dir))
        elif os.name == 'posix':  # macOS and Linux
            os.system(f'open "{model_dir}"' if os.uname().sysname == 'Darwin' else f'xdg-open "{model_dir}"')

    def setup_offline_config(self):
        """设置离线配置"""
        reply = QMessageBox.question(
            self, "确认设置", 
            f"确定要为模型 {self.model_key} 创建离线配置文件吗？\n\n"
            "这将创建必要的配置文件以支持离线加载。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.Yes
        )
        
        if reply != QMessageBox.StandardButton.Yes:
            return
        
        # 禁用按钮并显示进度
        self.setup_btn.setEnabled(False)
        self.setup_btn.setText("设置中...")
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不确定进度
        
        # 启动设置工作线程
        self.setup_worker = ModelSetupWorker(self.ai_client, self.model_key)
        self.setup_worker.progress.connect(self.on_setup_progress)
        self.setup_worker.finished.connect(self.on_setup_finished)
        self.setup_worker.error.connect(self.on_setup_error)
        self.setup_worker.start()

    def on_setup_progress(self, message):
        """设置进度更新"""
        self.status_label.setText(f"🔄 {message}")

    def on_setup_finished(self, result):
        """设置完成"""
        self.progress_bar.setVisible(False)
        self.setup_btn.setText("设置离线配置")
        
        if result.get("success"):
            created_files = result.get("created_files", [])
            model_dir = result.get("model_dir", "")
            
            QMessageBox.information(
                self, "设置成功", 
                f"离线配置设置成功！\n\n"
                f"模型目录: {model_dir}\n"
                f"创建了 {len(created_files)} 个配置文件\n\n"
                "现在可以尝试加载模型了。"
            )
            
            # 刷新状态
            self.refresh_status()
        else:
            error_msg = result.get("message", "设置失败")
            QMessageBox.critical(self, "设置失败", f"离线配置设置失败:\n{error_msg}")
            self.setup_btn.setEnabled(True)

    def on_setup_error(self, error_message):
        """设置错误"""
        self.progress_bar.setVisible(False)
        self.setup_btn.setText("设置离线配置")
        self.setup_btn.setEnabled(True)
        
        QMessageBox.critical(self, "设置失败", f"离线配置设置失败:\n{error_message}")

    def closeEvent(self, event):
        """关闭事件"""
        # 停止定时器
        if hasattr(self, 'refresh_timer'):
            self.refresh_timer.stop()
        
        # 停止工作线程
        if hasattr(self, 'status_worker') and self.status_worker.isRunning():
            self.status_worker.terminate()
            self.status_worker.wait()
        
        if hasattr(self, 'setup_worker') and self.setup_worker.isRunning():
            self.setup_worker.terminate()
            self.setup_worker.wait()
        
        event.accept() 