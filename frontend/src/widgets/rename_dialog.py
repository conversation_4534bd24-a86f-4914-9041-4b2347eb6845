"""
重命名对话框
支持数据集文件夹和图片文件的重命名
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton,
    QGroupBox, QTextEdit, QFormLayout, QMessageBox, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont
import re
import os

from styles.button_styles import ButtonStyleManager
import logging

logger = logging.getLogger(__name__)

class RenameDialog(QDialog):
    """重命名对话框"""
    
    rename_confirmed = pyqtSignal(str)  # 发送新名称
    
    def __init__(self, parent=None, rename_type="dataset", current_name="", item_info=None):
        super().__init__(parent)
        self.rename_type = rename_type  # "dataset" 或 "image"
        self.current_name = current_name
        self.item_info = item_info or {}
        
        self.setWindowTitle(f"重命名{'数据集' if rename_type == 'dataset' else '图片'}")
        self.setModal(True)
        self.setFixedSize(450, 300)
        
        # 设置中文字体
        font = QFont()
        font.setFamily("Microsoft YaHei")
        font.setPointSize(9)
        self.setFont(font)
        
        self.setup_ui()
        self._update_preview()
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title_text = f"重命名{'数据集' if self.rename_type == 'dataset' else '图片'}"
        title_label = QLabel(title_text)
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 当前信息
        current_group = QGroupBox("当前信息")
        current_layout = QFormLayout(current_group)
        
        current_name_label = QLabel(self.current_name)
        current_name_label.setStyleSheet("font-weight: bold; color: #333;")
        current_layout.addRow("当前名称:", current_name_label)
        
        if self.rename_type == "dataset":
            total_images = self.item_info.get("total_images", 0)
            created_at = self.item_info.get("created_at", "")[:10]
            current_layout.addRow("图片数量:", QLabel(f"{total_images} 张"))
            current_layout.addRow("创建时间:", QLabel(created_at))
        else:
            file_size = self.item_info.get("file_size", self.item_info.get("size", 0))
            size_mb = round(file_size / (1024 * 1024), 2) if file_size > 0 else 0
            image_info = self.item_info.get("image_info", {})
            width = image_info.get("width", self.item_info.get("width", 0))
            height = image_info.get("height", self.item_info.get("height", 0))
            current_layout.addRow("文件大小:", QLabel(f"{size_mb} MB"))
            current_layout.addRow("图片尺寸:", QLabel(f"{width} x {height}"))
        
        layout.addWidget(current_group)
        
        # 新名称输入
        name_group = QGroupBox("新名称")
        name_layout = QFormLayout(name_group)
        
        self.name_edit = QLineEdit()
        if self.rename_type == "dataset":
            # 对于数据集，提取原有的名称部分（去掉时间戳或序号前缀）
            if "_" in self.current_name:
                parts = self.current_name.split("_", 1)
                if len(parts) > 1 and (parts[0].isdigit() or self._is_timestamp(parts[0])):
                    self.name_edit.setText(parts[1])
                else:
                    self.name_edit.setText(self.current_name)
            else:
                self.name_edit.setText("")
            self.name_edit.setPlaceholderText("输入新的数据集名称")
        else:
            # 对于图片，去掉扩展名
            name_without_ext = os.path.splitext(self.current_name)[0]
            self.name_edit.setText(name_without_ext)
            self.name_edit.setPlaceholderText("输入新的文件名（不含扩展名）")
        
        self.name_edit.textChanged.connect(self._update_preview)
        self.name_edit.textChanged.connect(self._validate_input)
        name_layout.addRow("新名称:", self.name_edit)
        
        layout.addWidget(name_group)
        
        # 预览
        preview_group = QGroupBox("预览")
        preview_layout = QVBoxLayout(preview_group)
        
        self.preview_label = QLabel()
        self.preview_label.setStyleSheet("""
            QLabel {
                background-color: #f0f0f0;
                border: 1px solid #ccc;
                padding: 8px;
                border-radius: 4px;
                font-family: 'Consolas', 'Monaco', monospace;
                color: #333;
            }
        """)
        self.preview_label.setWordWrap(True)
        preview_layout.addWidget(self.preview_label)
        
        layout.addWidget(preview_group)
        
        # 验证信息
        self.validation_label = QLabel()
        self.validation_label.setStyleSheet("color: #dc3545; font-size: 10px;")
        self.validation_label.setWordWrap(True)
        layout.addWidget(self.validation_label)
        
        # 按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.cancel_btn = ButtonStyleManager.create_button("取消", "secondary")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        self.rename_btn = ButtonStyleManager.create_button("重命名", "primary")
        self.rename_btn.clicked.connect(self._confirm_rename)
        button_layout.addWidget(self.rename_btn)
        
        layout.addLayout(button_layout)
    
    def _is_timestamp(self, text):
        """检查是否是时间戳格式"""
        return bool(re.match(r'^\d{8}_\d{6}$', text))
    
    def _update_preview(self):
        """更新预览"""
        new_name = self.name_edit.text().strip()
        
        if self.rename_type == "dataset":
            if new_name:
                # 保持原有的时间戳或序号前缀
                if "_" in self.current_name:
                    parts = self.current_name.split("_", 1)
                    if len(parts) > 1 and (parts[0].isdigit() or self._is_timestamp(parts[0])):
                        preview = f"{parts[0]}_{new_name}"
                    else:
                        preview = new_name
                else:
                    preview = new_name
            else:
                preview = "请输入新名称"
        else:
            if new_name:
                # 保持原有的扩展名
                ext = os.path.splitext(self.current_name)[1]
                preview = f"{new_name}{ext}"
            else:
                preview = "请输入新名称"
        
        self.preview_label.setText(f"新名称: {preview}")
    
    def _validate_input(self):
        """验证输入"""
        new_name = self.name_edit.text().strip()
        
        if not new_name:
            self.validation_label.setText("❌ 名称不能为空")
            self.rename_btn.setEnabled(False)
            return False
        
        if len(new_name) > 50:
            self.validation_label.setText("❌ 名称不能超过50个字符")
            self.rename_btn.setEnabled(False)
            return False
        
        # 检查非法字符
        if self.rename_type == "dataset":
            if not all(c.isalnum() or c in (' ', '-', '_', '中') or '\u4e00' <= c <= '\u9fff' for c in new_name):
                self.validation_label.setText("❌ 名称只能包含字母、数字、中文、空格、连字符和下划线")
                self.rename_btn.setEnabled(False)
                return False
        else:
            # 图片文件名的限制更严格
            invalid_chars = '<>:"/\\|?*'
            if any(c in invalid_chars for c in new_name):
                self.validation_label.setText(f"❌ 文件名不能包含以下字符: {invalid_chars}")
                self.rename_btn.setEnabled(False)
                return False
        
        # 检查是否与当前名称相同
        if self.rename_type == "dataset":
            current_name_part = self.current_name
            if "_" in self.current_name:
                parts = self.current_name.split("_", 1)
                if len(parts) > 1 and (parts[0].isdigit() or self._is_timestamp(parts[0])):
                    current_name_part = parts[1]
            
            if new_name == current_name_part:
                self.validation_label.setText("❌ 新名称与当前名称相同")
                self.rename_btn.setEnabled(False)
                return False
        else:
            current_name_without_ext = os.path.splitext(self.current_name)[0]
            if new_name == current_name_without_ext:
                self.validation_label.setText("❌ 新名称与当前名称相同")
                self.rename_btn.setEnabled(False)
                return False
        
        self.validation_label.setText("✅ 名称有效")
        self.validation_label.setStyleSheet("color: #28a745; font-size: 10px;")
        self.rename_btn.setEnabled(True)
        return True
    
    def _confirm_rename(self):
        """确认重命名"""
        if not self._validate_input():
            return
        
        new_name = self.name_edit.text().strip()
        
        # 对于图片，需要添加扩展名
        if self.rename_type == "image":
            ext = os.path.splitext(self.current_name)[1]
            new_name = f"{new_name}{ext}"
        
        self.rename_confirmed.emit(new_name)
        self.accept()
    
    def get_new_name(self):
        """获取新名称"""
        new_name = self.name_edit.text().strip()
        
        if self.rename_type == "image":
            ext = os.path.splitext(self.current_name)[1]
            return f"{new_name}{ext}"
        
        return new_name
