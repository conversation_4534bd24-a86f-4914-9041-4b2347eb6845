"""
数据集管理界面组件 - 文件系统存储方案
"""

import os
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QListWidget,
    QListWidgetItem, QMessageBox, QInputDialog, QFileDialog, QGroupBox,
    QTextEdit, QSplitter, QFrame, QProgressBar, QComboBox, QDialog, QCheckBox
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QColor

from services.dataset_management_client import DatasetManagementClient
from services.ai_service import AIServiceClient
from services.sd_model_service import SDModelService
from widgets.dataset_create_dialog import DatasetCreateDialog
from widgets.image_detail_widget import ImageDetailWidget
from widgets.rename_dialog import RenameDialog
from widgets.delete_confirm_dialog import DeleteConfirmDialog
from widgets.auto_dataset_config_widget import AutoDatasetConfigWidget
from styles.button_styles import ButtonStyleManager
from styles.dataset_styles import apply_dataset_styles, get_status_style, setup_chinese_font
import logging

logger = logging.getLogger(__name__)

class DatasetListWorker(QThread):
    """数据集列表加载工作线程"""
    finished = pyqtSignal(dict)
    error = pyqtSignal(str)
    
    def __init__(self, client):
        super().__init__()
        self.client = client
    
    def run(self):
        try:
            result = self.client.list_datasets()
            self.finished.emit(result)
        except Exception as e:
            self.error.emit(str(e))

class ImageListWorker(QThread):
    """图片列表加载工作线程"""
    finished = pyqtSignal(dict)
    error = pyqtSignal(str)

    def __init__(self, client, folder_name, include_annotated=True):
        super().__init__()
        self.client = client
        self.folder_name = folder_name
        self.include_annotated = include_annotated

    def run(self):
        try:
            result = self.client.list_images_in_dataset(self.folder_name, self.include_annotated)
            self.finished.emit(result)
        except Exception as e:
            self.error.emit(str(e))

class DatasetManagementWidget(QWidget):
    """数据集管理界面组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.client = DatasetManagementClient()
        self.ai_client = AIServiceClient()
        self.sd_model_service = SDModelService()
        self.current_dataset = None

        # 设置中文字体
        setup_chinese_font(self)

        self.setup_ui()
        self.load_datasets()
        self.refresh_model_info()  # 加载模型信息

        # 定时刷新
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.load_datasets)
        self.refresh_timer.start(30000)  # 每30秒刷新一次

        # 模型信息刷新定时器
        self.model_refresh_timer = QTimer()
        self.model_refresh_timer.timeout.connect(self.refresh_model_info)
        self.model_refresh_timer.start(60000)  # 每60秒刷新一次模型信息
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)

        # 创建主分割器
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # 左侧：数据集列表
        left_panel = self.create_dataset_panel()
        main_splitter.addWidget(left_panel)

        # 中间：图片列表
        middle_panel = self.create_image_panel()
        main_splitter.addWidget(middle_panel)

        # 右侧：图片详情
        right_panel = self.create_detail_panel()
        main_splitter.addWidget(right_panel)

        # 设置分割比例 (数据集:图片:详情 = 1:2:2)
        main_splitter.setStretchFactor(0, 1)
        main_splitter.setStretchFactor(1, 2)
        main_splitter.setStretchFactor(2, 2)

        layout.addWidget(main_splitter)
    
    def create_dataset_panel(self):
        """创建数据集面板"""
        panel = QFrame()
        layout = QVBoxLayout(panel)
        
        # 标题
        title_label = QLabel("数据集管理")
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.create_dataset_btn = ButtonStyleManager.create_button("新建数据集", "primary")
        self.create_dataset_btn.clicked.connect(self.create_dataset)
        button_layout.addWidget(self.create_dataset_btn)
        
        self.refresh_btn = ButtonStyleManager.create_button("刷新", "secondary")
        self.refresh_btn.clicked.connect(self.load_datasets)
        button_layout.addWidget(self.refresh_btn)

        # AI自动管理配置按钮
        self.auto_config_btn = ButtonStyleManager.create_button("AI自动管理", "info")
        self.auto_config_btn.clicked.connect(self.open_auto_config)
        self.auto_config_btn.setToolTip("配置AI生成图片的自动数据集管理")
        button_layout.addWidget(self.auto_config_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # 数据集列表
        self.dataset_list = QListWidget()
        self.dataset_list.itemClicked.connect(self.on_dataset_selected)
        apply_dataset_styles(self.dataset_list, "list_widget")
        layout.addWidget(self.dataset_list)
        
        # 数据集操作按钮
        dataset_ops_layout = QHBoxLayout()
        
        self.rename_dataset_btn = ButtonStyleManager.create_button("重命名", "secondary")
        self.rename_dataset_btn.clicked.connect(self.rename_dataset)
        self.rename_dataset_btn.setEnabled(False)
        dataset_ops_layout.addWidget(self.rename_dataset_btn)
        
        self.delete_dataset_btn = ButtonStyleManager.create_button("删除", "danger")
        self.delete_dataset_btn.clicked.connect(self.delete_dataset)
        self.delete_dataset_btn.setEnabled(False)
        dataset_ops_layout.addWidget(self.delete_dataset_btn)
        
        dataset_ops_layout.addStretch()
        layout.addLayout(dataset_ops_layout)

        # 模型信息组
        model_group = QGroupBox("🤖 当前模型信息")
        model_layout = QVBoxLayout(model_group)

        self.current_model_label = QLabel("模型: 未加载")
        self.current_model_label.setStyleSheet("color: #666; font-size: 11px;")
        model_layout.addWidget(self.current_model_label)

        self.model_type_label = QLabel("类型: 未知")
        self.model_type_label.setStyleSheet("color: #666; font-size: 11px;")
        model_layout.addWidget(self.model_type_label)

        # 模型管理按钮
        model_btn_layout = QHBoxLayout()

        self.refresh_model_btn = ButtonStyleManager.create_button("刷新模型", "small")
        self.refresh_model_btn.clicked.connect(self.refresh_model_info)
        model_btn_layout.addWidget(self.refresh_model_btn)

        model_btn_layout.addStretch()
        model_layout.addLayout(model_btn_layout)

        layout.addWidget(model_group)

        # 统计信息
        self.stats_label = QLabel("📊 统计信息加载中...")
        apply_dataset_styles(self.stats_label, "stats_label")
        layout.addWidget(self.stats_label)

        # 状态指示器
        self.status_label = QLabel("🔄 准备就绪")
        self.status_label.setStyleSheet(get_status_style("info"))
        layout.addWidget(self.status_label)
        
        return panel
    
    def create_image_panel(self):
        """创建图片面板"""
        panel = QFrame()
        layout = QVBoxLayout(panel)
        
        # 标题
        self.image_title_label = QLabel("请选择数据集")
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        self.image_title_label.setFont(title_font)
        layout.addWidget(self.image_title_label)
        
        # 图片操作按钮
        image_ops_layout = QHBoxLayout()
        
        self.upload_image_btn = ButtonStyleManager.create_button("上传图片", "primary")
        self.upload_image_btn.clicked.connect(self.upload_image)
        self.upload_image_btn.setEnabled(False)
        image_ops_layout.addWidget(self.upload_image_btn)
        
        self.refresh_images_btn = ButtonStyleManager.create_button("刷新图片", "secondary")
        self.refresh_images_btn.clicked.connect(self.load_images)
        self.refresh_images_btn.setEnabled(False)
        image_ops_layout.addWidget(self.refresh_images_btn)

        # 添加标注图片显示选项
        self.show_annotated_checkbox = QCheckBox("显示标注图片")
        self.show_annotated_checkbox.setChecked(True)
        self.show_annotated_checkbox.stateChanged.connect(self.load_images)
        self.show_annotated_checkbox.setToolTip("是否在图片列表中显示标注图片")
        image_ops_layout.addWidget(self.show_annotated_checkbox)

        image_ops_layout.addStretch()
        layout.addLayout(image_ops_layout)
        
        # 图片列表
        self.image_list = QListWidget()
        self.image_list.itemClicked.connect(self.on_image_selected)
        apply_dataset_styles(self.image_list, "list_widget")
        layout.addWidget(self.image_list)
        
        # 图片操作按钮
        image_item_ops_layout = QHBoxLayout()
        
        self.rename_image_btn = ButtonStyleManager.create_button("重命名图片", "secondary")
        self.rename_image_btn.clicked.connect(self.rename_image)
        self.rename_image_btn.setEnabled(False)
        image_item_ops_layout.addWidget(self.rename_image_btn)
        
        self.delete_image_btn = ButtonStyleManager.create_button("删除图片", "danger")
        self.delete_image_btn.clicked.connect(self.delete_image)
        self.delete_image_btn.setEnabled(False)
        image_item_ops_layout.addWidget(self.delete_image_btn)
        
        image_item_ops_layout.addStretch()
        layout.addLayout(image_item_ops_layout)
        
        return panel

    def create_detail_panel(self):
        """创建详情面板"""
        panel = QFrame()
        layout = QVBoxLayout(panel)

        # 标题
        title_label = QLabel("图片详情")
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)

        # 图片详情组件
        self.image_detail_widget = ImageDetailWidget()
        layout.addWidget(self.image_detail_widget)

        return panel
    
    def load_datasets(self):
        """加载数据集列表"""
        self.refresh_btn.setEnabled(False)
        self.refresh_btn.setText("加载中...")
        self._update_status("🔄 正在加载数据集列表...", "loading")

        self.dataset_worker = DatasetListWorker(self.client)
        self.dataset_worker.finished.connect(self.on_datasets_loaded)
        self.dataset_worker.error.connect(self.on_datasets_error)
        self.dataset_worker.start()
    
    def on_datasets_loaded(self, result):
        """数据集列表加载完成"""
        self.refresh_btn.setEnabled(True)
        self.refresh_btn.setText("刷新")

        if result.get("success"):
            datasets = result.get("data", {}).get("datasets", [])
            self.update_dataset_list(datasets)
            self._update_status(f"✅ 成功加载 {len(datasets)} 个数据集", "success")

            # 更新统计信息
            self.load_statistics()
        else:
            self._update_status("❌ 加载数据集失败", "error")
            QMessageBox.warning(self, "错误", f"加载数据集失败: {result.get('message')}")
    
    def on_datasets_error(self, error_message):
        """数据集列表加载错误"""
        self.refresh_btn.setEnabled(True)
        self.refresh_btn.setText("刷新")
        self.stats_label.setText("❌ 无法连接到后端服务")
        self._update_status("❌ 连接后端服务失败", "error")
        QMessageBox.critical(self, "错误", f"加载数据集失败: {error_message}")
    
    def update_dataset_list(self, datasets):
        """更新数据集列表显示"""
        self.dataset_list.clear()

        for dataset in datasets:
            item = QListWidgetItem()
            folder_name = dataset.get("folder_name", "")
            total_images = dataset.get("total_images", 0)
            created_at = dataset.get("created_at", "")[:10]  # 只显示日期部分
            naming_format = dataset.get("naming_format", "timestamp")
            description = dataset.get("description", "")
            dataset_type = dataset.get("dataset_type", "unknown")

            # 根据数据集类型添加图标
            type_icon = self._get_dataset_type_icon(dataset_type)

            # 格式化显示文本
            display_text = f"{type_icon} {folder_name}\n"
            display_text += f"🖼️ {total_images} 张图片 | 📅 {created_at}\n"

            # 添加命名格式标识
            format_icon = "🕐" if naming_format == "timestamp" else "🔢"
            format_text = "时间戳" if naming_format == "timestamp" else "序号"
            display_text += f"{format_icon} {format_text}格式"

            # 如果有描述，添加描述
            if description and description != "自动生成的数据集":
                display_text += f"\n💬 {description[:30]}{'...' if len(description) > 30 else ''}"

            item.setText(display_text)
            item.setData(Qt.ItemDataRole.UserRole, dataset)

            # 设置工具提示
            tooltip = f"文件夹: {folder_name}\n"
            tooltip += f"图片数量: {total_images}\n"
            tooltip += f"创建时间: {created_at}\n"
            tooltip += f"命名格式: {format_text}\n"
            tooltip += f"数据集类型: {self._get_dataset_type_text(dataset_type)}\n"
            if description:
                tooltip += f"描述: {description}"
            item.setToolTip(tooltip)

            # 根据数据集类型设置特殊样式
            self._set_dataset_item_style(item, dataset_type)

            self.dataset_list.addItem(item)

    def _get_dataset_type_icon(self, dataset_type: str) -> str:
        """根据数据集类型获取图标"""
        icons = {
            "traditional": "🎨",  # 传统合成
            "ai": "🤖",          # AI生成
            "mixed": "🔀",       # 混合类型
            "manual": "📁",      # 手动上传
            "unknown": "❓"      # 未知类型
        }
        return icons.get(dataset_type, "📁")

    def _get_dataset_type_text(self, dataset_type: str) -> str:
        """根据数据集类型获取文本描述"""
        texts = {
            "traditional": "传统图片合成",
            "ai": "AI生成图片",
            "mixed": "混合类型",
            "manual": "手动上传",
            "unknown": "未知类型"
        }
        return texts.get(dataset_type, "未知类型")

    def _set_dataset_item_style(self, item, dataset_type: str):
        """根据数据集类型设置列表项样式"""
        if dataset_type == "traditional":
            # 传统图片数据集 - 橙色背景
            item.setBackground(QColor(255, 140, 0, 30))  # 淡橙色背景
            item.setForeground(QColor(255, 255, 255))    # 白色文字
        elif dataset_type == "ai":
            # AI生成数据集 - 蓝色背景
            item.setBackground(QColor(0, 120, 215, 30))  # 淡蓝色背景
            item.setForeground(QColor(255, 255, 255))    # 白色文字
        elif dataset_type == "mixed":
            # 混合数据集 - 紫色背景
            item.setBackground(QColor(128, 0, 128, 30))  # 淡紫色背景
            item.setForeground(QColor(255, 255, 255))    # 白色文字
        elif dataset_type == "manual":
            # 手动上传数据集 - 绿色背景
            item.setBackground(QColor(34, 139, 34, 30))  # 淡绿色背景
            item.setForeground(QColor(255, 255, 255))    # 白色文字
        # unknown类型使用默认样式

    def on_dataset_selected(self, item):
        """数据集被选中"""
        dataset = item.data(Qt.ItemDataRole.UserRole)
        self.current_dataset = dataset
        
        # 启用数据集操作按钮
        self.rename_dataset_btn.setEnabled(True)
        self.delete_dataset_btn.setEnabled(True)
        
        # 启用图片操作按钮
        self.upload_image_btn.setEnabled(True)
        self.refresh_images_btn.setEnabled(True)
        
        # 更新图片面板标题
        folder_name = dataset.get("folder_name", "")
        self.image_title_label.setText(f"数据集: {folder_name}")
        
        # 加载图片列表
        self.load_images()
    
    def load_images(self):
        """加载当前数据集的图片列表"""
        if not self.current_dataset:
            return

        folder_name = self.current_dataset.get("folder_name")
        if not folder_name:
            return

        self.refresh_images_btn.setEnabled(False)
        self.refresh_images_btn.setText("加载中...")

        # 获取是否显示标注图片的选项
        include_annotated = self.show_annotated_checkbox.isChecked()

        self.image_worker = ImageListWorker(self.client, folder_name, include_annotated)
        self.image_worker.finished.connect(self.on_images_loaded)
        self.image_worker.error.connect(self.on_images_error)
        self.image_worker.start()
    
    def on_images_loaded(self, result):
        """图片列表加载完成"""
        self.refresh_images_btn.setEnabled(True)
        self.refresh_images_btn.setText("刷新图片")
        
        if result.get("success"):
            images = result.get("data", {}).get("images", [])
            self.update_image_list(images)
        else:
            QMessageBox.warning(self, "错误", f"加载图片失败: {result.get('message')}")
    
    def on_images_error(self, error_message):
        """图片列表加载错误"""
        self.refresh_images_btn.setEnabled(True)
        self.refresh_images_btn.setText("刷新图片")
        QMessageBox.critical(self, "错误", f"加载图片失败: {error_message}")
    
    def update_image_list(self, images):
        """更新图片列表显示"""
        self.image_list.clear()

        for image in images:
            item = QListWidgetItem()
            filename = image.get("filename", "")
            file_size = image.get("file_size", image.get("size", 0))
            size_mb = round(file_size / (1024 * 1024), 2) if file_size > 0 else 0

            # 获取内容信息
            content = image.get("content", {})
            description = content.get("description", "") if isinstance(content, dict) else image.get("description", "")
            military_target = content.get("military_target") if isinstance(content, dict) else image.get("military_target")

            # 获取图片信息
            image_info = image.get("image_info", {})
            width = image_info.get("width", image.get("width", 0))
            height = image_info.get("height", image.get("height", 0))

            # 获取来源信息
            metadata = image.get("metadata", {})
            source = metadata.get("source", image.get("source", "upload"))

            # 格式化显示文本
            display_text = f"🖼️ {filename}\n"
            display_text += f"📏 {width}x{height} | 💾 {size_mb} MB\n"

            # 添加来源标识
            if source == "ai_generation":
                display_text += "🤖 AI生成"
            elif source == "traditional_generation":
                display_text += "🔧 传统合成"
            elif source == "annotation":
                display_text += "🏷️ 标注图片"
            else:
                display_text += "📤 手动上传"

            # 添加军事目标信息
            if military_target:
                display_text += f" | 🎯 {military_target}"

            # 添加描述（如果有）
            if description:
                short_desc = description[:25] + "..." if len(description) > 25 else description
                display_text += f"\n💬 {short_desc}"

            item.setText(display_text)
            item.setData(Qt.ItemDataRole.UserRole, image)

            # 为不同类型的图片设置特殊样式
            if source == "ai_generation":
                # 设置AI生成图片的特殊背景色
                item.setBackground(QColor(0, 120, 215, 30))  # 淡蓝色背景
                item.setForeground(QColor(255, 255, 255))  # 白色文字确保对比度
            elif source == "traditional_generation":
                # 设置传统合成图片的特殊背景色
                item.setBackground(QColor(255, 140, 0, 30))  # 淡橙色背景
                item.setForeground(QColor(255, 255, 255))  # 白色文字确保对比度
            elif source == "annotation":
                # 设置标注图片的特殊背景色
                item.setBackground(QColor(255, 165, 0, 30))  # 淡橙色背景
                item.setForeground(QColor(255, 255, 255))  # 白色文字确保对比度

            # 设置工具提示
            tooltip = f"文件名: {filename}\n"
            tooltip += f"尺寸: {width} x {height}\n"
            tooltip += f"大小: {size_mb} MB\n"
            tooltip += f"来源: {self._get_source_text(source)}\n"
            if military_target:
                tooltip += f"军事目标: {military_target}\n"
            if description:
                tooltip += f"描述: {description}"
            item.setToolTip(tooltip)

            self.image_list.addItem(item)

    def _get_source_text(self, source):
        """获取来源文本"""
        source_map = {
            "ai_generation": "AI生成",
            "traditional_generation": "传统合成",
            "annotation": "标注图片",
            "upload": "手动上传"
        }
        return source_map.get(source, "未知")

    def open_auto_config(self):
        """打开AI自动数据集配置界面"""
        try:
            # 创建配置对话框
            config_dialog = QDialog(self)
            config_dialog.setWindowTitle("AI自动数据集管理配置")
            config_dialog.setModal(True)
            config_dialog.resize(600, 500)

            # 设置布局
            layout = QVBoxLayout(config_dialog)
            layout.setContentsMargins(10, 10, 10, 10)

            # 添加配置组件
            config_widget = AutoDatasetConfigWidget()
            config_widget.config_updated.connect(self._on_auto_config_updated)
            layout.addWidget(config_widget)

            # 添加关闭按钮
            button_layout = QHBoxLayout()
            button_layout.addStretch()

            close_btn = ButtonStyleManager.create_button("关闭", "secondary")
            close_btn.clicked.connect(config_dialog.accept)
            button_layout.addWidget(close_btn)

            layout.addLayout(button_layout)

            # 显示对话框
            config_dialog.exec()

        except Exception as e:
            logger.error(f"打开自动配置界面失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"打开配置界面失败: {str(e)}")

    def _on_auto_config_updated(self, config):
        """自动配置更新回调"""
        logger.info("AI自动数据集配置已更新")
        # 刷新数据集列表以显示可能的新数据集
        self.load_datasets()
    
    def on_image_selected(self, item):
        """图片被选中"""
        image = item.data(Qt.ItemDataRole.UserRole)

        # 启用图片操作按钮
        self.rename_image_btn.setEnabled(True)
        self.delete_image_btn.setEnabled(True)

        # 更新图片详情显示
        self.image_detail_widget.update_image_info(image)

    def create_dataset(self):
        """创建新数据集"""
        dialog = DatasetCreateDialog(self)
        dialog.dataset_created.connect(self._on_dataset_create_requested)
        dialog.exec()

    def _on_dataset_create_requested(self, config):
        """处理数据集创建请求"""
        try:
            result = self.client.create_dataset(
                name=config.get("name"),
                naming_format=config.get("naming_format", "timestamp")
            )
            if result.get("success"):
                data = result.get("data", {})
                folder_name = data.get("folder_name", "")
                naming_format = data.get("naming_format", "timestamp")

                format_text = "时间戳格式" if naming_format == "timestamp" else "序号格式"
                QMessageBox.information(
                    self, "成功",
                    f"数据集创建成功！\n\n"
                    f"文件夹名称: {folder_name}\n"
                    f"命名格式: {format_text}"
                )
                self.load_datasets()
            else:
                QMessageBox.warning(self, "失败", f"创建数据集失败: {result.get('message')}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"创建数据集失败: {str(e)}")

    def rename_dataset(self):
        """重命名数据集"""
        if not self.current_dataset:
            return

        old_name = self.current_dataset.get("folder_name", "")
        dialog = RenameDialog(self, "dataset", old_name, self.current_dataset)
        dialog.rename_confirmed.connect(lambda new_name: self._perform_dataset_rename(old_name, new_name))
        dialog.exec()

    def _perform_dataset_rename(self, old_name, new_name):
        """执行数据集重命名"""
        try:
            self._update_status("🔄 正在重命名数据集...", "loading")
            result = self.client.rename_dataset(old_name, new_name)
            if result.get("success"):
                self._update_status("✅ 数据集重命名成功", "success")
                QMessageBox.information(self, "成功", f"数据集重命名成功！\n\n新名称: {new_name}")
                self.load_datasets()
                self.current_dataset = None
                self.image_list.clear()
                self.image_detail_widget.clear_display()
            else:
                self._update_status("❌ 数据集重命名失败", "error")
                QMessageBox.warning(self, "失败", f"重命名数据集失败: {result.get('message')}")
        except Exception as e:
            self._update_status("❌ 数据集重命名失败", "error")
            QMessageBox.critical(self, "错误", f"重命名数据集失败: {str(e)}")

    def delete_dataset(self):
        """删除数据集"""
        if not self.current_dataset:
            return

        dialog = DeleteConfirmDialog(self, "dataset", self.current_dataset)
        dialog.delete_confirmed.connect(self._perform_dataset_delete)
        dialog.exec()

    def _perform_dataset_delete(self):
        """执行数据集删除"""
        if not self.current_dataset:
            return

        folder_name = self.current_dataset.get("folder_name", "")

        try:
            self._update_status("🔄 正在删除数据集...", "loading")
            result = self.client.delete_dataset(folder_name)
            if result.get("success"):
                self._update_status("✅ 数据集删除成功", "success")
                QMessageBox.information(self, "成功", f"数据集 '{folder_name}' 删除成功！")
                self.load_datasets()
                self.current_dataset = None
                self.image_list.clear()
                self.image_detail_widget.clear_display()

                # 禁用按钮
                self.rename_dataset_btn.setEnabled(False)
                self.delete_dataset_btn.setEnabled(False)
                self.upload_image_btn.setEnabled(False)
                self.refresh_images_btn.setEnabled(False)
                self.rename_image_btn.setEnabled(False)
                self.delete_image_btn.setEnabled(False)
            else:
                self._update_status("❌ 数据集删除失败", "error")
                QMessageBox.warning(self, "失败", f"删除数据集失败: {result.get('message')}")
        except Exception as e:
            self._update_status("❌ 数据集删除失败", "error")
            QMessageBox.critical(self, "错误", f"删除数据集失败: {str(e)}")

    def upload_image(self):
        """上传图片到数据集"""
        if not self.current_dataset:
            return

        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择图片文件", "",
            "图片文件 (*.png *.jpg *.jpeg *.bmp *.gif)"
        )

        if file_path:
            # 简单的上传对话框
            description, ok = QInputDialog.getText(self, "图片描述", "请输入图片描述（可选）:")

            if ok:
                try:
                    folder_name = self.current_dataset.get("folder_name")
                    result = self.client.upload_image_to_dataset(
                        folder_name,
                        file_path,
                        description=description if description.strip() else None
                    )

                    if result.get("success"):
                        QMessageBox.information(self, "成功", "图片上传成功！")
                        self.load_images()
                        self.load_datasets()  # 刷新数据集列表以更新图片计数
                    else:
                        QMessageBox.warning(self, "失败", f"上传图片失败: {result.get('message')}")
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"上传图片失败: {str(e)}")

    def rename_image(self):
        """重命名图片"""
        current_item = self.image_list.currentItem()
        if not current_item or not self.current_dataset:
            return

        image = current_item.data(Qt.ItemDataRole.UserRole)
        old_filename = image.get("filename", "")

        dialog = RenameDialog(self, "image", old_filename, image)
        dialog.rename_confirmed.connect(lambda new_filename: self._perform_image_rename(old_filename, new_filename))
        dialog.exec()

    def _perform_image_rename(self, old_filename, new_filename):
        """执行图片重命名"""
        try:
            self._update_status("🔄 正在重命名图片...", "loading")
            folder_name = self.current_dataset.get("folder_name")

            # 提取新文件名（不含扩展名）
            new_name_without_ext = os.path.splitext(new_filename)[0]

            result = self.client.rename_image(folder_name, old_filename, new_name_without_ext)

            if result.get("success"):
                self._update_status("✅ 图片重命名成功", "success")
                QMessageBox.information(self, "成功", f"图片重命名成功！\n\n新名称: {new_filename}")
                self.load_images()
            else:
                self._update_status("❌ 图片重命名失败", "error")
                QMessageBox.warning(self, "失败", f"重命名图片失败: {result.get('message')}")
        except Exception as e:
            self._update_status("❌ 图片重命名失败", "error")
            QMessageBox.critical(self, "错误", f"重命名图片失败: {str(e)}")

    def delete_image(self):
        """删除图片"""
        current_item = self.image_list.currentItem()
        if not current_item or not self.current_dataset:
            return

        image = current_item.data(Qt.ItemDataRole.UserRole)

        dialog = DeleteConfirmDialog(self, "image", image)
        dialog.delete_confirmed.connect(lambda: self._perform_image_delete(image))
        dialog.exec()

    def _perform_image_delete(self, image):
        """执行图片删除"""
        if not self.current_dataset:
            return

        filename = image.get("filename", "")
        folder_name = self.current_dataset.get("folder_name")

        try:
            self._update_status("🔄 正在删除图片...", "loading")
            result = self.client.delete_image(folder_name, filename)

            if result.get("success"):
                self._update_status("✅ 图片删除成功", "success")
                QMessageBox.information(self, "成功", f"图片 '{filename}' 删除成功！")
                self.load_images()
                self.load_datasets()  # 刷新数据集列表以更新图片计数

                # 清空图片信息显示
                self.image_detail_widget.clear_display()
                self.rename_image_btn.setEnabled(False)
                self.delete_image_btn.setEnabled(False)
            else:
                self._update_status("❌ 图片删除失败", "error")
                QMessageBox.warning(self, "失败", f"删除图片失败: {result.get('message')}")
        except Exception as e:
            self._update_status("❌ 图片删除失败", "error")
            QMessageBox.critical(self, "错误", f"删除图片失败: {str(e)}")

    def load_statistics(self):
        """加载统计信息"""
        try:
            result = self.client.get_statistics()
            if result.get("success"):
                data = result.get("data", {})
                total_datasets = data.get("total_datasets", 0)
                total_images = data.get("total_images", 0)
                total_size_mb = data.get("total_size_mb", 0)

                stats_text = f"📊 数据集: {total_datasets} 个 | 🖼️ 图片: {total_images} 张 | 💾 总大小: {total_size_mb} MB"
                self.stats_label.setText(stats_text)
            else:
                self.stats_label.setText("📊 统计信息获取失败")
        except Exception as e:
            self.stats_label.setText("📊 统计信息获取失败")
            logger.error(f"获取统计信息失败: {str(e)}")

    def _update_status(self, message, status_type="info"):
        """更新状态显示"""
        self.status_label.setText(message)
        self.status_label.setStyleSheet(get_status_style(status_type))

    def closeEvent(self, event):
        """关闭事件"""
        if hasattr(self, 'refresh_timer'):
            self.refresh_timer.stop()

        if hasattr(self, 'model_refresh_timer'):
            self.model_refresh_timer.stop()

        if hasattr(self, 'client'):
            self.client.close()

        event.accept()

    def refresh_model_info(self):
        """刷新模型信息"""
        try:
            # 获取传统AI模型信息
            ai_models_info = self.ai_client.get_available_models()
            current_ai_model = ai_models_info.get('current_model', {})

            # 获取SD权重模型信息
            sd_models_result = self.sd_model_service.get_current_model()
            current_sd_model = sd_models_result.get('data', {}) if sd_models_result.get('success') else {}

            # 确定当前使用的模型
            if current_ai_model.get('loaded', False):
                model_key = current_ai_model.get('model_key', '未知')
                model_type = "传统AI模型"
                self.current_model_label.setText(f"模型: {model_key}")
                self.current_model_label.setStyleSheet("color: #4CAF50; font-size: 11px;")
                self.model_type_label.setText(f"类型: {model_type}")
                self.model_type_label.setStyleSheet("color: #4CAF50; font-size: 11px;")

            elif current_sd_model.get('current_model_key'):
                model_key = current_sd_model.get('current_model_key', '未知')
                model_type = "SD权重模型"
                self.current_model_label.setText(f"模型: {model_key}")
                self.current_model_label.setStyleSheet("color: #2196F3; font-size: 11px;")
                self.model_type_label.setText(f"类型: {model_type}")
                self.model_type_label.setStyleSheet("color: #2196F3; font-size: 11px;")

            else:
                self.current_model_label.setText("模型: 未加载")
                self.current_model_label.setStyleSheet("color: #888; font-size: 11px;")
                self.model_type_label.setText("类型: 未知")
                self.model_type_label.setStyleSheet("color: #888; font-size: 11px;")

        except Exception as e:
            logger.warning(f"刷新模型信息失败: {str(e)}")
            self.current_model_label.setText("模型: 获取失败")
            self.current_model_label.setStyleSheet("color: #f44336; font-size: 11px;")
            self.model_type_label.setText("类型: 获取失败")
            self.model_type_label.setStyleSheet("color: #f44336; font-size: 11px;")
