"""
数据集创建对话框
支持命名格式选择和详细配置
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton,
    QRadioButton, QButtonGroup, QGroupBox, QTextEdit, QFormLayout,
    QMessageBox, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

from styles.button_styles import ButtonStyleManager
import logging

logger = logging.getLogger(__name__)

class DatasetCreateDialog(QDialog):
    """数据集创建对话框"""
    
    dataset_created = pyqtSignal(dict)  # 发送创建结果
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("创建新数据集")
        self.setModal(True)
        self.setFixedSize(450, 350)
        
        # 设置中文字体
        font = QFont()
        font.setFamily("Microsoft YaHei")
        font.setPointSize(9)
        self.setFont(font)
        
        self.setup_ui()
        
        # 默认选择时间戳格式
        self.timestamp_radio.setChecked(True)
        self._update_preview()
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title_label = QLabel("创建新数据集")
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 数据集名称
        name_group = QGroupBox("数据集名称")
        name_layout = QFormLayout(name_group)
        
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("输入数据集名称（可选）")
        self.name_edit.textChanged.connect(self._update_preview)
        name_layout.addRow("名称:", self.name_edit)
        
        layout.addWidget(name_group)
        
        # 命名格式选择
        format_group = QGroupBox("文件夹命名格式")
        format_layout = QVBoxLayout(format_group)
        
        self.format_group = QButtonGroup()
        
        # 时间戳格式
        self.timestamp_radio = QRadioButton("时间戳格式")
        self.timestamp_radio.setToolTip("使用时间戳命名，如: 20250628_134500_数据集名称")
        self.timestamp_radio.toggled.connect(self._update_preview)
        self.format_group.addButton(self.timestamp_radio, 0)
        format_layout.addWidget(self.timestamp_radio)
        
        # 序号格式
        self.sequential_radio = QRadioButton("序号格式")
        self.sequential_radio.setToolTip("使用三位数序号命名，如: 001_数据集名称, 002_数据集名称")
        self.sequential_radio.toggled.connect(self._update_preview)
        self.format_group.addButton(self.sequential_radio, 1)
        format_layout.addWidget(self.sequential_radio)
        
        layout.addWidget(format_group)
        
        # 预览
        preview_group = QGroupBox("文件夹名称预览")
        preview_layout = QVBoxLayout(preview_group)
        
        self.preview_label = QLabel()
        self.preview_label.setStyleSheet("""
            QLabel {
                background-color: #f0f0f0;
                border: 1px solid #ccc;
                padding: 8px;
                border-radius: 4px;
                font-family: 'Consolas', 'Monaco', monospace;
                color: #333;
            }
        """)
        self.preview_label.setWordWrap(True)
        preview_layout.addWidget(self.preview_label)
        
        layout.addWidget(preview_group)
        
        # 按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.cancel_btn = ButtonStyleManager.create_button("取消", "secondary")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        self.create_btn = ButtonStyleManager.create_button("创建", "primary")
        self.create_btn.clicked.connect(self._create_dataset)
        button_layout.addWidget(self.create_btn)
        
        layout.addLayout(button_layout)
    
    def _update_preview(self):
        """更新文件夹名称预览"""
        name = self.name_edit.text().strip()
        
        if self.timestamp_radio.isChecked():
            if name:
                preview = f"20250628_134500_{name}"
                description = "时间戳格式: YYYYMMDD_HHMMSS_名称"
            else:
                preview = "20250628_134500"
                description = "时间戳格式: YYYYMMDD_HHMMSS"
        else:
            if name:
                preview = f"001_{name}"
                description = "序号格式: 001_名称, 002_名称, ..."
            else:
                preview = "001"
                description = "序号格式: 001, 002, 003, ..."
        
        self.preview_label.setText(f"示例: {preview}\n\n{description}")
    
    def _create_dataset(self):
        """创建数据集"""
        name = self.name_edit.text().strip() if self.name_edit.text().strip() else None
        naming_format = "timestamp" if self.timestamp_radio.isChecked() else "sequential"
        
        # 验证输入
        if name and len(name) > 50:
            QMessageBox.warning(self, "输入错误", "数据集名称不能超过50个字符")
            return
        
        if name and not all(c.isalnum() or c in (' ', '-', '_', '中') or '\u4e00' <= c <= '\u9fff' for c in name):
            QMessageBox.warning(self, "输入错误", "数据集名称只能包含字母、数字、中文、空格、连字符和下划线")
            return
        
        # 发送创建信号
        result = {
            "name": name,
            "naming_format": naming_format
        }
        
        self.dataset_created.emit(result)
        self.accept()
    
    def get_dataset_config(self):
        """获取数据集配置"""
        return {
            "name": self.name_edit.text().strip() if self.name_edit.text().strip() else None,
            "naming_format": "timestamp" if self.timestamp_radio.isChecked() else "sequential"
        }
