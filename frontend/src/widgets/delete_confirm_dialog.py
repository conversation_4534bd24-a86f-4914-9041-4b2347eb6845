"""
删除确认对话框
提供安全的删除确认功能，包含详细信息和风险提示
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QGroupBox, QTextEdit, QCheckBox, QFrame, QScrollArea
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QPixmap
import logging

from styles.button_styles import ButtonStyleManager

logger = logging.getLogger(__name__)

class DeleteConfirmDialog(QDialog):
    """删除确认对话框"""
    
    delete_confirmed = pyqtSignal()  # 确认删除信号
    
    def __init__(self, parent=None, delete_type="dataset", item_info=None):
        super().__init__(parent)
        self.delete_type = delete_type  # "dataset" 或 "image"
        self.item_info = item_info or {}
        
        self.setWindowTitle(f"确认删除{'数据集' if delete_type == 'dataset' else '图片'}")
        self.setModal(True)
        self.setFixedSize(500, 400)
        
        # 设置中文字体
        font = QFont()
        font.setFamily("Microsoft YaHei")
        font.setPointSize(9)
        self.setFont(font)
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 警告标题
        title_layout = QHBoxLayout()
        
        # 警告图标
        warning_label = QLabel("⚠️")
        warning_label.setStyleSheet("font-size: 24px;")
        title_layout.addWidget(warning_label)
        
        # 标题文本
        title_text = f"确认删除{'数据集' if self.delete_type == 'dataset' else '图片'}"
        title_label = QLabel(title_text)
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #dc3545;")
        title_layout.addWidget(title_label)
        
        title_layout.addStretch()
        layout.addLayout(title_layout)
        
        # 警告信息
        warning_text = self._get_warning_text()
        warning_label = QLabel(warning_text)
        warning_label.setStyleSheet("""
            QLabel {
                background-color: #f8d7da;
                border: 1px solid #f1aeb5;
                border-radius: 6px;
                padding: 12px;
                color: #721c24;
                font-weight: bold;
            }
        """)
        warning_label.setWordWrap(True)
        layout.addWidget(warning_label)
        
        # 详细信息
        self.create_details_section(layout)
        
        # 影响说明
        self.create_impact_section(layout)
        
        # 确认复选框
        self.confirm_checkbox = QCheckBox("我理解此操作无法撤销，确认要删除")
        self.confirm_checkbox.setStyleSheet("""
            QCheckBox {
                font-weight: bold;
                color: #dc3545;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)
        self.confirm_checkbox.stateChanged.connect(self._update_delete_button)
        layout.addWidget(self.confirm_checkbox)
        
        # 按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.cancel_btn = ButtonStyleManager.create_button("取消", "secondary")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        self.delete_btn = ButtonStyleManager.create_button("确认删除", "danger")
        self.delete_btn.clicked.connect(self._confirm_delete)
        self.delete_btn.setEnabled(False)
        button_layout.addWidget(self.delete_btn)
        
        layout.addLayout(button_layout)
    
    def _get_warning_text(self):
        """获取警告文本"""
        if self.delete_type == "dataset":
            name = self.item_info.get("folder_name", "未知")
            total_images = self.item_info.get("total_images", 0)
            return f"您即将删除数据集 '{name}'，包含 {total_images} 张图片和所有相关文件。此操作无法撤销！"
        else:
            filename = self.item_info.get("filename", "未知")
            return f"您即将删除图片 '{filename}' 及其描述文件。此操作无法撤销！"
    
    def create_details_section(self, layout):
        """创建详细信息部分"""
        details_group = QGroupBox("详细信息")
        details_layout = QVBoxLayout(details_group)
        
        self.details_text = QTextEdit()
        self.details_text.setMaximumHeight(120)
        self.details_text.setReadOnly(True)
        self.details_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 9pt;
            }
        """)
        
        # 填充详细信息
        details_text = self._get_details_text()
        self.details_text.setPlainText(details_text)
        
        details_layout.addWidget(self.details_text)
        layout.addWidget(details_group)
    
    def _get_details_text(self):
        """获取详细信息文本"""
        if self.delete_type == "dataset":
            folder_name = self.item_info.get("folder_name", "未知")
            total_images = self.item_info.get("total_images", 0)
            created_at = self.item_info.get("created_at", "")[:19] if self.item_info.get("created_at") else "未知"
            description = self.item_info.get("description", "无")
            
            details = f"数据集名称: {folder_name}\n"
            details += f"图片数量: {total_images} 张\n"
            details += f"创建时间: {created_at}\n"
            details += f"描述: {description}"
            
            return details
        else:
            filename = self.item_info.get("filename", "未知")
            file_size = self.item_info.get("file_size", self.item_info.get("size", 0))
            size_mb = round(file_size / (1024 * 1024), 2) if file_size > 0 else 0
            
            # 获取图片信息
            image_info = self.item_info.get("image_info", {})
            width = image_info.get("width", self.item_info.get("width", 0))
            height = image_info.get("height", self.item_info.get("height", 0))
            format_name = image_info.get("format", self.item_info.get("format", "未知"))
            
            # 获取内容信息
            content = self.item_info.get("content", {})
            description = content.get("description", "") if isinstance(content, dict) else self.item_info.get("description", "")
            
            details = f"文件名: {filename}\n"
            details += f"文件大小: {size_mb} MB\n"
            details += f"图片尺寸: {width} x {height}\n"
            details += f"图片格式: {format_name}\n"
            if description:
                details += f"描述: {description}"
            
            return details
    
    def create_impact_section(self, layout):
        """创建影响说明部分"""
        impact_group = QGroupBox("删除影响")
        impact_layout = QVBoxLayout(impact_group)
        
        impact_text = QLabel()
        impact_text.setWordWrap(True)
        impact_text.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 4px;
                padding: 10px;
                color: #856404;
            }
        """)
        
        if self.delete_type == "dataset":
            impact_content = """
🗂️ 将删除整个数据集文件夹及其所有内容
🖼️ 所有图片文件将被永久删除
📄 所有JSON描述文件将被永久删除
📊 数据集元数据文件将被永久删除
⚠️ 此操作无法通过回收站恢复
            """.strip()
        else:
            impact_content = """
🖼️ 图片文件将被永久删除
📄 对应的JSON描述文件将被永久删除
📊 数据集元数据将被更新
⚠️ 此操作无法通过回收站恢复
            """.strip()
        
        impact_text.setText(impact_content)
        impact_layout.addWidget(impact_text)
        layout.addWidget(impact_group)
    
    def _update_delete_button(self):
        """更新删除按钮状态"""
        self.delete_btn.setEnabled(self.confirm_checkbox.isChecked())
    
    def _confirm_delete(self):
        """确认删除"""
        if not self.confirm_checkbox.isChecked():
            return
        
        self.delete_confirmed.emit()
        self.accept()
    
    def get_item_info(self):
        """获取项目信息"""
        return self.item_info
