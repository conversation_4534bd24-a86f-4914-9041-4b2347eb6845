# -*- coding: utf-8 -*-
"""
前端编码工具模块 - 解决中文显示乱码问题
"""

import sys
import os
import locale
from PyQt6.QtGui import QFont
from PyQt6.QtWidgets import QApplication


class EncodingManager:
    """编码管理器"""
    
    @staticmethod
    def setup_system_encoding():
        """设置系统编码"""
        # 设置环境变量
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        os.environ['PYTHONUTF8'] = '1'
        os.environ['LC_ALL'] = 'zh_CN.UTF-8'
        
        # 设置Python默认编码
        if hasattr(sys, 'setdefaultencoding'):
            sys.setdefaultencoding('utf-8')
        
        # 设置locale
        try:
            if sys.platform.startswith('win'):
                locale.setlocale(locale.LC_ALL, 'Chinese_China.UTF-8')
            else:
                locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
        except locale.Error:
            try:
                locale.setlocale(locale.LC_ALL, 'C.UTF-8')
            except locale.Error:
                pass  # 忽略设置失败
    
    @staticmethod
    def setup_qt_encoding():
        """设置Qt编码"""
        # PyQt6中QTextCodec已被移除，Qt6默认使用UTF-8
        # 这里只需要确保环境变量设置正确
        pass
    
    @staticmethod
    def setup_chinese_font():
        """设置中文字体"""
        app = QApplication.instance()
        if not app:
            return None

        # 使用刚安装的中文字体，按优先级排序
        font_families = [
            "Noto Sans CJK SC",        # 思源黑体简体中文
            "WenQuanYi Micro Hei",     # 文泉驿微米黑
            "WenQuanYi Zen Hei",       # 文泉驿正黑
            "Noto Sans CJK TC",        # 思源黑体繁体中文
            "Microsoft YaHei",         # Windows字体
            "SimHei",                  # 黑体
            "DejaVu Sans",             # Linux默认字体
            "Liberation Sans",         # 开源字体
            "Arial",                   # 通用字体
        ]

        # 尝试设置字体
        selected_font = None
        for family in font_families:
            test_font = QFont(family)
            test_font.setPointSize(10)

            # 测试字体是否真正支持中文
            # 通过检查字体族名是否匹配来验证
            actual_family = test_font.family()
            if (family in actual_family or
                actual_family in family or
                family.replace(" ", "").lower() in actual_family.replace(" ", "").lower()):
                selected_font = test_font
                print(f"选择字体: {family} -> {actual_family}")
                break

        # 如果没有找到合适字体，创建一个默认字体
        if not selected_font:
            selected_font = QFont()
            selected_font.setFamily("sans-serif")
            print("使用系统默认字体")

        # 设置字体属性
        selected_font.setPointSize(10)
        selected_font.setStyleHint(QFont.StyleHint.SansSerif)
        selected_font.setStyleStrategy(QFont.StyleStrategy.PreferAntialias)

        # 设置为应用程序默认字体
        app.setFont(selected_font)

        return selected_font
    
    @staticmethod
    def fix_text_encoding(text):
        """修复文本编码问题"""
        if not isinstance(text, str):
            return str(text)
        
        # 如果文本已经是正确的UTF-8，直接返回
        try:
            text.encode('utf-8')
            return text
        except UnicodeEncodeError:
            pass
        
        # 尝试不同的编码方式解码
        encodings = ['utf-8', 'gbk', 'gb2312', 'big5', 'latin1']
        
        for encoding in encodings:
            try:
                if isinstance(text, bytes):
                    decoded_text = text.decode(encoding)
                else:
                    # 如果是字符串，尝试重新编码
                    decoded_text = text.encode('latin1').decode(encoding)
                return decoded_text
            except (UnicodeDecodeError, UnicodeEncodeError):
                continue
        
        # 如果所有编码都失败，使用错误处理
        if isinstance(text, bytes):
            return text.decode('utf-8', errors='replace')
        else:
            return text
    
    @staticmethod
    def ensure_utf8_string(obj):
        """确保对象是UTF-8字符串"""
        if isinstance(obj, bytes):
            return obj.decode('utf-8', errors='replace')
        elif isinstance(obj, str):
            return obj
        else:
            return str(obj)


def initialize_encoding():
    """初始化编码设置"""
    EncodingManager.setup_system_encoding()
    EncodingManager.setup_qt_encoding()
    return EncodingManager.setup_chinese_font()


def fix_display_text(text):
    """修复显示文本的编码问题"""
    return EncodingManager.fix_text_encoding(text)


# 在模块导入时自动初始化编码
initialize_encoding()
