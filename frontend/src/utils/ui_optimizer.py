"""
UI优化工具 - 统一的界面优化和错误处理
"""

import logging
from typing import Optional, Callable, Any
from PyQt6.QtWidgets import (
    QWidget, QMessageBox, QProgressBar, QLabel, QPushButton,
    QApplication, QDialog, QVBoxLayout, QHBoxLayout, QTextEdit
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QObject
from PyQt6.QtGui import QFont, QPixmap, QIcon

logger = logging.getLogger(__name__)

class UIOptimizer:
    """UI优化工具类"""
    
    @staticmethod
    def setup_chinese_font(widget: QWidget):
        """设置中文字体"""
        font = QFont()
        font.setFamily("Microsoft YaHei")
        font.setPointSize(10)
        widget.setFont(font)
    
    @staticmethod
    def ensure_minimum_size(widget: QWidget, min_width: int = 800, min_height: int = 600):
        """确保最小尺寸"""
        widget.setMinimumSize(min_width, min_height)
    
    @staticmethod
    def center_window(widget: QWidget):
        """居中显示窗口"""
        screen = QApplication.primaryScreen().geometry()
        widget_geometry = widget.geometry()
        x = (screen.width() - widget_geometry.width()) // 2
        y = (screen.height() - widget_geometry.height()) // 2
        widget.move(x, y)
    
    @staticmethod
    def set_window_icon(widget: QWidget, icon_path: str = None):
        """设置窗口图标"""
        if icon_path and QPixmap(icon_path).isNull() == False:
            widget.setWindowIcon(QIcon(icon_path))

class ErrorHandler(QObject):
    """统一错误处理器"""
    
    error_occurred = pyqtSignal(str, str)  # 错误类型, 错误消息
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.error_count = 0
        self.last_error_time = None
        
    def handle_error(self, error_type: str, error_message: str, 
                    parent_widget: QWidget = None, show_dialog: bool = True):
        """
        统一错误处理
        
        Args:
            error_type: 错误类型 (network, model, generation, validation, etc.)
            error_message: 错误消息
            parent_widget: 父窗口
            show_dialog: 是否显示对话框
        """
        self.error_count += 1
        
        # 记录错误日志
        logger.error(f"[{error_type}] {error_message}")
        
        # 发送信号
        self.error_occurred.emit(error_type, error_message)
        
        if show_dialog and parent_widget:
            self._show_error_dialog(error_type, error_message, parent_widget)
    
    def _show_error_dialog(self, error_type: str, error_message: str, parent: QWidget):
        """显示错误对话框"""
        title_map = {
            "network": "网络连接错误",
            "model": "模型加载错误", 
            "generation": "图像生成错误",
            "validation": "输入验证错误",
            "file": "文件操作错误",
            "permission": "权限错误",
            "unknown": "未知错误"
        }
        
        title = title_map.get(error_type, "错误")
        
        # 根据错误类型提供不同的解决建议
        suggestions = self._get_error_suggestions(error_type, error_message)
        
        full_message = f"{error_message}\n\n{suggestions}" if suggestions else error_message
        
        QMessageBox.critical(parent, title, full_message)
    
    def _get_error_suggestions(self, error_type: str, error_message: str) -> str:
        """获取错误解决建议"""
        suggestions_map = {
            "network": "建议检查:\n• 网络连接是否正常\n• 后端服务是否启动\n• 防火墙设置是否正确",
            "model": "建议检查:\n• 模型文件是否存在\n• 模型配置是否正确\n• 显存是否充足",
            "generation": "建议检查:\n• 输入参数是否有效\n• 模型是否正确加载\n• 系统资源是否充足",
            "validation": "请检查输入内容是否符合要求",
            "file": "建议检查:\n• 文件路径是否正确\n• 文件权限是否充足\n• 磁盘空间是否充足",
            "permission": "请以管理员权限运行程序或检查文件权限"
        }
        
        return suggestions_map.get(error_type, "")

class ProgressManager:
    """进度管理器"""
    
    def __init__(self, progress_bar: QProgressBar, status_label: QLabel = None):
        self.progress_bar = progress_bar
        self.status_label = status_label
        self.current_task = ""
        
    def start_task(self, task_name: str, max_value: int = 100):
        """开始任务"""
        self.current_task = task_name
        self.progress_bar.setVisible(True)
        self.progress_bar.setMaximum(max_value)
        self.progress_bar.setValue(0)
        
        if self.status_label:
            self.status_label.setText(f"正在{task_name}...")
    
    def update_progress(self, value: int, message: str = None):
        """更新进度"""
        self.progress_bar.setValue(value)
        
        if message and self.status_label:
            self.status_label.setText(message)
        elif self.status_label:
            percentage = int((value / self.progress_bar.maximum()) * 100)
            self.status_label.setText(f"正在{self.current_task}... {percentage}%")
    
    def finish_task(self, success_message: str = None):
        """完成任务"""
        self.progress_bar.setVisible(False)
        
        if success_message and self.status_label:
            self.status_label.setText(success_message)
        elif self.status_label:
            self.status_label.setText(f"{self.current_task}完成")

class NotificationManager:
    """通知管理器"""
    
    @staticmethod
    def show_success(parent: QWidget, title: str, message: str):
        """显示成功消息"""
        QMessageBox.information(parent, title, f"✅ {message}")
    
    @staticmethod
    def show_warning(parent: QWidget, title: str, message: str):
        """显示警告消息"""
        QMessageBox.warning(parent, title, f"⚠️ {message}")
    
    @staticmethod
    def show_error(parent: QWidget, title: str, message: str):
        """显示错误消息"""
        QMessageBox.critical(parent, title, f"❌ {message}")
    
    @staticmethod
    def ask_confirmation(parent: QWidget, title: str, message: str) -> bool:
        """询问确认"""
        reply = QMessageBox.question(
            parent, title, f"❓ {message}",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        return reply == QMessageBox.StandardButton.Yes

class StatusManager:
    """状态管理器"""
    
    def __init__(self, status_label: QLabel):
        self.status_label = status_label
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self._clear_temporary_status)
        self.permanent_status = ""
    
    def set_status(self, message: str, temporary: bool = False, duration: int = 3000):
        """设置状态消息"""
        self.status_label.setText(message)
        
        if temporary:
            self.status_timer.stop()
            self.status_timer.start(duration)
        else:
            self.permanent_status = message
            self.status_timer.stop()
    
    def _clear_temporary_status(self):
        """清除临时状态"""
        self.status_timer.stop()
        self.status_label.setText(self.permanent_status)
    
    def set_ready(self):
        """设置就绪状态"""
        self.set_status("就绪")

class ValidationHelper:
    """输入验证助手"""
    
    @staticmethod
    def validate_not_empty(value: str, field_name: str):
        """验证非空"""
        if not value or not value.strip():
            return False, f"{field_name}不能为空"
        return True, ""
    
    @staticmethod
    def validate_number_range(value: float, min_val: float, max_val: float,
                            field_name: str):
        """验证数值范围"""
        if value < min_val or value > max_val:
            return False, f"{field_name}必须在 {min_val} 到 {max_val} 之间"
        return True, ""
    
    @staticmethod
    def validate_file_path(path: str, must_exist: bool = True):
        """验证文件路径"""
        import os
        
        if not path or not path.strip():
            return False, "文件路径不能为空"
        
        if must_exist and not os.path.exists(path):
            return False, f"文件不存在: {path}"
        
        return True, ""
    
    @staticmethod
    def validate_all(validations: list):
        """批量验证"""
        for is_valid, error_msg in validations:
            if not is_valid:
                return False, error_msg
        return True, ""

class UIStateManager:
    """UI状态管理器"""
    
    def __init__(self):
        self.widget_states = {}
        self.button_states = {}
    
    def save_widget_state(self, widget: QWidget, state_name: str):
        """保存控件状态"""
        self.widget_states[state_name] = widget.isEnabled()
    
    def restore_widget_state(self, widget: QWidget, state_name: str):
        """恢复控件状态"""
        if state_name in self.widget_states:
            widget.setEnabled(self.widget_states[state_name])
    
    def disable_widgets(self, widgets):
        """禁用控件组"""
        for widget in widgets:
            widget.setEnabled(False)
    
    def enable_widgets(self, widgets):
        """启用控件组"""
        for widget in widgets:
            widget.setEnabled(True)
    
    def save_button_text(self, button: QPushButton, state_name: str):
        """保存按钮文本"""
        self.button_states[state_name] = button.text()
    
    def restore_button_text(self, button: QPushButton, state_name: str):
        """恢复按钮文本"""
        if state_name in self.button_states:
            button.setText(self.button_states[state_name])

class DetailedErrorDialog(QDialog):
    """详细错误对话框"""
    
    def __init__(self, title: str, message: str, details: str = None, parent=None):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(500, 400)
        
        layout = QVBoxLayout(self)
        
        # 错误消息
        message_label = QLabel(message)
        message_label.setWordWrap(True)
        layout.addWidget(message_label)
        
        # 详细信息
        if details:
            details_text = QTextEdit()
            details_text.setPlainText(details)
            details_text.setReadOnly(True)
            details_text.setMaximumHeight(200)
            layout.addWidget(QLabel("详细信息:"))
            layout.addWidget(details_text)
        
        # 按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        ok_button = QPushButton("确定")
        ok_button.clicked.connect(self.accept)
        button_layout.addWidget(ok_button)
        
        layout.addLayout(button_layout)
