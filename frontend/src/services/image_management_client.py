"""
图片管理API客户端
"""
import requests
import json
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime


class ImageManagementClient:
    """图片管理API客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.api_base = f"{base_url}/api/v1"
    
    def get_images(
        self,
        page: int = 1,
        page_size: int = 20,
        generation_type: Optional[str] = None,
        military_target: Optional[str] = None,
        weather: Optional[str] = None,
        scene: Optional[str] = None,
        category: Optional[str] = None,
        is_favorite: Optional[bool] = None,
        search_text: Optional[str] = None,
        sort_by: str = "created_at",
        sort_order: str = "desc"
    ) -> Dict[str, Any]:
        """获取图片列表"""
        params = {
            "page": page,
            "page_size": page_size,
            "sort_by": sort_by,
            "sort_order": sort_order
        }
        
        # 添加筛选参数
        if generation_type:
            params["generation_type"] = generation_type
        if military_target:
            params["military_target"] = military_target
        if weather:
            params["weather"] = weather
        if scene:
            params["scene"] = scene
        if category:
            params["category"] = category
        if is_favorite is not None:
            params["is_favorite"] = is_favorite
        if search_text:
            params["search_text"] = search_text
        
        try:
            response = requests.get(f"{self.api_base}/images/", params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            return {"success": False, "message": f"请求失败: {str(e)}"}
    
    def get_image(self, image_id: int) -> Dict[str, Any]:
        """获取单个图片详情"""
        try:
            response = requests.get(f"{self.api_base}/images/{image_id}")
            response.raise_for_status()
            return {"success": True, "data": response.json()}
        except requests.exceptions.RequestException as e:
            return {"success": False, "message": f"获取图片失败: {str(e)}"}
    
    def get_image_file_url(self, image_id: int) -> str:
        """获取图片文件URL"""
        return f"{self.api_base}/images/{image_id}/file"
    
    def upload_image(
        self,
        file_path: str,
        description: Optional[str] = None,
        tags: Optional[List[str]] = None,
        category: Optional[str] = None,
        military_target: Optional[str] = None,
        weather: Optional[str] = None,
        scene: Optional[str] = None
    ) -> Dict[str, Any]:
        """上传图片"""
        try:
            with open(file_path, 'rb') as f:
                files = {'file': f}
                data = {}
                
                if description:
                    data['description'] = description
                if tags:
                    data['tags'] = json.dumps(tags)
                if category:
                    data['category'] = category
                if military_target:
                    data['military_target'] = military_target
                if weather:
                    data['weather'] = weather
                if scene:
                    data['scene'] = scene
                
                response = requests.post(
                    f"{self.api_base}/images/upload",
                    files=files,
                    data=data
                )
                response.raise_for_status()
                return response.json()
        except requests.exceptions.RequestException as e:
            return {"success": False, "message": f"上传失败: {str(e)}"}
        except Exception as e:
            return {"success": False, "message": f"文件处理失败: {str(e)}"}
    
    def update_image(
        self,
        image_id: int,
        description: Optional[str] = None,
        tags: Optional[List[str]] = None,
        category: Optional[str] = None,
        is_favorite: Optional[bool] = None
    ) -> Dict[str, Any]:
        """更新图片信息"""
        data = {}
        if description is not None:
            data['description'] = description
        if tags is not None:
            data['tags'] = tags
        if category is not None:
            data['category'] = category
        if is_favorite is not None:
            data['is_favorite'] = is_favorite
        
        try:
            response = requests.put(
                f"{self.api_base}/images/{image_id}",
                json=data
            )
            response.raise_for_status()
            return {"success": True, "data": response.json()}
        except requests.exceptions.RequestException as e:
            return {"success": False, "message": f"更新失败: {str(e)}"}
    
    def delete_image(self, image_id: int) -> Dict[str, Any]:
        """删除图片"""
        try:
            response = requests.delete(f"{self.api_base}/images/{image_id}")
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            return {"success": False, "message": f"删除失败: {str(e)}"}
    
    def batch_delete_images(self, image_ids: List[int]) -> Dict[str, Any]:
        """批量删除图片"""
        try:
            response = requests.post(
                f"{self.api_base}/images/batch-delete",
                json={"image_ids": image_ids}
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            return {"success": False, "message": f"批量删除失败: {str(e)}"}
    
    def batch_update_images(
        self,
        image_ids: List[int],
        description: Optional[str] = None,
        tags: Optional[List[str]] = None,
        category: Optional[str] = None,
        is_favorite: Optional[bool] = None
    ) -> Dict[str, Any]:
        """批量更新图片"""
        updates = {}
        if description is not None:
            updates['description'] = description
        if tags is not None:
            updates['tags'] = tags
        if category is not None:
            updates['category'] = category
        if is_favorite is not None:
            updates['is_favorite'] = is_favorite
        
        try:
            response = requests.post(
                f"{self.api_base}/images/batch-update",
                json={"image_ids": image_ids, "updates": updates}
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            return {"success": False, "message": f"批量更新失败: {str(e)}"}
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取图片统计信息"""
        try:
            response = requests.get(f"{self.api_base}/images/statistics/overview")
            response.raise_for_status()
            return {"success": True, "data": response.json()}
        except requests.exceptions.RequestException as e:
            return {"success": False, "message": f"获取统计信息失败: {str(e)}"}
    
    def get_image_annotations(self, image_id: int) -> Dict[str, Any]:
        """获取图片标注"""
        try:
            response = requests.get(f"{self.api_base}/images/{image_id}/annotations")
            response.raise_for_status()
            return {"success": True, "data": response.json()}
        except requests.exceptions.RequestException as e:
            return {"success": False, "message": f"获取标注失败: {str(e)}"}
    
    def create_annotation(
        self,
        image_id: int,
        label: str,
        bbox_x: float,
        bbox_y: float,
        bbox_width: float,
        bbox_height: float,
        confidence: Optional[float] = None,
        source: str = "manual"
    ) -> Dict[str, Any]:
        """创建图片标注"""
        data = {
            "label": label,
            "bbox_x": bbox_x,
            "bbox_y": bbox_y,
            "bbox_width": bbox_width,
            "bbox_height": bbox_height,
            "source": source
        }
        if confidence is not None:
            data["confidence"] = confidence
        
        try:
            response = requests.post(
                f"{self.api_base}/images/{image_id}/annotations",
                json=data
            )
            response.raise_for_status()
            return {"success": True, "data": response.json()}
        except requests.exceptions.RequestException as e:
            return {"success": False, "message": f"创建标注失败: {str(e)}"}
    
    def delete_annotation(self, annotation_id: int) -> Dict[str, Any]:
        """删除标注"""
        try:
            response = requests.delete(f"{self.api_base}/images/annotations/{annotation_id}")
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            return {"success": False, "message": f"删除标注失败: {str(e)}"}
