"""
自定义模型管理客户端
"""

import requests
import logging
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

class CustomModelClient:
    """自定义模型管理客户端"""
    
    def __init__(self, api_base: str = "http://localhost:8000/api/v1"):
        self.api_base = api_base
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json'
        })
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """发送API请求"""
        url = f"{self.api_base}{endpoint}"
        
        try:
            response = self.session.request(method, url, **kwargs)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"API请求失败: {method} {url}, 错误: {str(e)}")
            raise Exception(f"API请求失败: {str(e)}")
    
    def list_custom_models(self) -> Dict[str, Any]:
        """列出所有自定义模型"""
        try:
            return self._make_request('GET', '/custom-models/list')
        except Exception as e:
            logger.error(f"列出自定义模型失败: {str(e)}")
            return {
                "success": False,
                "message": f"列出自定义模型失败: {str(e)}",
                "data": {"models": {}, "total": 0}
            }
    
    def list_enabled_custom_models(self) -> Dict[str, Any]:
        """列出启用的自定义模型"""
        try:
            return self._make_request('GET', '/custom-models/enabled')
        except Exception as e:
            logger.error(f"列出启用的自定义模型失败: {str(e)}")
            return {
                "success": False,
                "message": f"列出启用的自定义模型失败: {str(e)}",
                "data": {"models": {}, "total": 0}
            }
    
    def add_custom_model(
        self,
        model_key: str,
        model_name: str,
        model_path: str,
        description: str = "",
        model_type: str = "diffusers",
        **kwargs
    ) -> Dict[str, Any]:
        """添加自定义模型"""
        try:
            data = {
                "model_key": model_key,
                "model_name": model_name,
                "model_path": model_path,
                "description": description,
                "model_type": model_type,
                **kwargs
            }
            return self._make_request('POST', '/custom-models/add', json=data)
        except Exception as e:
            logger.error(f"添加自定义模型失败: {str(e)}")
            return {
                "success": False,
                "message": f"添加自定义模型失败: {str(e)}"
            }
    
    def update_custom_model(self, model_key: str, **updates) -> Dict[str, Any]:
        """更新自定义模型"""
        try:
            return self._make_request('PUT', f'/custom-models/{model_key}', json=updates)
        except Exception as e:
            logger.error(f"更新自定义模型失败: {str(e)}")
            return {
                "success": False,
                "message": f"更新自定义模型失败: {str(e)}"
            }
    
    def remove_custom_model(self, model_key: str, delete_files: bool = False) -> Dict[str, Any]:
        """移除自定义模型"""
        try:
            params = {"delete_files": delete_files}
            return self._make_request('DELETE', f'/custom-models/{model_key}', params=params)
        except Exception as e:
            logger.error(f"移除自定义模型失败: {str(e)}")
            return {
                "success": False,
                "message": f"移除自定义模型失败: {str(e)}"
            }
    
    def get_custom_model(self, model_key: str) -> Dict[str, Any]:
        """获取指定自定义模型"""
        try:
            return self._make_request('GET', f'/custom-models/{model_key}')
        except Exception as e:
            logger.error(f"获取自定义模型失败: {str(e)}")
            return {
                "success": False,
                "message": f"获取自定义模型失败: {str(e)}"
            }
    
    def enable_custom_model(self, model_key: str) -> Dict[str, Any]:
        """启用自定义模型"""
        try:
            return self._make_request('POST', f'/custom-models/{model_key}/enable')
        except Exception as e:
            logger.error(f"启用自定义模型失败: {str(e)}")
            return {
                "success": False,
                "message": f"启用自定义模型失败: {str(e)}"
            }
    
    def disable_custom_model(self, model_key: str) -> Dict[str, Any]:
        """禁用自定义模型"""
        try:
            return self._make_request('POST', f'/custom-models/{model_key}/disable')
        except Exception as e:
            logger.error(f"禁用自定义模型失败: {str(e)}")
            return {
                "success": False,
                "message": f"禁用自定义模型失败: {str(e)}"
            }
    
    def validate_custom_model(self, model_key: str) -> Dict[str, Any]:
        """验证自定义模型"""
        try:
            return self._make_request('POST', f'/custom-models/{model_key}/validate')
        except Exception as e:
            logger.error(f"验证自定义模型失败: {str(e)}")
            return {
                "success": False,
                "message": f"验证自定义模型失败: {str(e)}"
            }
    
    def import_model_from_directory(
        self, 
        source_dir: str, 
        model_key: str, 
        model_name: str
    ) -> Dict[str, Any]:
        """从目录导入模型"""
        try:
            data = {
                "source_dir": source_dir,
                "model_key": model_key,
                "model_name": model_name
            }
            # 使用form data而不是JSON
            response = requests.post(
                f"{self.api_base}/custom-models/import-from-directory",
                data=data
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"从目录导入模型失败: {str(e)}")
            return {
                "success": False,
                "message": f"从目录导入模型失败: {str(e)}"
            }
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取自定义模型统计信息"""
        try:
            return self._make_request('GET', '/custom-models/statistics')
        except Exception as e:
            logger.error(f"获取自定义模型统计信息失败: {str(e)}")
            return {
                "success": False,
                "message": f"获取统计信息失败: {str(e)}",
                "data": {
                    "total_models": 0,
                    "enabled_models": 0,
                    "disabled_models": 0,
                    "type_statistics": {},
                    "total_size_bytes": 0,
                    "total_size_mb": 0
                }
            }
    
    def close(self):
        """关闭客户端连接"""
        if self.session:
            self.session.close()
            logger.info("自定义模型客户端连接已关闭")
