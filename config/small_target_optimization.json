{"optimization_config": {"version": "1.1", "description": "军事目标小尺寸生成优化配置 - 新增Realistic Vision V6.0和专用LoRA", "last_updated": "2024-12-19"}, "recommended_models": {"for_tiny_targets": {"model_key": "realistic-vision-v6", "model_id": "SG161222/Realistic_Vision_V6.0_B1", "target_size_range": "3-5%", "description": "最新版本，极小目标生成效果更佳，构图控制能力显著提升", "optimal_params": {"cfg_scale": 5.5, "steps": 20, "composition_strategy": "ultra_wide"}}, "for_tiny_targets_legacy": {"model_key": "realistic-vision", "model_id": "SG161222/Realistic_Vision_V4.0", "target_size_range": "3-5%", "description": "经典版本，适合极小目标生成，优秀的构图控制", "optimal_params": {"cfg_scale": 6.0, "steps": 25, "composition_strategy": "ultra_wide"}}, "for_small_targets": {"model_key": "dreamlike-photoreal", "model_id": "dreamlike-art/dreamlike-photoreal-2.0", "target_size_range": "5-8%", "description": "照片级真实感，适合小军事目标", "optimal_params": {"cfg_scale": 7.5, "steps": 30, "composition_strategy": "aerial"}}, "for_medium_targets": {"model_key": "stable-diffusion-v1-5", "model_id": "runwayml/stable-diffusion-v1-5", "target_size_range": "8-15%", "description": "标准模型，适合中等尺寸目标", "optimal_params": {"cfg_scale": 9.0, "steps": 35, "composition_strategy": "environmental"}}}, "lora_models": {"tank_lora": {"lora_key": "military-tanks-hyperrealistic", "lora_name": "Military Tanks - Battle Cars - HyperRealistic LoRA", "description": "坦克专用超真实LoRA，提升坦克细节和真实感", "compatible_models": ["realistic-vision-v6", "realistic-vision", "dreamlike-photoreal"], "optimal_weight": 0.7, "target_types": ["坦克", "装甲车", "自行火炮"], "trigger_keywords": ["battle tank", "armored vehicle", "military tank", "combat vehicle"], "enhancement_keywords": ["hyperrealistic tank", "detailed armor plating", "realistic camouflage"]}, "f16_lora": {"lora_key": "f16-fighting-falcon", "lora_name": "F-16 Fighting Falcon LoRA", "description": "F-16战斗机专用LoRA，精确还原F-16外观细节", "compatible_models": ["realistic-vision-v6", "realistic-vision", "dreamlike-photoreal"], "optimal_weight": 0.8, "target_types": ["F-16战机", "战斗机"], "trigger_keywords": ["F-16", "Fighting Falcon", "fighter jet", "military aircraft"], "enhancement_keywords": ["F-16 Fighting Falcon", "realistic fighter jet", "detailed aircraft"]}}, "prompt_strategies": {"ultra_small_targets": {"size_keywords": ["extremely tiny", "barely visible", "distant speck", "miniature"], "composition_keywords": ["ultra wide angle", "satellite view", "extreme aerial", "panoramic"], "environment_keywords": ["vast landscape", "enormous environment", "massive scale"], "negative_keywords": ["close up", "detailed", "large", "prominent", "fills frame"]}, "small_targets": {"size_keywords": ["small", "distant", "tiny", "compact"], "composition_keywords": ["wide shot", "aerial view", "bird's eye", "overhead"], "environment_keywords": ["wide landscape", "expansive scene", "broad view"], "negative_keywords": ["close", "big", "huge", "detailed view", "zoomed in"]}, "medium_targets": {"size_keywords": ["medium sized", "balanced scale", "proportional"], "composition_keywords": ["medium shot", "balanced composition", "clear view"], "environment_keywords": ["environmental context", "scenic background"], "negative_keywords": ["macro", "extreme close up", "giant", "enormous"]}}, "target_specific_settings": {"坦克": {"optimal_aspect_ratio": "1:1", "recommended_size_range": "5-10%", "best_composition": "aerial", "environment_emphasis": "battlefield, urban terrain, open field", "size_modifiers": ["small battle tank", "distant armored vehicle", "compact tank unit"], "recommended_lora": "tank_lora", "lora_weight": 0.7, "enhanced_keywords": ["hyperrealistic tank", "detailed armor plating", "realistic camouflage"]}, "战机": {"optimal_aspect_ratio": "1.6:1", "recommended_size_range": "4-8%", "best_composition": "ultra_wide", "environment_emphasis": "vast sky, endless atmosphere, cloud formations", "size_modifiers": ["small fighter jet", "distant aircraft", "tiny warplane"]}, "F-16战机": {"optimal_aspect_ratio": "1.6:1", "recommended_size_range": "4-8%", "best_composition": "ultra_wide", "environment_emphasis": "vast sky, endless atmosphere, cloud formations", "size_modifiers": ["small F-16 Fighting Falcon", "distant F-16 fighter jet", "tiny F-16 aircraft"], "recommended_lora": "f16_lora", "lora_weight": 0.8, "enhanced_keywords": ["F-16 Fighting Falcon", "realistic F-16", "detailed F-16 fighter"]}, "舰艇": {"optimal_aspect_ratio": "2:1", "recommended_size_range": "6-12%", "best_composition": "aerial", "environment_emphasis": "vast ocean, expansive seascape, maritime environment", "size_modifiers": ["small warship", "distant naval vessel", "compact battleship"]}}, "generation_workflow": {"step1": {"action": "analyze_target_size_requirement", "description": "根据用户设置的目标尺寸比例选择合适的策略"}, "step2": {"action": "select_optimal_model", "description": "根据目标尺寸范围选择最适合的AI模型"}, "step3": {"action": "build_advanced_prompt", "description": "使用高级提示词构建器生成针对小目标优化的提示词"}, "step4": {"action": "apply_optimal_parameters", "description": "应用模型特定的最优生成参数"}, "step5": {"action": "generate_and_validate", "description": "生成图像并验证目标尺寸是否符合预期"}}, "troubleshooting": {"targets_too_large": {"problem": "生成的军事目标仍然太大", "solutions": ["切换到realistic-vision-v6模型（最佳选择）", "切换到realistic-vision模型（经典选择）", "降低目标尺寸比例到3-5%", "使用ultra_wide构图策略", "在负面提示词中添加更多大目标关键词", "增加'extremely distant'等强化词汇", "考虑使用坦克或飞机专用LoRA提升细节"]}, "targets_too_small": {"problem": "生成的军事目标太小难以识别", "solutions": ["提高目标尺寸比例到8-12%", "使用environmental构图策略", "减少'tiny'、'distant'等词汇", "切换到stable-diffusion-v1-5模型"]}, "poor_image_quality": {"problem": "图像质量不佳或目标不清晰", "solutions": ["增加生成步数到30-40", "调整CFG scale到7-9", "使用dreamlike-photoreal模型提高真实感", "在提示词中添加'high resolution'、'sharp focus'"]}}, "performance_metrics": {"target_size_accuracy": {"excellent": "误差 < 1%", "good": "误差 < 2%", "acceptable": "误差 < 3%", "needs_improvement": "误差 >= 3%"}, "generation_speed": {"realistic-vision-v6": "20-30秒/张", "realistic-vision": "25-35秒/张", "dreamlike-photoreal": "20-30秒/张", "stable-diffusion-v1-5": "15-25秒/张"}, "memory_usage": {"realistic-vision-v6": "~6.5GB VRAM", "realistic-vision": "~6GB VRAM", "dreamlike-photoreal": "~5GB VRAM", "stable-diffusion-v1-5": "~4GB VRAM"}}}