#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
编码测试脚本 - 验证UTF-8编码是否正常工作
"""

import sys
import os

# 导入编码配置
sys.path.append('backend')
from app.core.encoding_config import setup_utf8_encoding, setup_logging_encoding

import logging

# 设置编码
setup_utf8_encoding()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

setup_logging_encoding()

logger = logging.getLogger(__name__)

def test_encoding():
    """测试编码功能"""
    print("=" * 50)
    print("编码测试开始")
    print("=" * 50)
    
    # 测试print输出
    print("测试print输出:")
    print("✅ 中文字符显示正常")
    print("✅ 特殊符号: ♠♥♦♣")
    print("✅ 日文: こんにちは")
    print("✅ 韩文: 안녕하세요")
    print("✅ 俄文: Привет")
    
    print("\n" + "-" * 30)
    
    # 测试logger输出
    print("测试logger输出:")
    logger.info("✅ 日志中文显示正常")
    logger.info("✅ 应用启动完成")
    logger.warning("⚠️ 这是一个警告信息")
    logger.error("❌ 这是一个错误信息")
    
    print("\n" + "-" * 30)
    
    # 测试系统信息
    print("系统信息:")
    print(f"操作系统: {sys.platform}")
    print(f"Python版本: {sys.version}")
    print(f"默认编码: {sys.getdefaultencoding()}")
    print(f"文件系统编码: {sys.getfilesystemencoding()}")
    print(f"stdout编码: {sys.stdout.encoding}")
    print(f"stderr编码: {sys.stderr.encoding}")
    
    # 检查环境变量
    print(f"PYTHONIOENCODING: {os.environ.get('PYTHONIOENCODING', '未设置')}")
    print(f"PYTHONUTF8: {os.environ.get('PYTHONUTF8', '未设置')}")
    
    print("\n" + "=" * 50)
    print("编码测试完成 ✅")
    print("如果上述中文字符显示正常，说明编码修复成功！")
    print("=" * 50)

if __name__ == "__main__":
    test_encoding() 