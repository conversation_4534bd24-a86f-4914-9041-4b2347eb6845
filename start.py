#!/usr/bin/env python3
"""
军事目标数据集生成平台启动脚本
"""

import sys
import os
import subprocess
import argparse
import time
import signal
import json
import logging
import platform
import shutil
import urllib.request
import socket
from pathlib import Path
from threading import Thread
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum

# 华为云PyPI镜像地址常量，根据用户要求统一使用
PIP_INDEX_URL = "https://repo.huaweicloud.com/repository/pypi/simple/"
PIP_TRUSTED_HOST = "repo.huaweicloud.com"

# ---------------- 端口检测工具函数 ----------------

def _is_port_in_use(host: str, port: int) -> bool:
    """检查指定 host:port 是否已被占用"""
    import socket
    for family in (socket.AF_INET, socket.AF_INET6):
        try:
            with socket.socket(family, socket.SOCK_STREAM) as sock:
                sock.settimeout(0.5)
                if sock.connect_ex((host, port)) == 0:
                    return True
        except OSError:
            # 某些 family/host 组合可能不支持，忽略
            continue
    return False


def _find_available_port(host: str, start_port: int = 8000, max_tries: int = 100) -> int:
    """从 start_port 开始寻找可用端口，返回找到的端口；若未找到则仍返回 start_port"""
    port = start_port
    for _ in range(max_tries):
        if not _is_port_in_use(host, port):
            return port
        port += 1
    return start_port

class CheckStatus(Enum):
    """检测状态枚举"""
    PASS = "PASS"
    WARNING = "WARNING"
    FAIL = "FAIL"
    FIXED = "FIXED"


class FixType(Enum):
    """修复类型枚举"""
    AUTO_SAFE = "AUTO_SAFE"        # 安全的自动修复
    AUTO_RISKY = "AUTO_RISKY"      # 有风险的自动修复
    MANUAL = "MANUAL"              # 需要手动修复
    IMPOSSIBLE = "IMPOSSIBLE"      # 无法修复


@dataclass
class FixAction:
    """修复操作数据类"""
    name: str
    description: str
    fix_type: FixType
    risk_level: str  # "LOW", "MEDIUM", "HIGH"
    command: Optional[str] = None
    function: Optional[callable] = None
    rollback_function: Optional[callable] = None


@dataclass
class CheckResult:
    """检测结果数据类"""
    name: str
    status: CheckStatus
    message: str
    details: Optional[str] = None
    fix_suggestion: Optional[str] = None
    fix_actions: Optional[List[FixAction]] = None


class EnvironmentChecker:
    """环境检测和修复类"""

    def __init__(self, silent_mode: bool = False, interactive_mode: bool = True, preview_mode: bool = False):
        self.silent_mode = silent_mode
        self.interactive_mode = interactive_mode
        self.preview_mode = preview_mode
        self.results: List[CheckResult] = []
        self.fix_history: List[Dict[str, Any]] = []
        self.setup_logging()

    def setup_logging(self):
        """设置日志"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)

        log_file = log_dir / "environment_check.log"

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler() if not self.silent_mode else logging.NullHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def log_and_print(self, message: str, level: str = "INFO"):
        """记录日志并打印消息"""
        if level == "INFO":
            self.logger.info(message)
        elif level == "WARNING":
            self.logger.warning(message)
        elif level == "ERROR":
            self.logger.error(message)

        if not self.silent_mode:
            print(f"[{level}] {message}")

    def add_result(self, result: CheckResult):
        """添加检测结果"""
        self.results.append(result)
        status_symbol = {
            CheckStatus.PASS: "✓",
            CheckStatus.WARNING: "⚠",
            CheckStatus.FAIL: "✗",
            CheckStatus.FIXED: "🔧"
        }

        symbol = status_symbol.get(result.status, "?")
        self.log_and_print(f"{symbol} {result.name}: {result.message}")

        if result.details:
            self.log_and_print(f"   详情: {result.details}")

        if result.fix_suggestion and result.status in [CheckStatus.FAIL, CheckStatus.WARNING]:
            self.log_and_print(f"   建议: {result.fix_suggestion}")

    def check_python_version(self) -> CheckResult:
        """检查Python版本"""
        try:
            version = sys.version_info
            required_version = (3, 8)

            if version >= required_version:
                return CheckResult(
                    name="Python版本检查",
                    status=CheckStatus.PASS,
                    message=f"Python {version.major}.{version.minor}.{version.micro} 符合要求"
                )
            else:
                return CheckResult(
                    name="Python版本检查",
                    status=CheckStatus.FAIL,
                    message=f"Python {version.major}.{version.minor}.{version.micro} 版本过低",
                    details=f"需要Python {required_version[0]}.{required_version[1]}+",
                    fix_suggestion="请升级到Python 3.8或更高版本"
                )
        except Exception as e:
            return CheckResult(
                name="Python版本检查",
                status=CheckStatus.FAIL,
                message="无法检测Python版本",
                details=str(e)
            )

    def check_system_requirements(self) -> CheckResult:
        """检查系统要求"""
        try:
            import psutil

            # 检查内存
            memory = psutil.virtual_memory()
            memory_gb = memory.total / (1024**3)

            if memory_gb < 8:
                return CheckResult(
                    name="系统内存检查",
                    status=CheckStatus.WARNING,
                    message=f"系统内存 {memory_gb:.1f}GB 可能不足",
                    details="推荐8GB以上内存以获得最佳性能",
                    fix_suggestion="考虑增加系统内存或关闭其他应用程序"
                )
            else:
                return CheckResult(
                    name="系统内存检查",
                    status=CheckStatus.PASS,
                    message=f"系统内存 {memory_gb:.1f}GB 充足"
                )
        except ImportError:
            return CheckResult(
                name="系统内存检查",
                status=CheckStatus.WARNING,
                message="无法检查系统内存",
                details="psutil包未安装",
                fix_suggestion="运行: pip install psutil"
            )
        except Exception as e:
            return CheckResult(
                name="系统内存检查",
                status=CheckStatus.WARNING,
                message="系统内存检查失败",
                details=str(e)
            )

    def check_gpu_availability(self) -> CheckResult:
        """检查GPU可用性"""
        try:
            import torch

            if torch.cuda.is_available():
                gpu_count = torch.cuda.device_count()
                gpu_name = torch.cuda.get_device_name(0) if gpu_count > 0 else "Unknown"

                return CheckResult(
                    name="GPU检查",
                    status=CheckStatus.PASS,
                    message=f"检测到 {gpu_count} 个GPU: {gpu_name}",
                    details=f"CUDA版本: {torch.version.cuda}"
                )
            else:
                return CheckResult(
                    name="GPU检查",
                    status=CheckStatus.WARNING,
                    message="未检测到可用的GPU",
                    details="将使用CPU模式，性能可能较慢",
                    fix_suggestion="安装NVIDIA GPU驱动和CUDA工具包以获得更好性能"
                )
        except ImportError:
            return CheckResult(
                name="GPU检查",
                status=CheckStatus.WARNING,
                message="无法检查GPU状态",
                details="PyTorch未安装",
                fix_suggestion="安装PyTorch以启用GPU支持"
            )
        except Exception as e:
            return CheckResult(
                name="GPU检查",
                status=CheckStatus.WARNING,
                message="GPU检查失败",
                details=str(e)
            )

    def check_dependencies(self) -> List[CheckResult]:
        """检查依赖包"""
        results = []

        # 核心依赖包
        core_packages = {
            'fastapi': '0.104.1',
            'uvicorn': '0.24.0',
            'pydantic': '2.4.0',
            'requests': '2.31.0',
            'Pillow': '10.0.0',
            'numpy': '1.24.0'
        }

        # AI/ML依赖包
        ai_packages = {
            'torch': '2.0.0',
            'torchvision': '0.15.0',
            'diffusers': '0.21.0',
            'transformers': '4.25.0'
        }

        # 前端依赖包
        frontend_packages = {
            'PyQt6': '6.5.0'
        }

        # 检查核心依赖
        results.append(self._check_package_group("核心依赖", core_packages, critical=True))

        # 检查AI/ML依赖
        results.append(self._check_package_group("AI/ML依赖", ai_packages, critical=True))

        # 检查前端依赖
        results.append(self._check_package_group("前端依赖", frontend_packages, critical=False))

        return results

    def _check_package_group(self, group_name: str, packages: Dict[str, str], critical: bool = True) -> CheckResult:
        """检查包组"""
        missing_packages = []
        outdated_packages = []

        for package_name, min_version in packages.items():
            try:
                if package_name == 'PyQt6':
                    # PyQt6特殊处理
                    from PyQt6 import QtCore
                    installed_version = QtCore.PYQT_VERSION_STR
                elif package_name == 'Pillow':
                    # Pillow特殊处理
                    import PIL
                    installed_version = PIL.__version__
                else:
                    module = __import__(package_name.lower().replace('-', '_'))
                    installed_version = getattr(module, '__version__', 'unknown')

                if installed_version == 'unknown':
                    outdated_packages.append(package_name)
                elif self._compare_versions(installed_version, min_version) < 0:
                    outdated_packages.append(package_name)

            except ImportError:
                missing_packages.append(package_name)
            except Exception as e:
                missing_packages.append(package_name)

        # 创建修复操作
        fix_actions = self.create_fix_actions_for_dependencies(missing_packages, outdated_packages)

        if missing_packages:
            status = CheckStatus.FAIL if critical else CheckStatus.WARNING
            return CheckResult(
                name=f"{group_name}检查",
                status=status,
                message=f"缺少 {len(missing_packages)} 个包",
                details=f"缺少: {', '.join(missing_packages)}",
                fix_suggestion=f"运行自动修复或手动安装: pip install {' '.join(missing_packages)}",
                fix_actions=fix_actions
            )
        elif outdated_packages:
            return CheckResult(
                name=f"{group_name}检查",
                status=CheckStatus.WARNING,
                message=f"{len(outdated_packages)} 个包版本过低",
                details=f"过低: {', '.join(outdated_packages)}",
                fix_suggestion="运行自动修复或手动升级包",
                fix_actions=fix_actions
            )
        else:
            return CheckResult(
                name=f"{group_name}检查",
                status=CheckStatus.PASS,
                message="所有包已正确安装"
            )

    def _compare_versions(self, version1: str, version2: str) -> int:
        """比较版本号"""
        try:
            from packaging import version
            return -1 if version.parse(version1) < version.parse(version2) else 0
        except ImportError:
            # 简单的版本比较
            v1_parts = [int(x) for x in version1.split('.') if x.isdigit()]
            v2_parts = [int(x) for x in version2.split('.') if x.isdigit()]

            for i in range(max(len(v1_parts), len(v2_parts))):
                v1_part = v1_parts[i] if i < len(v1_parts) else 0
                v2_part = v2_parts[i] if i < len(v2_parts) else 0

                if v1_part < v2_part:
                    return -1
                elif v1_part > v2_part:
                    return 1
            return 0

    def create_fix_actions_for_dependencies(self, missing_packages: List[str], outdated_packages: List[str]) -> List[FixAction]:
        """为依赖问题创建修复操作"""
        actions = []

        if missing_packages:
            actions.append(FixAction(
                name="安装缺失的依赖包",
                description=f"安装 {len(missing_packages)} 个缺失的包: {', '.join(missing_packages[:3])}{'...' if len(missing_packages) > 3 else ''}",
                fix_type=FixType.AUTO_RISKY,
                risk_level="MEDIUM",
                function=lambda: self._install_packages(missing_packages)
            ))

        if outdated_packages:
            actions.append(FixAction(
                name="升级过时的依赖包",
                description=f"升级 {len(outdated_packages)} 个过时的包",
                fix_type=FixType.AUTO_RISKY,
                risk_level="MEDIUM",
                function=lambda: self._upgrade_packages(outdated_packages)
            ))

        return actions

    def create_fix_actions_for_directories(self, missing_dirs: List[str]) -> List[FixAction]:
        """为目录问题创建修复操作"""
        if not missing_dirs:
            return []

        return [FixAction(
            name="创建缺失的目录",
            description=f"创建 {len(missing_dirs)} 个缺失的目录: {', '.join(missing_dirs)}",
            fix_type=FixType.AUTO_SAFE,
            risk_level="LOW",
            function=lambda: self._create_directories(missing_dirs),
            rollback_function=lambda: self._remove_directories(missing_dirs)
        )]

    def create_fix_actions_for_config_files(self, missing_files: List[str]) -> List[FixAction]:
        """为配置文件问题创建修复操作"""
        actions = []

        for file_path in missing_files:
            if file_path.endswith('.json'):
                actions.append(FixAction(
                    name=f"创建配置文件 {file_path}",
                    description=f"生成默认的配置文件: {file_path}",
                    fix_type=FixType.AUTO_SAFE,
                    risk_level="LOW",
                    function=lambda f=file_path: self._create_default_config_file(f)
                ))
            else:
                actions.append(FixAction(
                    name=f"修复文件 {file_path}",
                    description=f"需要手动检查和修复文件: {file_path}",
                    fix_type=FixType.MANUAL,
                    risk_level="HIGH"
                ))

        return actions

    def create_fix_actions_for_environment(self, env_issues: List[str]) -> List[FixAction]:
        """为环境变量问题创建修复操作"""
        if not env_issues:
            return []

        return [FixAction(
            name="修复环境变量",
            description="设置正确的编码环境变量",
            fix_type=FixType.AUTO_SAFE,
            risk_level="LOW",
            function=lambda: self._fix_environment_variables()
        )]

    def check_directories_and_files(self) -> List[CheckResult]:
        """检查必要的目录和文件"""
        results = []

        # 必要的目录
        required_dirs = [
            "backend",
            "frontend",
            "scripts",
            "config",
            "data",
            "data/uploads",
            "data/generated",
            "data/datasets",
            "data/temp",
            "logs",
            "models"
        ]

        # 必要的文件
        required_files = [
            "backend/requirements.txt",
            "frontend/requirements.txt",
            "scripts/start_backend.py",
            "scripts/start_frontend.py",
            "config/auto_dataset_config.json"
        ]

        # 检查目录
        missing_dirs = []
        for dir_path in required_dirs:
            path = Path(dir_path)
            if not path.exists():
                missing_dirs.append(dir_path)

        # 创建目录修复操作
        dir_fix_actions = self.create_fix_actions_for_directories(missing_dirs)

        if missing_dirs:
            results.append(CheckResult(
                name="目录结构检查",
                status=CheckStatus.WARNING,
                message=f"缺少 {len(missing_dirs)} 个目录",
                details=f"缺少: {', '.join(missing_dirs)}",
                fix_suggestion="运行自动修复创建缺失目录",
                fix_actions=dir_fix_actions
            ))
        else:
            results.append(CheckResult(
                name="目录结构检查",
                status=CheckStatus.PASS,
                message="所有必要目录存在"
            ))

        # 检查文件
        missing_files = []
        for file_path in required_files:
            path = Path(file_path)
            if not path.exists():
                missing_files.append(file_path)

        # 创建文件修复操作
        file_fix_actions = self.create_fix_actions_for_config_files(missing_files)

        if missing_files:
            results.append(CheckResult(
                name="必要文件检查",
                status=CheckStatus.FAIL,
                message=f"缺少 {len(missing_files)} 个关键文件",
                details=f"缺少: {', '.join(missing_files)}",
                fix_suggestion="检查项目完整性，重新下载或克隆项目",
                fix_actions=file_fix_actions
            ))
        else:
            results.append(CheckResult(
                name="必要文件检查",
                status=CheckStatus.PASS,
                message="所有必要文件存在"
            ))

        return results

    def check_network_connectivity(self) -> List[CheckResult]:
        """检查网络连接"""
        results = []

        # 检查基本网络连接
        try:
            socket.create_connection(("8.8.8.8", 53), timeout=3)
            results.append(CheckResult(
                name="网络连接检查",
                status=CheckStatus.PASS,
                message="网络连接正常"
            ))
        except OSError:
            results.append(CheckResult(
                name="网络连接检查",
                status=CheckStatus.WARNING,
                message="网络连接异常",
                details="无法连接到外部网络",
                fix_suggestion="检查网络设置和防火墙配置"
            ))

        # 检查PyPI连接
        try:
            urllib.request.urlopen("https://pypi.org", timeout=5)
            results.append(CheckResult(
                name="PyPI连接检查",
                status=CheckStatus.PASS,
                message="PyPI连接正常"
            ))
        except Exception:
            results.append(CheckResult(
                name="PyPI连接检查",
                status=CheckStatus.WARNING,
                message="PyPI连接异常",
                details="可能影响包安装",
                fix_suggestion="检查网络或使用国内镜像源"
            ))

        # 检查Hugging Face连接
        try:
            urllib.request.urlopen("https://huggingface.co", timeout=5)
            results.append(CheckResult(
                name="Hugging Face连接检查",
                status=CheckStatus.PASS,
                message="Hugging Face连接正常"
            ))
        except Exception:
            results.append(CheckResult(
                name="Hugging Face连接检查",
                status=CheckStatus.WARNING,
                message="Hugging Face连接异常",
                details="可能影响模型下载",
                fix_suggestion="检查网络或配置镜像源"
            ))

        return results

    def check_environment_variables(self) -> CheckResult:
        """检查环境变量"""
        issues = []

        # 检查编码相关环境变量
        encoding_vars = {
            'PYTHONIOENCODING': 'utf-8',
            'PYTHONUTF8': '1'
        }

        for var, expected in encoding_vars.items():
            actual = os.environ.get(var)
            if actual != expected:
                issues.append(f"{var}={actual} (期望: {expected})")

        # 创建环境变量修复操作
        env_fix_actions = self.create_fix_actions_for_environment(issues)

        if issues:
            return CheckResult(
                name="环境变量检查",
                status=CheckStatus.WARNING,
                message=f"发现 {len(issues)} 个环境变量问题",
                details="; ".join(issues),
                fix_suggestion="运行自动修复设置正确的环境变量",
                fix_actions=env_fix_actions
            )
        else:
            return CheckResult(
                name="环境变量检查",
                status=CheckStatus.PASS,
                message="环境变量配置正确"
            )

    def run_all_checks(self) -> bool:
        """运行所有检测"""
        self.log_and_print("开始环境检测...")
        self.log_and_print("=" * 50)

        # 基础检测
        self.add_result(self.check_python_version())
        self.add_result(self.check_system_requirements())
        self.add_result(self.check_gpu_availability())

        # 依赖检测
        dependency_results = self.check_dependencies()
        for result in dependency_results:
            self.add_result(result)

        # 文件和目录检测
        file_results = self.check_directories_and_files()
        for result in file_results:
            self.add_result(result)

        # 网络检测
        network_results = self.check_network_connectivity()
        for result in network_results:
            self.add_result(result)

        # 环境变量检测
        self.add_result(self.check_environment_variables())

        # 统计结果
        pass_count = sum(1 for r in self.results if r.status == CheckStatus.PASS)
        warning_count = sum(1 for r in self.results if r.status == CheckStatus.WARNING)
        fail_count = sum(1 for r in self.results if r.status == CheckStatus.FAIL)

        self.log_and_print("=" * 50)
        self.log_and_print(f"检测完成: {pass_count} 通过, {warning_count} 警告, {fail_count} 失败")

        # 如果有失败项，返回False
        if fail_count > 0:
            self.log_and_print("发现严重问题，建议运行修复功能", "ERROR")
            return False
        elif warning_count > 0:
            self.log_and_print("发现一些警告，建议运行修复功能", "WARNING")

        return True

    def auto_fix_issues(self, batch_mode: bool = True) -> bool:
        """增强的自动修复问题功能"""
        self.log_and_print("开始环境问题修复...")
        self.log_and_print("=" * 50)

        # 收集所有需要修复的问题
        fix_plan = self._create_fix_plan()

        if not fix_plan:
            self.log_and_print("没有发现可以自动修复的问题")
            return False

        # 显示修复计划
        self._display_fix_plan(fix_plan)

        # 用户确认
        if not self._confirm_fix_execution(fix_plan):
            self.log_and_print("用户取消了修复操作")
            return False

        # 执行修复
        if batch_mode:
            return self._execute_batch_fix(fix_plan)
        else:
            return self._execute_interactive_fix(fix_plan)

    def _create_fix_plan(self) -> List[FixAction]:
        """创建修复计划"""
        fix_plan = []

        for result in self.results:
            if result.status in [CheckStatus.FAIL, CheckStatus.WARNING] and result.fix_actions:
                for action in result.fix_actions:
                    if action.fix_type in [FixType.AUTO_SAFE, FixType.AUTO_RISKY]:
                        fix_plan.append(action)

        # 按风险级别和类型排序
        fix_plan.sort(key=lambda x: (
            0 if x.fix_type == FixType.AUTO_SAFE else 1,
            0 if x.risk_level == "LOW" else 1 if x.risk_level == "MEDIUM" else 2
        ))

        return fix_plan

    def _display_fix_plan(self, fix_plan: List[FixAction]):
        """显示修复计划"""
        if self.preview_mode:
            self.log_and_print("修复计划预览（不会实际执行）:")
        else:
            self.log_and_print("修复计划:")

        self.log_and_print("-" * 50)

        for i, action in enumerate(fix_plan, 1):
            risk_symbol = {
                "LOW": "🟢",
                "MEDIUM": "🟡",
                "HIGH": "🔴"
            }.get(action.risk_level, "⚪")

            self.log_and_print(f"{i}. {risk_symbol} {action.name}")
            self.log_and_print(f"   描述: {action.description}")
            self.log_and_print(f"   风险: {action.risk_level}")
            if action.fix_type == FixType.AUTO_RISKY:
                self.log_and_print("   注意: 此操作可能影响系统配置")
            self.log_and_print("")

        self.log_and_print("-" * 50)

    def _confirm_fix_execution(self, fix_plan: List[FixAction]) -> bool:
        """确认修复执行"""
        if self.preview_mode:
            return False

        if not self.interactive_mode:
            # 非交互模式下，只执行安全的修复
            safe_actions = [a for a in fix_plan if a.fix_type == FixType.AUTO_SAFE]
            return len(safe_actions) > 0

        if self.silent_mode:
            return True

        # 检查是否有高风险操作
        risky_actions = [a for a in fix_plan if a.risk_level in ["MEDIUM", "HIGH"]]

        if risky_actions:
            self.log_and_print(f"警告: 发现 {len(risky_actions)} 个中高风险操作")
            response = input("是否继续执行修复？(y/N): ").strip().lower()
            return response in ['y', 'yes', '是']
        else:
            response = input(f"是否执行 {len(fix_plan)} 个修复操作？(Y/n): ").strip().lower()
            return response in ['', 'y', 'yes', '是']

    def _execute_batch_fix(self, fix_plan: List[FixAction]) -> bool:
        """批量执行修复"""
        self.log_and_print("开始批量修复...")

        success_count = 0
        failed_actions = []

        for i, action in enumerate(fix_plan, 1):
            self.log_and_print(f"[{i}/{len(fix_plan)}] 执行: {action.name}")

            try:
                if action.function:
                    result = action.function()
                    if result:
                        success_count += 1
                        self._record_fix_history(action, True)
                        self.log_and_print(f"  ✓ 成功")
                    else:
                        failed_actions.append(action)
                        self._record_fix_history(action, False)
                        self.log_and_print(f"  ✗ 失败")
                else:
                    self.log_and_print(f"  ! 跳过（需要手动处理）")

            except Exception as e:
                failed_actions.append(action)
                self._record_fix_history(action, False, str(e))
                self.log_and_print(f"  ✗ 异常: {str(e)}", "ERROR")

        self.log_and_print("=" * 50)
        self.log_and_print(f"批量修复完成: {success_count} 成功, {len(failed_actions)} 失败")

        if failed_actions:
            self.log_and_print("失败的操作:")
            for action in failed_actions:
                self.log_and_print(f"  - {action.name}")

        return success_count > 0

    def _execute_interactive_fix(self, fix_plan: List[FixAction]) -> bool:
        """交互式执行修复"""
        self.log_and_print("开始交互式修复...")

        success_count = 0

        for i, action in enumerate(fix_plan, 1):
            self.log_and_print(f"\n[{i}/{len(fix_plan)}] {action.name}")
            self.log_and_print(f"描述: {action.description}")
            self.log_and_print(f"风险级别: {action.risk_level}")

            if not self.silent_mode:
                response = input("是否执行此操作？(Y/n/s=跳过剩余): ").strip().lower()
                if response == 's':
                    self.log_and_print("跳过剩余操作")
                    break
                elif response in ['n', 'no', '否']:
                    self.log_and_print("跳过此操作")
                    continue

            try:
                if action.function:
                    result = action.function()
                    if result:
                        success_count += 1
                        self._record_fix_history(action, True)
                        self.log_and_print("✓ 执行成功")
                    else:
                        self._record_fix_history(action, False)
                        self.log_and_print("✗ 执行失败")

            except Exception as e:
                self._record_fix_history(action, False, str(e))
                self.log_and_print(f"✗ 执行异常: {str(e)}", "ERROR")

        self.log_and_print("=" * 50)
        self.log_and_print(f"交互式修复完成: {success_count} 个操作成功")

        return success_count > 0

    def _record_fix_history(self, action: FixAction, success: bool, error: str = None):
        """记录修复历史"""
        record = {
            "timestamp": time.time(),
            "action_name": action.name,
            "description": action.description,
            "risk_level": action.risk_level,
            "success": success,
            "error": error
        }
        self.fix_history.append(record)

        # 记录到日志文件
        status = "SUCCESS" if success else "FAILED"
        self.logger.info(f"FIX_HISTORY: {status} - {action.name} - {action.description}")
        if error:
            self.logger.error(f"FIX_ERROR: {action.name} - {error}")

    def _fix_directories(self) -> bool:
        """修复目录结构"""
        required_dirs = [
            "data/uploads", "data/generated", "data/datasets",
            "data/temp", "logs", "models"
        ]

        fixed = False
        for dir_path in required_dirs:
            path = Path(dir_path)
            if not path.exists():
                try:
                    path.mkdir(parents=True, exist_ok=True)
                    self.log_and_print(f"创建目录: {dir_path}")
                    fixed = True
                except Exception as e:
                    self.log_and_print(f"创建目录失败 {dir_path}: {e}", "ERROR")

        return fixed

    def _create_directories(self, directories: List[str]) -> bool:
        """创建目录"""
        success = True
        for dir_path in directories:
            try:
                path = Path(dir_path)
                path.mkdir(parents=True, exist_ok=True)
                self.log_and_print(f"创建目录: {dir_path}")
            except Exception as e:
                self.log_and_print(f"创建目录失败 {dir_path}: {e}", "ERROR")
                success = False
        return success

    def _remove_directories(self, directories: List[str]) -> bool:
        """删除目录（回滚操作）"""
        success = True
        for dir_path in directories:
            try:
                path = Path(dir_path)
                if path.exists() and path.is_dir():
                    # 只删除空目录，避免误删有内容的目录
                    if not any(path.iterdir()):
                        path.rmdir()
                        self.log_and_print(f"回滚删除目录: {dir_path}")
                    else:
                        self.log_and_print(f"目录非空，跳过删除: {dir_path}")
            except Exception as e:
                self.log_and_print(f"删除目录失败 {dir_path}: {e}", "ERROR")
                success = False
        return success

    def _install_packages(self, packages: List[str]) -> bool:
        """安装包"""
        try:
            self.log_and_print(f"安装包: {', '.join(packages)}")

            # 设置环境变量
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONUTF8'] = '1'

            # 构建安装命令
            cmd = [sys.executable, "-m", "pip", "install", "-i", PIP_INDEX_URL, "--trusted-host", PIP_TRUSTED_HOST] + packages

            # 添加常用的pip参数
            cmd.extend(["--upgrade", "--no-cache-dir"])

            result = subprocess.run(
                cmd,
                check=True,
                capture_output=self.silent_mode,
                env=env,
                timeout=300  # 5分钟超时
            )

            self.log_and_print("包安装完成")
            return True

        except subprocess.TimeoutExpired:
            self.log_and_print("包安装超时", "ERROR")
            return False
        except subprocess.CalledProcessError as e:
            self.log_and_print(f"包安装失败: {e}", "ERROR")
            return False
        except Exception as e:
            self.log_and_print(f"包安装异常: {e}", "ERROR")
            return False

    def _upgrade_packages(self, packages: List[str]) -> bool:
        """升级包"""
        try:
            self.log_and_print(f"升级包: {', '.join(packages)}")

            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONUTF8'] = '1'

            cmd = [sys.executable, "-m", "pip", "install", "--upgrade", "-i", PIP_INDEX_URL, "--trusted-host", PIP_TRUSTED_HOST] + packages

            result = subprocess.run(
                cmd,
                check=True,
                capture_output=self.silent_mode,
                env=env,
                timeout=300
            )

            self.log_and_print("包升级完成")
            return True

        except Exception as e:
            self.log_and_print(f"包升级失败: {e}", "ERROR")
            return False

    def _create_default_config_file(self, file_path: str) -> bool:
        """创建默认配置文件"""
        try:
            path = Path(file_path)

            # 确保目录存在
            path.parent.mkdir(parents=True, exist_ok=True)

            # 根据文件类型创建默认内容
            if file_path.endswith('auto_dataset_config.json'):
                default_config = {
                    "auto_add_to_dataset": True,
                    "default_dataset_name": "AI_Generated_Images",
                    "dataset_naming_format": "timestamp",
                    "create_daily_datasets": False,
                    "max_images_per_dataset": 1000,
                    "auto_organize_by_target": False
                }
                with open(path, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, indent=2, ensure_ascii=False)
            else:
                # 创建空的JSON文件
                with open(path, 'w', encoding='utf-8') as f:
                    json.dump({}, f, indent=2)

            self.log_and_print(f"创建配置文件: {file_path}")
            return True

        except Exception as e:
            self.log_and_print(f"创建配置文件失败 {file_path}: {e}", "ERROR")
            return False

    def _fix_environment_variables(self) -> bool:
        """修复环境变量（仅对当前进程）"""
        encoding_vars = {
            'PYTHONIOENCODING': 'utf-8',
            'PYTHONUTF8': '1'
        }

        fixed = False
        for var, value in encoding_vars.items():
            if os.environ.get(var) != value:
                os.environ[var] = value
                self.log_and_print(f"设置环境变量: {var}={value}")
                fixed = True

        return fixed

    def _fix_dependencies(self) -> bool:
        """修复依赖包"""
        if not self.interactive_mode:
            self.log_and_print("非交互模式，跳过依赖包安装")
            return False

        # 询问用户是否安装依赖
        if not self.silent_mode:
            response = input("是否自动安装缺失的依赖包？(y/N): ").strip().lower()
            if response not in ['y', 'yes', '是']:
                self.log_and_print("跳过依赖包安装")
                return False

        try:
            # 安装核心依赖
            self.log_and_print("安装核心依赖...")
            subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", "backend/requirements.txt", "-i", PIP_INDEX_URL, "--trusted-host", PIP_TRUSTED_HOST
            ], check=True, capture_output=self.silent_mode)

            # 安装前端依赖
            self.log_and_print("安装前端依赖...")
            subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", "frontend/requirements.txt", "-i", PIP_INDEX_URL, "--trusted-host", PIP_TRUSTED_HOST
            ], check=True, capture_output=self.silent_mode)

            self.log_and_print("依赖包安装完成")
            return True

        except subprocess.CalledProcessError as e:
            self.log_and_print(f"依赖包安装失败: {e}", "ERROR")
            return False
        except Exception as e:
            self.log_and_print(f"依赖包安装异常: {e}", "ERROR")
            return False


def start_backend(host="localhost", port=8000, reload=False, skip_install_check=False):
    """启动后端服务"""
    backend_script = Path(__file__).parent / "scripts" / "start_backend.py"
    
    cmd = [sys.executable, str(backend_script), "--host", host, "--port", str(port)]
    if reload:
        cmd.append("--reload")
    if skip_install_check:
        cmd.append("--skip-install-check")
    
    print(f"启动后端服务: {host}:{port}")
    return subprocess.Popen(cmd)

def start_frontend(backend_url="http://localhost:8000", debug=False):
    """启动前端应用"""
    frontend_script = Path(__file__).parent / "scripts" / "start_frontend.py"
    
    cmd = [sys.executable, str(frontend_script), "--backend-url", backend_url]
    if debug:
        cmd.append("--debug")
    
    print(f"启动前端应用，连接到: {backend_url}")
    return subprocess.Popen(cmd)

def wait_for_backend(host, port, timeout=30):
    """等待后端服务启动"""
    import requests
    
    url = f"http://{host}:{port}/health"
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        try:
            response = requests.get(url, timeout=1)
            if response.status_code == 200:
                print("后端服务已就绪")
                return True
        except:
            pass
        time.sleep(1)
    
    print("等待后端服务超时")
    return False

def main():
    parser = argparse.ArgumentParser(description="军事目标数据集生成平台")
    parser.add_argument("--mode", choices=["all", "backend", "frontend"],
                       default="all", help="启动模式")
    parser.add_argument("--host", default="localhost", help="后端服务主机")
    parser.add_argument("--port", type=int, default=8000, help="后端服务端口")
    parser.add_argument("--reload", action="store_true", help="后端自动重载")
    parser.add_argument("--debug", action="store_true", help="调试模式")

    # 环境检测相关参数
    parser.add_argument("--env-check-only", action="store_true", help="仅运行环境检测")
    parser.add_argument("--auto-fix", action="store_true", help="自动修复环境问题 (与 --fix 效果类似)")
    parser.add_argument("--skip-env-check", action="store_true", default=True, help=argparse.SUPPRESS)
    parser.add_argument("--fix", "-f", action="store_true", help="检测并修复环境后启动")
    parser.add_argument("--silent", action="store_true", help="静默模式")
    parser.add_argument("--non-interactive", action="store_true", help="非交互模式")
    parser.add_argument("--preview-fix", action="store_true", help="预览修复操作（不实际执行）")
    parser.add_argument("--interactive-fix", action="store_true", help="交互式修复模式")
    parser.add_argument("--force-fix", action="store_true", help="强制执行所有修复操作")

    args = parser.parse_args()

    # 处理 --fix 逻辑：若指定 --fix/-f，则执行环境检测与修复
    if args.fix:
        args.skip_env_check = False
        args.auto_fix = True
    else:
        args.skip_env_check = True

    # 环境检测
    if not args.skip_env_check:
        print("=" * 60)
        print("军事目标数据集生成平台 - 环境检测")
        print("=" * 60)

        checker = EnvironmentChecker(
            silent_mode=args.silent,
            interactive_mode=not args.non_interactive,
            preview_mode=args.preview_fix
        )

        # 运行环境检测
        env_ok = checker.run_all_checks()

        # 如果仅运行环境检测
        if args.env_check_only:
            if args.auto_fix and not env_ok:
                # 确定修复模式
                batch_mode = not args.interactive_fix

                if args.force_fix:
                    # 强制修复模式，跳过用户确认
                    original_interactive = checker.interactive_mode
                    checker.interactive_mode = False

                success = checker.auto_fix_issues(batch_mode=batch_mode)

                if args.force_fix:
                    checker.interactive_mode = original_interactive

                if success:
                    # 重新检测
                    print("\n" + "=" * 60)
                    print("🔄 重新检测环境")
                    print("=" * 60)
                    checker.results.clear()
                    env_ok = checker.run_all_checks()
                else:
                    print("\n修复操作未成功完成")

            return 0 if env_ok else 1

        # 如果环境检测失败
        if not env_ok:
            if args.auto_fix:
                print("\n" + "=" * 60)
                print("自动修复环境问题")
                print("=" * 60)

                # 确定修复模式
                batch_mode = not args.interactive_fix

                if args.force_fix:
                    # 强制修复模式
                    original_interactive = checker.interactive_mode
                    checker.interactive_mode = False

                success = checker.auto_fix_issues(batch_mode=batch_mode)

                if args.force_fix:
                    checker.interactive_mode = original_interactive

                if success:
                    # 重新检测
                    print("\n" + "=" * 60)
                    print("重新检测环境")
                    print("=" * 60)
                    checker.results.clear()
                    env_ok = checker.run_all_checks()

                    if not env_ok:
                        print("\n自动修复后仍有问题，请手动解决")
                        return 1
                else:
                    print("\n自动修复失败，请手动解决问题")
                    return 1
            else:
                if not args.silent and not args.non_interactive:
                    response = input("\n发现环境问题，是否继续启动？(y/N): ").strip().lower()
                    if response not in ['y', 'yes', '是']:
                        print("启动已取消")
                        return 1
                else:
                    print("\n发现环境问题，启动已取消")
                    return 1

        print("\n环境检测通过，开始启动服务...")
        print("=" * 60)

    # 原有的启动逻辑
    processes = []

    try:
        if args.mode in ["all", "backend"]:
            # 如果端口被占用则自动寻找可用端口
            if _is_port_in_use(args.host, args.port):
                print(f"警告: 端口 {args.port} 已被占用，自动查找可用端口...")
                free_port = _find_available_port(args.host, args.port)
                if free_port != args.port:
                    print(f"成功: 使用新的端口 {free_port}")
                    args.port = free_port
                else:
                    print("❌ 无可用端口，启动失败")
                    return 1

            # 启动后端服务
            print(f"启动后端服务: {args.host}:{args.port}")
            backend_process = start_backend(args.host, args.port, args.reload, skip_install_check=args.skip_env_check)
            processes.append(backend_process)

            if args.mode == "all":
                # 等待后端服务启动
                print("等待后端服务启动...")
                if not wait_for_backend(args.host, args.port):
                    print("后端服务启动失败")
                    return 1
                print("后端服务启动成功")

        if args.mode in ["all", "frontend"]:
            # 启动前端应用
            backend_url = f"http://{args.host}:{args.port}"
            print(f"启动前端应用，连接到: {backend_url}")
            frontend_process = start_frontend(backend_url, args.debug)
            processes.append(frontend_process)
            print("前端应用启动成功")

        if args.mode == "backend":
            print(f"后端服务运行在: http://{args.host}:{args.port}")
            print(f"API文档: http://{args.host}:{args.port}/docs")
            print("按 Ctrl+C 停止服务")
        elif args.mode == "all":
            print("\n" + "=" * 60)
            print("所有服务启动完成！")
            print(f"后端服务: http://{args.host}:{args.port}")
            print(f"API文档: http://{args.host}:{args.port}/docs")
            print("前端应用已启动")
            print("按 Ctrl+C 停止所有服务")
            print("=" * 60)

        # 等待进程结束
        for process in processes:
            process.wait()

    except KeyboardInterrupt:
        print("\n正在停止服务...")

        # 终止所有进程
        for process in processes:
            if process.poll() is None:
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()

        print("所有服务已停止")

    except Exception as e:
        print(f"启动失败: {str(e)}")
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main()) 