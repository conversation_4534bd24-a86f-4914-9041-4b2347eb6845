"""
JSON描述文件模板管理器
为不同类型的图片生成标准化的JSON描述
"""

from datetime import datetime
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class JSONTemplateManager:
    """JSON描述文件模板管理器"""
    
    @staticmethod
    def create_base_template(
        filename: str,
        file_size: int,
        width: int,
        height: int,
        format_name: str,
        mode: str,
        channels: int
    ) -> Dict[str, Any]:
        """
        创建基础JSON模板
        
        Args:
            filename: 文件名
            file_size: 文件大小
            width: 图片宽度
            height: 图片高度
            format_name: 图片格式
            mode: 颜色模式
            channels: 通道数
            
        Returns:
            Dict: 基础JSON模板
        """
        return {
            # 基本文件信息
            "filename": filename,
            "file_size": file_size,
            "added_at": datetime.now().isoformat(),
            
            # 图片技术信息
            "image_info": {
                "width": width,
                "height": height,
                "format": format_name,
                "mode": mode,
                "channels": channels,
                "aspect_ratio": round(width / height, 3) if height > 0 else 0
            },
            
            # 内容描述
            "content": {
                "description": "",
                "military_target": None,
                "weather": None,
                "scene": None,
                "tags": []
            },
            
            # 生成参数（AI生成）
            "generation_params": {
                "method": None,
                "model": None,
                "prompt": None,
                "negative_prompt": None,
                "steps": None,
                "cfg_scale": None,
                "seed": None,
                "sampler": None
            },
            
            # 传统合成参数
            "composition_params": {
                "target_vehicle": None,
                "background_scene": None,
                "weather_effect": None,
                "matting_method": None,
                "blend_mode": None,
                "opacity": None
            },
            
            # 元数据
            "metadata": {
                "original_filename": None,
                "content_type": None,
                "source": "upload",
                "version": "1.0",
                "created_by": "pic_gen_tool_v2"
            }
        }
    
    @staticmethod
    def create_ai_generation_template(
        filename: str,
        file_size: int,
        width: int,
        height: int,
        format_name: str,
        mode: str,
        channels: int,
        generation_params: Dict[str, Any],
        content_info: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        创建AI生成图片的JSON模板
        
        Args:
            filename: 文件名
            file_size: 文件大小
            width: 图片宽度
            height: 图片高度
            format_name: 图片格式
            mode: 颜色模式
            channels: 通道数
            generation_params: 生成参数
            content_info: 内容信息
            
        Returns:
            Dict: AI生成图片JSON模板
        """
        template = JSONTemplateManager.create_base_template(
            filename, file_size, width, height, format_name, mode, channels
        )
        
        # 更新生成参数
        template["generation_params"].update({
            "method": "ai",
            "model": generation_params.get("model"),
            "prompt": generation_params.get("prompt"),
            "negative_prompt": generation_params.get("negative_prompt"),
            "steps": generation_params.get("steps"),
            "cfg_scale": generation_params.get("cfg_scale"),
            "seed": generation_params.get("seed"),
            "sampler": generation_params.get("sampler"),
            "scheduler": generation_params.get("scheduler"),
            "guidance_scale": generation_params.get("guidance_scale")
        })
        
        # 更新内容信息
        if content_info:
            template["content"].update({
                "description": content_info.get("description", ""),
                "military_target": content_info.get("military_target"),
                "weather": content_info.get("weather"),
                "scene": content_info.get("scene"),
                "tags": content_info.get("tags", [])
            })
        
        # 更新元数据
        template["metadata"].update({
            "source": "ai_generation",
            "generation_time": datetime.now().isoformat()
        })
        
        return template
    
    @staticmethod
    def create_traditional_generation_template(
        filename: str,
        file_size: int,
        width: int,
        height: int,
        format_name: str,
        mode: str,
        channels: int,
        composition_params: Dict[str, Any],
        content_info: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        创建传统合成图片的JSON模板
        
        Args:
            filename: 文件名
            file_size: 文件大小
            width: 图片宽度
            height: 图片高度
            format_name: 图片格式
            mode: 颜色模式
            channels: 通道数
            composition_params: 合成参数
            content_info: 内容信息
            
        Returns:
            Dict: 传统合成图片JSON模板
        """
        template = JSONTemplateManager.create_base_template(
            filename, file_size, width, height, format_name, mode, channels
        )
        
        # 更新合成参数
        template["composition_params"].update({
            "target_vehicle": composition_params.get("target_vehicle"),
            "background_scene": composition_params.get("background_scene"),
            "weather_effect": composition_params.get("weather_effect"),
            "matting_method": composition_params.get("matting_method"),
            "blend_mode": composition_params.get("blend_mode"),
            "opacity": composition_params.get("opacity"),
            "feather_radius": composition_params.get("feather_radius"),
            "edge_smooth": composition_params.get("edge_smooth"),
            "color_match": composition_params.get("color_match"),
            "lighting_adjust": composition_params.get("lighting_adjust"),
            "shadow_generate": composition_params.get("shadow_generate")
        })
        
        # 更新内容信息
        if content_info:
            template["content"].update({
                "description": content_info.get("description", ""),
                "military_target": content_info.get("military_target"),
                "weather": content_info.get("weather"),
                "scene": content_info.get("scene"),
                "tags": content_info.get("tags", [])
            })
        
        # 更新元数据
        template["metadata"].update({
            "source": "traditional_generation",
            "composition_time": datetime.now().isoformat()
        })
        
        return template
    
    @staticmethod
    def create_upload_template(
        filename: str,
        file_size: int,
        width: int,
        height: int,
        format_name: str,
        mode: str,
        channels: int,
        upload_info: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        创建上传图片的JSON模板
        
        Args:
            filename: 文件名
            file_size: 文件大小
            width: 图片宽度
            height: 图片高度
            format_name: 图片格式
            mode: 颜色模式
            channels: 通道数
            upload_info: 上传信息
            
        Returns:
            Dict: 上传图片JSON模板
        """
        template = JSONTemplateManager.create_base_template(
            filename, file_size, width, height, format_name, mode, channels
        )
        
        # 更新上传信息
        if upload_info:
            template["content"].update({
                "description": upload_info.get("description", ""),
                "military_target": upload_info.get("military_target"),
                "weather": upload_info.get("weather"),
                "scene": upload_info.get("scene"),
                "tags": upload_info.get("tags", [])
            })
            
            template["metadata"].update({
                "original_filename": upload_info.get("original_filename"),
                "content_type": upload_info.get("content_type"),
                "upload_time": datetime.now().isoformat()
            })
        
        return template
    
    @staticmethod
    def validate_json_template(template: Dict[str, Any]) -> bool:
        """
        验证JSON模板的完整性
        
        Args:
            template: JSON模板
            
        Returns:
            bool: 是否有效
        """
        required_fields = [
            "filename", "file_size", "added_at",
            "image_info", "content", "generation_params",
            "composition_params", "metadata"
        ]
        
        for field in required_fields:
            if field not in template:
                logger.warning(f"JSON模板缺少必需字段: {field}")
                return False
        
        # 验证image_info字段
        image_info_fields = ["width", "height", "format", "mode", "channels"]
        for field in image_info_fields:
            if field not in template["image_info"]:
                logger.warning(f"image_info缺少必需字段: {field}")
                return False
        
        return True
