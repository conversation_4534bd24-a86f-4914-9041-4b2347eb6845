#!/usr/bin/env python3
"""
严格的数据集管理器
实现严格的数据集存储逻辑，确保数据一致性和文件隔离
"""

import os
import json
import shutil
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from PIL import Image

logger = logging.getLogger(__name__)

class StrictDatasetManager:
    """严格的数据集管理器"""
    
    def __init__(self, base_dir: str = "data/datasets"):
        """
        初始化数据集管理器
        
        Args:
            base_dir: 数据集基础目录
        """
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成文件目录
        self.generated_ai_dir = Path("data/generated/ai")
        self.generated_ai_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"严格数据集管理器初始化完成，基础目录: {self.base_dir}")
    
    def create_dataset_from_generation(
        self, 
        generation_id: str, 
        generation_params: Dict[str, Any],
        cleanup_generated_files: bool = True
    ) -> Tuple[str, Dict[str, Any]]:
        """
        从AI生成结果创建数据集
        
        Args:
            generation_id: 生成ID
            generation_params: 生成参数
            cleanup_generated_files: 是否清理生成目录中的文件
            
        Returns:
            Tuple[str, Dict]: (数据集名称, 数据集信息)
        """
        try:
            # 1. 生成数据集名称
            dataset_name = self._generate_dataset_name(generation_id, generation_params)
            dataset_path = self.base_dir / dataset_name
            
            # 2. 创建数据集目录
            if dataset_path.exists():
                logger.warning(f"数据集目录已存在，将覆盖: {dataset_path}")
                shutil.rmtree(dataset_path)
            
            dataset_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"创建数据集目录: {dataset_path}")
            
            # 3. 收集生成目录中的相关文件
            generated_files = self._collect_generated_files(generation_id)
            
            if not generated_files['original_images']:
                raise ValueError(f"没有找到generation_id {generation_id} 的原始图片")
            
            # 4. 复制文件到数据集
            dataset_files = self._copy_files_to_dataset(
                generated_files, 
                dataset_path, 
                generation_id
            )
            
            # 5. 创建数据集元数据
            dataset_metadata = self._create_dataset_metadata(
                dataset_name,
                generation_id,
                generation_params,
                dataset_files
            )
            
            # 6. 保存元数据文件
            metadata_path = dataset_path / "dataset.json"
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(dataset_metadata, f, indent=2, ensure_ascii=False)
            
            logger.info(f"数据集元数据已保存: {metadata_path}")
            
            # 7. 验证数据集完整性
            self._validate_dataset_integrity(dataset_path, generation_id)
            
            # 8. 清理生成目录中的文件
            if cleanup_generated_files:
                self._cleanup_generated_files(generation_id)
            
            logger.info(f"✅ 数据集创建成功: {dataset_name}")
            
            return dataset_name, dataset_metadata
            
        except Exception as e:
            logger.error(f"创建数据集失败: {e}")
            # 清理可能创建的不完整数据集
            if 'dataset_path' in locals() and dataset_path.exists():
                shutil.rmtree(dataset_path)
            raise
    
    def _generate_dataset_name(self, generation_id: str, generation_params: Dict[str, Any]) -> str:
        """生成数据集名称"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        target = generation_params.get('military_target', 'unknown')
        
        # 使用完整的generation_id确保唯一性
        dataset_name = f"{timestamp}_dataset_{target}_{generation_id}_{timestamp}"
        
        return dataset_name
    
    def _collect_generated_files(self, generation_id: str) -> Dict[str, List[Path]]:
        """收集生成目录中的相关文件"""
        generated_files = {
            'original_images': [],
            'annotated_images': [],
            'temp_files': [],
            'json_files': [],
            'annotation_files': []
        }
        
        # 搜索所有相关文件
        for file_path in self.generated_ai_dir.rglob("*"):
            if not file_path.is_file():
                continue
                
            filename = file_path.name
            
            # 检查是否包含generation_id
            if generation_id not in filename:
                continue
            
            # 分类文件
            if filename.startswith('annotated_') and filename.endswith('_temp.png'):
                generated_files['annotated_images'].append(file_path)
            elif filename.startswith('original_') and filename.endswith('_temp.png'):
                generated_files['temp_files'].append(file_path)  # 原始副本视为临时文件
            elif filename.endswith('_temp.png'):
                generated_files['temp_files'].append(file_path)
            elif filename.endswith('.png') and not any(prefix in filename for prefix in ['annotated_', 'original_']):
                generated_files['original_images'].append(file_path)
            elif filename.endswith('.json'):
                if 'annotation' in filename:
                    generated_files['annotation_files'].append(file_path)
                else:
                    generated_files['json_files'].append(file_path)
        
        logger.info(f"收集到generation_id {generation_id} 的文件:")
        logger.info(f"  原始图片: {len(generated_files['original_images'])}")
        logger.info(f"  标注图片: {len(generated_files['annotated_images'])}")
        logger.info(f"  临时文件: {len(generated_files['temp_files'])}")
        logger.info(f"  JSON文件: {len(generated_files['json_files'])}")
        logger.info(f"  标注文件: {len(generated_files['annotation_files'])}")
        
        return generated_files
    
    def _copy_files_to_dataset(
        self, 
        generated_files: Dict[str, List[Path]], 
        dataset_path: Path,
        generation_id: str
    ) -> Dict[str, List[str]]:
        """复制文件到数据集目录"""
        dataset_files = {
            'original_images': [],
            'annotated_images': []
        }
        
        # 复制原始图片
        for original_file in generated_files['original_images']:
            target_path = dataset_path / original_file.name
            shutil.copy2(original_file, target_path)
            dataset_files['original_images'].append(original_file.name)
            logger.info(f"✅ 复制原始图片: {original_file.name}")
        
        # 复制标注图片（如果存在）
        for annotated_file in generated_files['annotated_images']:
            target_path = dataset_path / annotated_file.name
            shutil.copy2(annotated_file, target_path)
            dataset_files['annotated_images'].append(annotated_file.name)
            logger.info(f"✅ 复制标注图片: {annotated_file.name}")
        
        # 不复制临时文件、JSON文件和标注文件
        logger.info(f"跳过 {len(generated_files['temp_files'])} 个临时文件")
        logger.info(f"跳过 {len(generated_files['json_files'])} 个JSON文件")
        logger.info(f"跳过 {len(generated_files['annotation_files'])} 个标注文件")
        
        return dataset_files
    
    def _create_dataset_metadata(
        self,
        dataset_name: str,
        generation_id: str,
        generation_params: Dict[str, Any],
        dataset_files: Dict[str, List[str]]
    ) -> Dict[str, Any]:
        """创建数据集元数据"""
        
        # 获取图片信息
        images_info = []
        
        # 处理原始图片
        for img_filename in dataset_files['original_images']:
            img_path = self.base_dir / dataset_name / img_filename
            
            try:
                with Image.open(img_path) as img:
                    width, height = img.size
                    format_name = img.format
                
                img_info = {
                    "filename": img_filename,
                    "type": "original",
                    "width": width,
                    "height": height,
                    "format": format_name,
                    "generation_id": generation_id,
                    "generation_params": generation_params
                }
                
                images_info.append(img_info)
                
            except Exception as e:
                logger.error(f"获取图片信息失败 {img_filename}: {e}")
        
        # 处理标注图片
        for img_filename in dataset_files['annotated_images']:
            img_path = self.base_dir / dataset_name / img_filename
            
            try:
                with Image.open(img_path) as img:
                    width, height = img.size
                    format_name = img.format
                
                img_info = {
                    "filename": img_filename,
                    "type": "annotated",
                    "width": width,
                    "height": height,
                    "format": format_name,
                    "generation_id": generation_id,
                    "generation_params": generation_params
                }
                
                images_info.append(img_info)
                
            except Exception as e:
                logger.error(f"获取标注图片信息失败 {img_filename}: {e}")
        
        # 构建完整的元数据
        metadata = {
            "dataset_name": dataset_name,
            "generation_id": generation_id,
            "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "generation_type": "ai",
            "generation_params": generation_params,
            "images": images_info,
            "statistics": {
                "total_images": len(images_info),
                "original_images": len(dataset_files['original_images']),
                "annotated_images": len(dataset_files['annotated_images']),
                "has_annotations": len(dataset_files['annotated_images']) > 0
            }
        }
        
        return metadata
    
    def _validate_dataset_integrity(self, dataset_path: Path, generation_id: str) -> None:
        """验证数据集完整性"""
        logger.info(f"验证数据集完整性: {dataset_path}")
        
        # 检查必需文件
        if not (dataset_path / "dataset.json").exists():
            raise ValueError("缺少dataset.json元数据文件")
        
        # 检查所有图片文件是否包含正确的generation_id
        image_files = [f for f in dataset_path.iterdir() if f.is_file() and f.suffix.lower() in ['.png', '.jpg', '.jpeg']]
        
        for img_file in image_files:
            if generation_id not in img_file.name:
                raise ValueError(f"图片文件 {img_file.name} 不包含正确的generation_id {generation_id}")
        
        # 检查是否有多余的文件
        allowed_extensions = {'.png', '.jpg', '.jpeg', '.json'}
        for file_path in dataset_path.iterdir():
            if file_path.is_file() and file_path.suffix.lower() not in allowed_extensions:
                logger.warning(f"发现多余文件: {file_path}")
        
        logger.info("✅ 数据集完整性验证通过")
    
    def _cleanup_generated_files(self, generation_id: str) -> None:
        """清理生成目录中的相关文件"""
        logger.info(f"清理generation_id {generation_id} 的生成文件")
        
        cleaned_count = 0
        
        # 清理主目录中的文件
        for file_path in self.generated_ai_dir.rglob("*"):
            if file_path.is_file() and generation_id in file_path.name:
                try:
                    file_path.unlink()
                    logger.debug(f"删除文件: {file_path}")
                    cleaned_count += 1
                except Exception as e:
                    logger.error(f"删除文件失败 {file_path}: {e}")
        
        logger.info(f"✅ 清理完成，删除了 {cleaned_count} 个文件")
    
    def validate_all_datasets(self) -> Dict[str, Any]:
        """验证所有数据集的完整性"""
        logger.info("开始验证所有数据集的完整性")
        
        validation_results = {
            'total_datasets': 0,
            'valid_datasets': 0,
            'invalid_datasets': [],
            'issues': []
        }
        
        for dataset_dir in self.base_dir.iterdir():
            if not dataset_dir.is_dir():
                continue
            
            validation_results['total_datasets'] += 1
            dataset_name = dataset_dir.name
            
            try:
                # 检查元数据文件
                metadata_path = dataset_dir / "dataset.json"
                if not metadata_path.exists():
                    raise ValueError("缺少dataset.json文件")
                
                with open(metadata_path, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                
                generation_id = metadata.get('generation_id')
                if not generation_id:
                    raise ValueError("元数据中缺少generation_id")
                
                # 验证文件完整性
                self._validate_dataset_integrity(dataset_dir, generation_id)
                
                validation_results['valid_datasets'] += 1
                logger.info(f"✅ 数据集验证通过: {dataset_name}")
                
            except Exception as e:
                validation_results['invalid_datasets'].append(dataset_name)
                validation_results['issues'].append(f"{dataset_name}: {str(e)}")
                logger.error(f"❌ 数据集验证失败 {dataset_name}: {e}")
        
        logger.info(f"数据集验证完成: {validation_results['valid_datasets']}/{validation_results['total_datasets']} 个数据集有效")
        
        return validation_results

    def cleanup_invalid_datasets(self) -> Dict[str, Any]:
        """清理无效的数据集"""
        logger.info("开始清理无效的数据集")

        cleanup_results = {
            'cleaned_datasets': [],
            'cleanup_errors': [],
            'total_cleaned': 0
        }

        validation_results = self.validate_all_datasets()

        for invalid_dataset in validation_results['invalid_datasets']:
            try:
                dataset_path = self.base_dir / invalid_dataset
                if dataset_path.exists():
                    shutil.rmtree(dataset_path)
                    cleanup_results['cleaned_datasets'].append(invalid_dataset)
                    cleanup_results['total_cleaned'] += 1
                    logger.info(f"✅ 清理无效数据集: {invalid_dataset}")

            except Exception as e:
                cleanup_results['cleanup_errors'].append(f"{invalid_dataset}: {str(e)}")
                logger.error(f"❌ 清理数据集失败 {invalid_dataset}: {e}")

        logger.info(f"数据集清理完成: 清理了 {cleanup_results['total_cleaned']} 个无效数据集")

        return cleanup_results

    def cleanup_orphaned_generated_files(self) -> Dict[str, Any]:
        """清理孤立的生成文件（没有对应数据集的文件）"""
        logger.info("开始清理孤立的生成文件")

        cleanup_results = {
            'cleaned_files': [],
            'cleanup_errors': [],
            'total_cleaned': 0
        }

        # 获取所有数据集中的generation_id
        active_generation_ids = set()

        for dataset_dir in self.base_dir.iterdir():
            if not dataset_dir.is_dir():
                continue

            try:
                metadata_path = dataset_dir / "dataset.json"
                if metadata_path.exists():
                    with open(metadata_path, 'r', encoding='utf-8') as f:
                        metadata = json.load(f)

                    generation_id = metadata.get('generation_id')
                    if generation_id:
                        active_generation_ids.add(generation_id)

            except Exception as e:
                logger.warning(f"读取数据集元数据失败 {dataset_dir.name}: {e}")

        logger.info(f"找到 {len(active_generation_ids)} 个活跃的generation_id")

        # 清理孤立的生成文件
        for file_path in self.generated_ai_dir.rglob("*"):
            if not file_path.is_file():
                continue

            # 检查文件是否属于任何活跃的generation_id
            file_generation_id = None
            filename = file_path.name

            # 尝试从文件名中提取generation_id
            for gen_id in active_generation_ids:
                if gen_id in filename:
                    file_generation_id = gen_id
                    break

            # 如果文件不属于任何活跃的generation_id，则删除
            if not file_generation_id:
                try:
                    file_path.unlink()
                    cleanup_results['cleaned_files'].append(str(file_path))
                    cleanup_results['total_cleaned'] += 1
                    logger.debug(f"删除孤立文件: {file_path}")

                except Exception as e:
                    cleanup_results['cleanup_errors'].append(f"{file_path}: {str(e)}")
                    logger.error(f"删除孤立文件失败 {file_path}: {e}")

        logger.info(f"孤立文件清理完成: 清理了 {cleanup_results['total_cleaned']} 个文件")

        return cleanup_results

    def get_dataset_statistics(self) -> Dict[str, Any]:
        """获取数据集统计信息"""
        stats = {
            'total_datasets': 0,
            'total_images': 0,
            'total_original_images': 0,
            'total_annotated_images': 0,
            'datasets_with_annotations': 0,
            'datasets_without_annotations': 0,
            'generation_ids': set(),
            'total_size_bytes': 0
        }

        for dataset_dir in self.base_dir.iterdir():
            if not dataset_dir.is_dir():
                continue

            stats['total_datasets'] += 1

            try:
                # 读取元数据
                metadata_path = dataset_dir / "dataset.json"
                if metadata_path.exists():
                    with open(metadata_path, 'r', encoding='utf-8') as f:
                        metadata = json.load(f)

                    generation_id = metadata.get('generation_id')
                    if generation_id:
                        stats['generation_ids'].add(generation_id)

                    dataset_stats = metadata.get('statistics', {})
                    stats['total_images'] += dataset_stats.get('total_images', 0)
                    stats['total_original_images'] += dataset_stats.get('original_images', 0)
                    stats['total_annotated_images'] += dataset_stats.get('annotated_images', 0)

                    if dataset_stats.get('has_annotations', False):
                        stats['datasets_with_annotations'] += 1
                    else:
                        stats['datasets_without_annotations'] += 1

                # 计算目录大小
                for file_path in dataset_dir.rglob("*"):
                    if file_path.is_file():
                        stats['total_size_bytes'] += file_path.stat().st_size

            except Exception as e:
                logger.warning(f"获取数据集统计信息失败 {dataset_dir.name}: {e}")

        # 转换generation_ids为列表
        stats['generation_ids'] = list(stats['generation_ids'])
        stats['unique_generation_ids'] = len(stats['generation_ids'])
        stats['total_size_mb'] = round(stats['total_size_bytes'] / (1024 * 1024), 2)

        return stats
