"""
AI模型管理器
支持多模型切换和优化配置
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import os
import re
try:
    import torch
    from diffusers import StableDiffusionPipeline, DiffusionPipeline
except ImportError:
    torch = None
    StableDiffusionPipeline = None
    DiffusionPipeline = None
from .custom_model_manager import CustomModelManager
from .sd_weight_loader import SDWeightLoader

logger = logging.getLogger(__name__)

class ModelManager:
    """AI模型管理器"""
    
    # 预定义的模型配置，针对军事目标生成优化
    MODEL_CONFIGS = {
        "stable-diffusion-v1-5": {
            "model_id": "runwayml/stable-diffusion-v1-5",
            "description": "标准SD1.5模型，平衡质量和速度",
            "target_size_bias": "medium",  # 倾向于生成中等大小目标
            "recommended_for": ["通用场景", "快速生成"],
            "cfg_scale_range": (7.0, 12.0),
            "steps_range": (20, 50)
        },
        "stable-diffusion-2-1": {
            "model_id": "stabilityai/stable-diffusion-2-1",
            "description": "SD2.1模型，更好的细节表现",
            "target_size_bias": "small",  # 倾向于生成较小目标
            "recommended_for": ["高质量生成", "细节丰富"],
            "cfg_scale_range": (5.0, 10.0),
            "steps_range": (25, 60)
        },
        "dreamlike-photoreal": {
            "model_id": "dreamlike-art/dreamlike-photoreal-2.0",
            "description": "照片级真实感模型，适合军事场景",
            "target_size_bias": "small",  # 倾向于生成较小、更真实的目标
            "recommended_for": ["真实感场景", "军事训练数据"],
            "cfg_scale_range": (6.0, 9.0),
            "steps_range": (20, 40)
        },
        "realistic-vision-v6": {
            "model_id": "SG161222/Realistic_Vision_V6.0_B1_noVAE",
            "description": "最新版本现实主义视觉模型，极小目标生成效果更佳",
            "target_size_bias": "controllable",  # 可控的目标大小
            "recommended_for": ["精确构图", "小目标生成", "超真实感"],
            "cfg_scale_range": (4.0, 7.0),
            "steps_range": (15, 30)
        }
    }
    
    def __init__(self, device: str = "auto", proxy_port: int = 7890):
        """
        初始化模型管理器

        Args:
            device: 推理设备
            proxy_port: 代理端口，用于模型下载
        """
        self.device = self._get_device(device)
        self.current_model = None
        self.current_model_id = None
        self.loaded_models = {}  # 缓存已加载的模型
        self.proxy_port = proxy_port

        # 初始化自定义模型管理器
        self.custom_model_manager = CustomModelManager()

        # 初始化SD权重加载器
        self.sd_weight_loader = SDWeightLoader()

        # 配置代理环境变量
        self._setup_proxy()

        logger.info(f"模型管理器初始化完成，设备: {self.device}, 代理端口: {self.proxy_port}")

    def _setup_proxy(self):
        """设置代理环境变量用于模型下载"""
        if self.proxy_port:
            proxy_url = f"http://127.0.0.1:{self.proxy_port}"

            # 设置环境变量
            os.environ['HTTP_PROXY'] = proxy_url
            os.environ['HTTPS_PROXY'] = proxy_url
            os.environ['http_proxy'] = proxy_url
            os.environ['https_proxy'] = proxy_url

            # 设置不使用代理的地址（本地地址）
            no_proxy = "localhost,127.0.0.1,::1"
            os.environ['NO_PROXY'] = no_proxy
            os.environ['no_proxy'] = no_proxy

            logger.info(f"已配置代理: {proxy_url}")
        else:
            logger.info("未配置代理")

    def _clear_proxy(self):
        """清除代理环境变量"""
        proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'NO_PROXY', 'no_proxy']
        for var in proxy_vars:
            if var in os.environ:
                del os.environ[var]
        logger.info("已清除代理配置")

    def set_proxy_port(self, port: int):
        """
        设置代理端口

        Args:
            port: 代理端口号，设置为0表示不使用代理
        """ 
        self.proxy_port = port
        if port > 0:
            self._setup_proxy()
        else:
            self._clear_proxy()
        logger.info(f"代理端口已更新为: {port}")

    def _download_and_load_model(self, model_id: str, model_key: str, torch_dtype) -> StableDiffusionPipeline:
        """
        下载并加载模型，支持不同的模型格式
        
        Args:
            model_id: 模型ID
            model_key: 模型键名
            torch_dtype: 数据类型
            
        Returns:
            StableDiffusionPipeline: 加载的模型管道
        """
        
        # 首先检查本地models目录中的单文件模型
        logger.info(f"检查本地单文件模型: {model_key}")
        local_model_dir = Path("models") / model_key
        
        if local_model_dir.exists():
            # 查找单文件模型
            safetensors_files = list(local_model_dir.glob("*.safetensors"))
            if safetensors_files:
                # 优先选择非inpainting文件
                priority_files = [f for f in safetensors_files if not f.name.endswith('_inpainting.safetensors') and not f.name.endswith('_inpainting_fp16.safetensors')]
                if priority_files:
                    safetensors_files = priority_files
                
                for safetensors_file in safetensors_files:
                    try:
                        logger.info(f"尝试加载本地单文件: {safetensors_file}")
                        # 使用新的离线加载方法
                        pipeline = self._load_single_file_offline(safetensors_file, torch_dtype)
                        logger.info(f"✅ 本地单文件加载成功: {safetensors_file.name}")
                        return pipeline
                    except Exception as local_e:
                        logger.warning(f"本地单文件加载失败: {str(local_e)}")
                        continue
        
        try:
            # 如果本地单文件不存在或加载失败，尝试标准的diffusers格式
            logger.info(f"尝试标准格式加载: {model_id}")
            pipeline = StableDiffusionPipeline.from_pretrained(
                model_id,
                torch_dtype=torch_dtype,
                safety_checker=None,
                requires_safety_checker=False,
                use_safetensors=True,
                resume_download=True,
                variant="fp16" if torch_dtype == torch.float16 else None  # 尝试fp16变体
            )
            logger.info("✅ 标准格式加载成功")
            return pipeline
            
        except Exception as e:
            error_msg = str(e)
            logger.warning(f"标准格式加载失败: {error_msg}")
            
            # 如果是网络连接问题，提供更好的错误信息
            if "Connection" in error_msg or "SSL" in error_msg or "Max retries" in error_msg:
                logger.error("网络连接问题，请检查:")
                logger.error("1. 网络连接是否正常")
                logger.error("2. 代理设置是否正确 (当前使用7890端口)")
                logger.error("3. 防火墙是否阻止了连接")
                raise Exception(f"网络连接失败，无法下载模型 {model_id}。请检查网络设置和代理配置。")
            
            # 检测是否为单文件safetensors模型错误或VAE文件缺失
            if ("Could not find the necessary `safetensors` weights" in error_msg or
                "Error no file named diffusion_pytorch_model.safetensors found" in error_msg or
                "does not appear to have a file named" in error_msg):
                logger.info("检测到单文件safetensors模型或组件缺失，尝试单文件加载...")

                # 从错误信息中提取所有.safetensors文件
                matches = re.findall(r"'([^']*\.safetensors)'", error_msg)
                safetensors_files = [f for f in matches if not f.endswith('_inpainting.safetensors')]

                logger.info(f"从错误信息发现的文件: {safetensors_files}")

                # 为不同模型定义文件名映射
                model_file_mapping = {
                    "SG161222/Realistic_Vision_V6.0_B1_noVAE": [
                        "Realistic_Vision_V6.0_NV_B1_fp16.safetensors",  # 优先fp16版本
                        "Realistic_Vision_V6.0_NV_B1.safetensors"
                    ],
                    "SG161222/Realistic_Vision_V4.0": [
                        "realisticVisionV40_v40VAE.safetensors",
                        "Realistic_Vision_V4.0.safetensors"
                    ]
                }
                
                # 获取当前模型的可能文件名
                possible_files = model_file_mapping.get(model_id, [])
                
                # 合并从错误信息提取的文件名和预定义的文件名
                all_possible_files = list(set(safetensors_files + possible_files))
                
                # 优先尝试非inpainting文件
                priority_files = [f for f in all_possible_files if not f.endswith('_inpainting.safetensors') and not f.endswith('_inpainting_fp16.safetensors')]
                other_files = [f for f in all_possible_files if f not in priority_files]
                ordered_files = priority_files + other_files
                
                # 再次检查本地models目录（以防用户刚刚下载了文件）
                for filename in ordered_files:
                    local_file_path = local_model_dir / filename
                    if local_file_path.exists():
                        try:
                            logger.info(f"发现本地文件: {local_file_path}")
                            pipeline = StableDiffusionPipeline.from_single_file(
                                str(local_file_path),
                                torch_dtype=torch_dtype,
                                safety_checker=None,
                                feature_extractor=None,
                                requires_safety_checker=False,
                                use_safetensors=True,
                                load_safety_checker=False,
                                local_files_only=True
                            )
                            logger.info(f"✅ 本地单文件加载成功: {filename}")
                            return pipeline
                        except Exception as local_e:
                            logger.warning(f"本地单文件加载失败: {str(local_e)}")
                            continue
                
                # 尝试从Hugging Face缓存加载
                for filename in ordered_files:
                    try:
                        logger.info(f"尝试从缓存加载单文件: {filename}")
                        
                        # 检查Hugging Face缓存目录
                        cache_dir = Path.home() / ".cache" / "huggingface" / "hub"
                        model_cache_dir = cache_dir / f"models--{model_id.replace('/', '--')}"
                        
                        if model_cache_dir.exists():
                            # 查找snapshots目录中的文件
                            snapshots_dir = model_cache_dir / "snapshots"
                            if snapshots_dir.exists():
                                for snapshot_dir in snapshots_dir.iterdir():
                                    if snapshot_dir.is_dir():
                                        local_file = snapshot_dir / filename
                                        if local_file.exists():
                                            logger.info(f"发现缓存文件: {local_file}")
                                            try:
                                                pipeline = StableDiffusionPipeline.from_single_file(
                                                    str(local_file),
                                                    torch_dtype=torch_dtype,
                                                    safety_checker=None,
                                                    feature_extractor=None,
                                                    requires_safety_checker=False,
                                                    use_safetensors=True,
                                                    load_safety_checker=False,
                                                    local_files_only=True
                                                )
                                                logger.info(f"✅ 缓存单文件加载成功: {filename}")
                                                return pipeline
                                            except Exception as cache_e:
                                                logger.warning(f"缓存单文件加载失败: {str(cache_e)}")
                                                break
                        
                        # 如果本地和缓存都失败，尝试在线加载
                        # 使用正确的Hugging Face URL格式
                        file_url = f"https://huggingface.co/{model_id}/resolve/main/{filename}"
                        logger.info(f"尝试在线单文件加载: {file_url}")
                        
                        try:
                            pipeline = StableDiffusionPipeline.from_single_file(
                                file_url,
                                torch_dtype=torch_dtype,
                                safety_checker=None,
                                feature_extractor=None,
                                requires_safety_checker=False,
                                use_safetensors=True,
                                load_safety_checker=False
                            )
                            logger.info(f"✅ 在线单文件加载成功: {filename}")
                            return pipeline
                        except Exception as url_e:
                            logger.warning(f"在线单文件加载失败: {str(url_e)}")
                            # 检查是否是网络问题
                            if "SSL" in str(url_e) or "Connection" in str(url_e) or "Max retries" in str(url_e):
                                logger.warning("检测到网络连接问题，跳过在线下载")
                                break
                            continue
                        
                    except Exception as single_e:
                        logger.warning(f"单文件 {filename} 加载失败: {str(single_e)}")
                        continue
                
                # 如果单文件加载失败，提供解决方案
                logger.info("单文件加载失败，可能的解决方案:")
                logger.info("1. 确保模型文件已下载到 models/realistic-vision-v6/ 目录")
                logger.info("2. 检查文件名是否正确 (应为 Realistic_Vision_V6.0_NV_B1.safetensors)")
                logger.info("3. 网络连接问题 - 检查代理设置或网络连接")
                logger.info("4. 使用其他可用的模型")
                
                # 不再尝试非safetensors格式，直接抛出原始错误
                raise e
            else:
                # 其他类型的错误，直接抛出
                raise e

    def _get_device(self, device: str) -> str:
        """获取推理设备"""
        if device == "auto":
            if torch is not None and torch.cuda.is_available():
                logger.info("检测到CUDA支持，使用GPU")
                return "cuda"
            elif torch is not None and hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                logger.info("检测到MPS支持，使用Apple Silicon GPU")
                return "mps"
            else:
                logger.info("使用CPU进行推理")
                return "cpu"
        return device
    
    def get_available_models(self) -> Dict[str, Dict[str, Any]]:
        """获取可用模型列表（包括自定义模型）"""
        # 获取预定义模型
        all_models = self.MODEL_CONFIGS.copy()

        # 添加自定义模型
        custom_models = self.custom_model_manager.get_enabled_custom_models()
        for model_key, config in custom_models.items():
            # 转换自定义模型配置为标准格式
            all_models[model_key] = {
                "model_id": config.get("model_path", ""),
                "description": config.get("description", "自定义模型"),
                "target_size_bias": config.get("target_size_bias", "medium"),
                "recommended_for": config.get("recommended_for", ["自定义场景"]),
                "cfg_scale_range": tuple(config.get("cfg_scale_range", [7.0, 12.0])),
                "steps_range": tuple(config.get("steps_range", [20, 50])),
                "is_custom": True,
                "model_name": config.get("model_name", model_key),
                "model_type": config.get("model_type", "diffusers")
            }

        return all_models
    
    def get_model_recommendations(self, target_size_preference: str = "small") -> List[str]:
        """
        根据目标尺寸偏好推荐模型
        
        Args:
            target_size_preference: 目标尺寸偏好 (small/medium/large/controllable)
            
        Returns:
            List[str]: 推荐的模型ID列表
        """
        recommendations = []
        
        for model_key, config in self.MODEL_CONFIGS.items():
            if config["target_size_bias"] == target_size_preference:
                recommendations.append(model_key)
            elif target_size_preference == "small" and config["target_size_bias"] in ["small", "controllable"]:
                recommendations.append(model_key)
        
        # 如果没有完全匹配的，返回所有可控的模型
        if not recommendations:
            recommendations = [k for k, v in self.MODEL_CONFIGS.items() 
                             if v["target_size_bias"] in ["controllable", "small"]]
        
        return recommendations
    
    def load_model(self, model_key: str, force_reload: bool = False) -> bool:
        """
        加载指定模型
        
        Args:
            model_key: 模型键名
            force_reload: 是否强制重新加载
            
        Returns:
            bool: 加载是否成功
        """
        # 检查是否为预定义模型或自定义模型
        is_predefined = model_key in self.MODEL_CONFIGS
        is_custom = model_key in self.custom_model_manager.get_custom_models()

        if not is_predefined and not is_custom:
            logger.error(f"未知模型: {model_key}")
            return False
        
        
        # RV6模型特殊处理
        if model_key == "realistic-vision-v6":
            try:
                logger.info("检测到RV6模型，使用专用加载器...")
                # 确保CPU设备使用float32
                if self.device == "cuda" and torch is not None and torch.cuda.is_available():
                    torch_dtype = torch.float16
                else:
                    torch_dtype = torch.float32 if torch is not None else None
                pipeline = self._load_rv6_offline(model_key, torch_dtype)
                pipeline = pipeline.to(self.device)
                
                # 更新当前模型
                self.current_model = pipeline
                self.current_model_id = model_key  # 保持与现有代码的兼容性
                
                logger.info(f"✅ RV6模型加载成功: {model_key}")
                return True
                
            except Exception as rv6_e:
                logger.error(f"RV6专用加载器失败: {str(rv6_e)}")
                # 继续使用标准加载流程
                pass
        
# 如果模型已加载且不强制重新加载
        if not force_reload and self.current_model_id == model_key and self.current_model is not None:
            logger.info(f"模型 {model_key} 已加载")
            return True

        # 检查是否已缓存（优先使用缓存）
        if model_key in self.loaded_models and not force_reload:
            self.current_model = self.loaded_models[model_key]
            self.current_model_id = model_key
            logger.info(f"从缓存快速切换到模型: {model_key}")
            return True

        # 获取模型配置
        if is_predefined:
            config = self.MODEL_CONFIGS[model_key]
            model_id = config["model_id"]
        else:
            # 自定义模型
            custom_config = self.custom_model_manager.get_custom_model(model_key)
            if not custom_config:
                logger.error(f"自定义模型配置不存在: {model_key}")
                return False

            # 验证自定义模型
            is_valid, validation_msg = self.custom_model_manager.validate_custom_model(model_key)
            if not is_valid:
                logger.error(f"自定义模型验证失败: {validation_msg}")
                return False

            model_id = custom_config.get("local_path") or custom_config.get("model_path")
            config = {
                "model_id": model_id,
                "description": custom_config.get("description", ""),
                "model_type": custom_config.get("model_type", "diffusers")
            }

        try:
            logger.info(f"正在加载模型: {model_id}")

            # 如果不在缓存中，需要重新加载

            # 确保代理配置生效
            self._setup_proxy()

            # 确定数据类型 - CPU不支持float16，强制使用float32
            if self.device == "cuda" and torch is not None and torch.cuda.is_available():
                torch_dtype = torch.float16
                logger.info("使用CUDA设备，数据类型: float16")
            else:
                torch_dtype = torch.float32 if torch is not None else None
                logger.info(f"使用{self.device}设备，数据类型: float32")

            # 优先检查本地模型
            local_model_path = Path("models") / model_key
            if local_model_path.exists() and local_model_path.is_dir():
                logger.info(f"发现本地模型，从本地加载: {local_model_path}")
                try:
                    pipeline = StableDiffusionPipeline.from_pretrained(
                        str(local_model_path),
                        torch_dtype=torch_dtype,
                        safety_checker=None,
                        requires_safety_checker=False,
                        local_files_only=True
                    )
                    logger.info("✓ 本地模型加载成功")
                except Exception as local_e:
                    logger.warning(f"本地模型加载失败，尝试在线下载: {str(local_e)}")
                    # 如果本地加载失败，继续在线下载
                    pipeline = self._download_and_load_model(str(model_id), model_key, torch_dtype)
            else:
                # 在线下载模型
                logger.info(f"使用代理端口 {self.proxy_port} 下载模型...")
                pipeline = self._download_and_load_model(str(model_id), model_key, torch_dtype)
            
            # 移动到设备
            pipeline = pipeline.to(self.device)
            
            # 启用内存优化
            if self.device == "cuda":
                pipeline.enable_model_cpu_offload()
                try:
                    pipeline.enable_xformers_memory_efficient_attention()
                except:
                    logger.warning("xformers优化启用失败，继续使用标准优化")
            
            # 缓存模型
            self.loaded_models[model_key] = pipeline
            self.current_model = pipeline
            self.current_model_id = model_key
            
            logger.info(f"模型 {model_key} 加载成功")
            return True
            
        except Exception as e:
            logger.error(f"模型 {model_key} 加载失败: {str(e)}", exc_info=True)

            # 提供详细的错误信息和解决方案
            error_msg = str(e)
            user_friendly_msg = self._get_user_friendly_error_message(error_msg)

            # 记录详细的错误分析
            self._log_detailed_error_analysis(model_key, error_msg, is_predefined, is_custom)

            # 提供具体的解决方案
            self._log_error_solutions(error_msg)

            logger.info(f"用户友好错误信息: {user_friendly_msg}")
            return False

    def _log_detailed_error_analysis(self, model_key: str, error_msg: str, is_predefined: bool, is_custom: bool):
        """
        记录详细的错误分析信息

        Args:
            model_key: 模型键名
            error_msg: 错误信息
            is_predefined: 是否为预定义模型
            is_custom: 是否为自定义模型
        """
        logger.error("=" * 50)
        logger.error(f"模型加载失败详细分析:")
        logger.error(f"模型键名: {model_key}")
        logger.error(f"模型类型: {'预定义' if is_predefined else '自定义' if is_custom else '未知'}")
        logger.error(f"设备: {self.device}")
        logger.error(f"当前已加载模型数量: {len(self.loaded_models)}")
        logger.error(f"错误信息: {error_msg}")

        # 检查系统状态
        if torch is not None:
            if torch.cuda.is_available():
                logger.error(f"CUDA可用: 是, 设备数量: {torch.cuda.device_count()}")
                if self.device == "cuda":
                    try:
                        memory_info = torch.cuda.memory_stats()
                        logger.error(f"GPU内存使用: {memory_info.get('allocated_bytes.all.current', 0) / 1024**3:.2f}GB")
                    except:
                        logger.error("无法获取GPU内存信息")
            else:
                logger.error("CUDA可用: 否")
        else:
            logger.error("torch库未正确导入")

        logger.error("=" * 50)

    def _log_error_solutions(self, error_msg: str):
        """
        根据错误类型提供解决方案

        Args:
            error_msg: 错误信息
        """
        logger.info("建议解决方案:")

        if any(keyword in error_msg for keyword in ["Connection", "network", "SSL", "timeout"]):
            logger.info("🌐 网络连接问题:")
            logger.info("  1. 检查网络连接是否正常")
            logger.info("  2. 检查代理设置（当前使用7890端口）")
            logger.info("  3. 尝试使用华为云镜像源")
            logger.info("  4. 下载模型到本地models目录")

        elif any(keyword in error_msg for keyword in ["memory", "CUDA", "out of memory"]):
            logger.info("💾 内存问题:")
            logger.info("  1. 关闭其他占用GPU内存的程序")
            logger.info("  2. 使用CPU模式运行")
            logger.info("  3. 清理模型缓存")
            logger.info("  4. 重启应用程序")

        elif any(keyword in error_msg for keyword in ["not found", "FileNotFound", "文件"]):
            logger.info("📁 文件问题:")
            logger.info("  1. 检查文件路径是否正确")
            logger.info("  2. 确认文件是否存在")
            logger.info("  3. 检查文件权限")
            logger.info("  4. 重新下载模型文件")

        elif any(keyword in error_msg for keyword in ["safetensors", "format", "corrupt"]):
            logger.info("🔧 文件格式问题:")
            logger.info("  1. 检查safetensors文件完整性")
            logger.info("  2. 重新下载模型文件")
            logger.info("  3. 尝试使用其他格式的模型")
            logger.info("  4. 验证文件哈希值")

        else:
            logger.info("🔍 通用解决方案:")
            logger.info("  1. 重启应用程序")
            logger.info("  2. 检查依赖库版本")
            logger.info("  3. 查看完整错误日志")
            logger.info("  4. 尝试使用其他模型")
    
    def get_current_model(self) -> Optional[Any]:
        """获取当前加载的模型"""
        return self.current_model
    
    def get_current_model_info(self) -> Dict[str, Any]:
        """获取当前模型信息"""
        # 如果没有当前模型但有缓存模型，自动激活第一个缓存模型
        if self.current_model_id is None and self.loaded_models:
            first_cached_model = list(self.loaded_models.keys())[0]
            logger.info(f"自动激活缓存模型: {first_cached_model}")
            self.current_model = self.loaded_models[first_cached_model]
            self.current_model_id = first_cached_model
        
        if self.current_model_id is None:
            return {"loaded": False}
        
        config = self.MODEL_CONFIGS.get(self.current_model_id, {})
        return {
            "loaded": True,
            "model_key": self.current_model_id,
            "model_id": config.get("model_id"),
            "description": config.get("description"),
            "target_size_bias": config.get("target_size_bias"),
            "recommended_for": config.get("recommended_for", []),
            "device": self.device
        }

    def get_custom_model(self, model_key: str) -> Optional[Dict[str, Any]]:
        """获取指定的自定义模型配置"""
        return self.custom_model_manager.get_custom_model(model_key)

    def update_custom_model(self, model_key: str, **updates) -> Tuple[bool, str]:
        """更新自定义模型配置"""
        return self.custom_model_manager.update_custom_model(model_key, **updates)

    def add_safetensors_model(
        self,
        model_key: str,
        model_name: str,
        safetensors_path: str,
        description: str = "",
        **kwargs
    ) -> Tuple[bool, str]:
        """添加safetensors格式的模型"""
        return self.custom_model_manager.add_safetensors_model(
            model_key=model_key,
            model_name=model_name,
            safetensors_path=safetensors_path,
            description=description,
            **kwargs
        )
    
    def preload_model(self, model_key: str) -> bool:
        """
        预加载模型到缓存中，不切换当前模型

        Args:
            model_key: 模型键名

        Returns:
            bool: 预加载是否成功
        """
        is_predefined = model_key in self.MODEL_CONFIGS
        is_custom = model_key in self.custom_model_manager.get_custom_models()

        if not is_predefined and not is_custom:
            logger.error(f"未知模型: {model_key}")
            return False

        # 如果模型已经在缓存中，直接返回成功
        if model_key in self.loaded_models:
            logger.info(f"模型 {model_key} 已在缓存中")
            return True

        # 获取模型配置
        if is_predefined:
            config = self.MODEL_CONFIGS[model_key]
            model_id = config["model_id"]
            model_type = "diffusers"
        else: # is_custom
            custom_config = self.custom_model_manager.get_custom_model(model_key)
            if not custom_config:
                logger.error(f"自定义模型配置不存在: {model_key}")
                return False
            model_id = custom_config.get("local_path") or custom_config.get("model_path")
            model_type = custom_config.get("model_type", "diffusers")

        try:
            logger.info(f"正在预加载模型: {model_id} (类型: {model_type})")

            # 根据模型类型选择加载方式
            if model_type == "safetensors":
                # 对于safetensors，我们直接调用load_safetensors_model
                # 它会处理加载和缓存，但我们不把它设为当前模型
                success = self.load_safetensors_model(model_key, str(model_id), force_reload=True)
                # 成功加载后，它已经在self.loaded_models中
                if success:
                    logger.info(f"✅ 自定义safetensors模型 {model_key} 预加载成功")
                    # 预加载后，将当前模型恢复为预加载之前的状态
                    # load_safetensors_model会改变current_model, 我们需要恢复它
                    # (这是一个简化的处理，更复杂的场景可能需要保存和恢复状态)
                    if self.current_model_id != model_key:
                         logger.info("预加载完成，当前活动模型保持不变。")
                    return True
                else:
                    logger.error(f"自定义safetensors模型 {model_key} 预加载失败")
                    return False

            # --- 以下是处理diffusers格式模型的现有逻辑 ---

            # RV6模型特殊处理
            if model_key == "realistic-vision-v6":
                try:
                    logger.info("检测到RV6模型，使用专用预加载器...")
                    torch_dtype = torch.float16 if (torch is not None and self.device != "cpu") else (torch.float32 if torch is not None else None)
                    pipeline = self._load_rv6_offline(model_key, torch_dtype)
                except Exception as rv6_e:
                    logger.error(f"RV6专用预加载器失败: {str(rv6_e)}")
                    # 继续使用标准预加载流程
                    pass
            else:
                 # 确保代理配置生效
                self._setup_proxy()
                # 确定数据类型
                torch_dtype = torch.float16 if (torch is not None and self.device == "cuda") else (torch.float32 if torch is not None else None)
                pipeline = self._download_and_load_model(model_id, model_key, torch_dtype)


            # 移动到设备
            pipeline = pipeline.to(self.device)

            # 启用内存优化
            if self.device == "cuda":
                pipeline.enable_model_cpu_offload()
                try:
                    pipeline.enable_xformers_memory_efficient_attention()
                except:
                    logger.warning("xformers优化启用失败，继续使用标准优化")

            # 缓存模型（但不设置为当前模型）
            self.loaded_models[model_key] = pipeline

            logger.info(f"模型 {model_key} 预加载成功")
            return True

        except Exception as e:
            logger.error(f"模型 {model_key} 预加载失败: {str(e)}", exc_info=True)
            return False

    def get_cached_models(self) -> List[str]:
        """
        获取已缓存的模型列表

        Returns:
            List[str]: 已缓存的模型键名列表
        """
        return list(self.loaded_models.keys())

    def load_safetensors_model(
        self,
        model_key: str,
        safetensors_path: str,
        force_reload: bool = False
    ) -> bool:
        """
        加载safetensors格式的权重文件
        新逻辑：先加载原始模型，然后使用pipe.load_lora_weights加载权重
        """
        try:
            # 规范化文件路径
            normalized_path = self._normalize_file_path(safetensors_path)

            # 检查是否已加载且不需要强制重新加载
            if not force_reload and model_key in self.loaded_models:
                self.current_model = self.loaded_models[model_key]
                self.current_model_id = model_key
                logger.info(f"使用缓存的权重模型: {model_key}")
                return True

            # 确定设备和数据类型
            device = self.device
            if torch is None:
                logger.error("torch库未正确导入，无法加载模型")
                return False

            torch_dtype = torch.float16 if device == "cuda" and torch.cuda.is_available() else torch.float32

            # 1. 先加载基础SD模型
            base_model_key = "stable-diffusion-v1-5"
            logger.info(f"正在加载基础模型: {base_model_key}")

            if not self.load_model(base_model_key):
                logger.error(f"基础模型 {base_model_key} 加载失败")
                return False

            base_pipeline = self.get_current_model()
            if not base_pipeline:
                logger.error("无法获取基础模型管道")
                return False

            # 2. 使用pipe.load_lora_weights加载权重
            logger.info(f"正在加载权重文件: {normalized_path}")
            try:
                # 检查文件是否存在
                if not Path(normalized_path).exists():
                    logger.error(f"权重文件不存在: {normalized_path}")
                    return False

                # 检查PEFT是否可用
                try:
                    import peft
                    logger.info("PEFT库可用，支持LoRA权重加载")
                except ImportError:
                    logger.warning("PEFT库不可用，尝试使用diffusers内置方法")

                # 使用diffusers的load_lora_weights方法
                try:
                    # 方法1：尝试使用adapter_name参数
                    base_pipeline.load_lora_weights(normalized_path, adapter_name=model_key)
                    logger.info(f"✅ 权重文件加载成功（带adapter_name）: {normalized_path}")
                except Exception as e1:
                    logger.warning(f"带adapter_name的加载失败: {e1}")
                    try:
                        # 方法2：尝试直接加载
                        base_pipeline.load_lora_weights(normalized_path)
                        logger.info(f"✅ 权重文件加载成功（直接加载）: {normalized_path}")
                    except Exception as e2:
                        logger.error(f"直接加载也失败: {e2}")
                        # 方法3：尝试使用底层API加载（不依赖PEFT）
                        try:
                            logger.info("尝试使用底层API加载LoRA权重")
                            success = self._load_lora_weights_without_peft(base_pipeline, normalized_path, model_key)
                            if success:
                                logger.info(f"✅ 使用底层API加载成功: {normalized_path}")
                            else:
                                raise Exception("底层API加载失败")
                        except Exception as e3:
                            logger.error(f"底层API加载也失败: {e3}")
                            raise e2  # 抛出原始错误

                # 更新当前模型
                self.current_model = base_pipeline
                self.current_model_id = model_key
                # 添加到本地缓存
                self.loaded_models[model_key] = base_pipeline

                logger.info(f"✅ safetensors权重模型加载成功: {model_key}")
                return True

            except Exception as lora_e:
                logger.error(f"LoRA权重加载失败: {str(lora_e)}")
                
                # 检查是否是PEFT相关错误
                if "PEFT backend is required" in str(lora_e):
                    logger.error("缺少PEFT库支持，请安装: pip install peft")
                    user_friendly_msg = "模型加载失败：缺少PEFT库支持。请运行 'pip install peft' 安装必要的依赖。"
                else:
                    # 提供用户友好的错误信息
                    user_friendly_msg = self._get_user_friendly_error_message(str(lora_e))
                
                logger.info(f"用户友好错误信息: {user_friendly_msg}")
                return False

        except Exception as e:
            logger.error(f"加载safetensors模型时发生异常: {str(e)}", exc_info=True)
            return False



    def _get_user_friendly_error_message(self, technical_message: str) -> str:
        """
        将技术性错误信息转换为用户友好的错误信息

        Args:
            technical_message: 技术性错误信息

        Returns:
            str: 用户友好的错误信息
        """
        if "文件不存在" in technical_message or "not found" in technical_message.lower():
            return "找不到指定的模型文件，请检查文件路径是否正确"
        elif "验证失败" in technical_message or "validation" in technical_message.lower():
            return "模型文件格式不正确或已损坏，请检查文件完整性"
        elif "内存" in technical_message or "memory" in technical_message.lower():
            return "内存不足，请尝试关闭其他程序或使用较小的模型"
        elif "网络" in technical_message or "connection" in technical_message.lower():
            return "网络连接问题，请检查网络设置或使用本地模型文件"
        elif "权限" in technical_message or "permission" in technical_message.lower():
            return "文件访问权限不足，请检查文件权限设置"
        else:
            return f"模型加载失败: {technical_message}"

    def add_safetensors_model_from_file(
        self,
        model_key: str,
        model_name: str,
        safetensors_path: str,
        description: str = ""
    ) -> Tuple[bool, str]:
        """
        从safetensors文件添加LoRA权重模型（简化版本）

        Args:
            model_key: 模型键名
            model_name: 模型显示名称
            safetensors_path: safetensors文件路径
            description: 模型描述

        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        try:
            # 规范化路径处理
            abs_safetensors_path = self._normalize_file_path(safetensors_path)
            logger.info(f"收到的原始路径: {safetensors_path}, 解析后的绝对路径: {abs_safetensors_path}")

            # 简单验证文件存在性
            if not Path(abs_safetensors_path).exists():
                return False, f"文件不存在: {abs_safetensors_path}"

            # 检查文件格式
            if not abs_safetensors_path.lower().endswith('.safetensors'):
                return False, f"不支持的文件格式，仅支持.safetensors文件"

            # 简化的LoRA模型配置
            model_config = {
                "model_key": model_key,
                "model_name": model_name,
                "model_path": abs_safetensors_path,
                "description": description or f"LoRA权重: {model_name}",
                "model_type": "lora",
                "is_local": True,
                "enabled": True,
                "cfg_scale_range": [7.0, 12.0],
                "steps_range": [20, 50],
                "target_size_bias": "medium",
                "recommended_for": ["LoRA微调", "风格迁移"],
                "default_resolution": "512x512",
                "base_model": "stable-diffusion-v1-5"
            }

            # 添加到自定义模型管理器
            success, message = self.custom_model_manager.add_custom_model(
                model_key=model_key,
                model_name=model_name,
                model_path=abs_safetensors_path,
                description=model_config["description"],
                model_type="lora",
                **{k: v for k, v in model_config.items() if k not in ["model_key", "model_name", "model_path", "description", "model_type"]}
            )

            if success:
                logger.info(f"成功添加LoRA模型: {model_key}")
                return True, f"成功添加LoRA模型: {model_name}"
            else:
                return False, message

        except Exception as e:
            logger.error(f"添加LoRA模型失败: {str(e)}")
            return False, f"添加失败: {str(e)}"

    def _normalize_file_path(self, file_path: str) -> str:
        """
        规范化文件路径，支持相对路径和绝对路径

        Args:
            file_path: 原始文件路径

        Returns:
            str: 规范化后的绝对路径
        """
        try:
            path_obj = Path(file_path)

            # 如果是相对路径，尝试相对于项目根目录解析
            if not path_obj.is_absolute():
                # 获取项目根目录（从当前文件位置推算）
                current_file = Path(__file__)
                project_root = current_file.parent.parent.parent.parent.parent
                path_obj = project_root / path_obj

            return str(path_obj.resolve())
        except Exception as e:
            logger.warning(f"路径规范化失败: {str(e)}, 使用原始路径")
            return str(Path(file_path).resolve())

    def _create_safetensors_model_config(self, file_info: Dict[str, Any], model_name: str, description: str) -> Dict[str, Any]:
        """
        根据文件信息创建模型配置

        Args:
            file_info: 文件信息
            model_name: 模型名称
            description: 模型描述

        Returns:
            Dict[str, Any]: 模型配置
        """
        # 检测模型类型
        is_sd_model = file_info.get("is_sd_model", False)
        found_indicators = file_info.get("found_indicators", [])

        # 默认配置（假设为SD1.5微调权重）
        config = {
            "detected_type": "SD 1.5 微调权重",
            "description": description or f"Stable Diffusion 1.5 微调模型: {model_name}",
            "params": {
                "cfg_scale_range": [7.0, 12.0],
                "steps_range": [20, 50],
                "target_size_bias": "medium",
                "recommended_for": ["图像生成", "微调场景"],
                "default_resolution": "512x512",
                "base_model": "stable-diffusion-v1-5",
                "model_type": "safetensors"
            }
        }

        # 根据检测到的组件调整配置
        if is_sd_model:
            if any("unet" in indicator.lower() for indicator in found_indicators):
                config["detected_type"] = "SD UNet权重"
                config["params"]["recommended_for"] = ["UNet微调", "风格迁移"]
            elif any("vae" in indicator.lower() for indicator in found_indicators):
                config["detected_type"] = "SD VAE权重"
                config["params"]["recommended_for"] = ["VAE替换", "图像质量优化"]
            elif len(found_indicators) >= 2:
                config["detected_type"] = "SD 完整模型"
                config["params"]["recommended_for"] = ["完整模型替换", "独立使用"]
        else:
            # 即使不是标准SD模型，也可能是有用的权重
            config["detected_type"] = "神经网络权重"
            config["description"] = description or f"神经网络权重文件: {model_name}"
            config["params"]["recommended_for"] = ["实验性使用", "权重迁移"]

        return config

    def load_single_weight_file(
        self,
        file_path: str,
        model_name: Optional[str] = None
    ) -> Tuple[bool, str, Optional[str]]:
        """
        便捷方法：加载单个权重文件（.safetensors或.ckpt）
        新逻辑：先加载基础模型，然后使用LoRA方式加载权重

        Args:
            file_path: 权重文件路径
            model_name: 模型名称（可选，自动生成）

        Returns:
            Tuple[bool, str, Optional[str]]: (是否成功, 消息, 模型键名)
        """
        try:
            # 规范化文件路径
            normalized_path = self._normalize_file_path(file_path)
            file_path_obj = Path(normalized_path)

            if not file_path_obj.exists():
                return False, f"文件不存在: {normalized_path}", None

            # 检查文件格式
            if file_path_obj.suffix.lower() not in ['.safetensors', '.ckpt']:
                return False, f"不支持的文件格式: {file_path_obj.suffix}", None

            # 生成模型键名和名称
            if model_name is None:
                model_name = file_path_obj.stem

            model_key = f"lora_{model_name.lower().replace(' ', '_').replace('-', '_')}"

            # 确保模型键名唯一
            counter = 1
            original_key = model_key
            while model_key in self.loaded_models:
                model_key = f"{original_key}_{counter}"
                counter += 1

            logger.info(f"开始加载LoRA权重文件: {normalized_path}")
            logger.info(f"生成的模型键名: {model_key}")
            logger.info(f"模型名称: {model_name}")

            # 直接使用新的LoRA加载逻辑
            load_success = self.load_safetensors_model(
                model_key=model_key,
                safetensors_path=normalized_path,
                force_reload=True
            )

            if load_success:
                logger.info(f"✅ LoRA权重文件加载成功: {model_name}")
                return True, f"成功加载LoRA权重: {model_name}", model_key
            else:
                logger.error(f"LoRA权重文件加载失败: {model_key}")
                return False, f"LoRA权重加载失败: {model_name}", None

        except Exception as e:
            logger.error(f"加载LoRA权重文件失败: {str(e)}", exc_info=True)
            return False, f"加载失败: {self._get_user_friendly_error_message(str(e))}", None

    def quick_load_weight_file(self, file_path: str) -> Tuple[bool, str]:
        """
        快速加载权重文件的简化接口

        Args:
            file_path: 权重文件路径

        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        success, message, model_key = self.load_single_weight_file(file_path)
        if success and model_key:
            return True, f"LoRA权重加载成功，模型键名: {model_key}"
        else:
            return False, message

    def unload_lora_weights(self) -> bool:
        """
        卸载当前加载的LoRA权重，恢复到基础模型

        Returns:
            bool: 是否成功
        """
        try:
            if self.current_model is None:
                logger.warning("当前没有加载的模型")
                return False

            # 检查当前模型是否有unload_lora_weights方法
            if hasattr(self.current_model, 'unload_lora_weights'):
                self.current_model.unload_lora_weights()
                logger.info("✅ LoRA权重已卸载，恢复到基础模型")

                # 更新模型ID为基础模型
                self.current_model_id = "stable-diffusion-v1-5"
                return True
            else:
                logger.warning("当前模型不支持LoRA权重卸载")
                return False

        except Exception as e:
            logger.error(f"卸载LoRA权重失败: {str(e)}")
            return False

    def get_lora_status(self) -> Dict[str, Any]:
        """
        获取当前LoRA状态信息

        Returns:
            Dict[str, Any]: LoRA状态信息
        """
        try:
            if self.current_model is None:
                return {"has_lora": False, "message": "没有加载的模型"}

            # 检查是否有LoRA相关属性或方法
            has_lora_methods = hasattr(self.current_model, 'load_lora_weights') and hasattr(self.current_model, 'unload_lora_weights')

            # 检查当前模型ID是否为LoRA模型
            is_lora_model = self.current_model_id and self.current_model_id.startswith("lora_")

            return {
                "has_lora": has_lora_methods,
                "is_lora_loaded": is_lora_model,
                "current_model_id": self.current_model_id,
                "supports_lora": has_lora_methods
            }

        except Exception as e:
            logger.error(f"获取LoRA状态失败: {str(e)}")
            return {"has_lora": False, "error": str(e)}

    def is_model_cached(self, model_key: str) -> bool:
        """
        检查模型是否已缓存

        Args:
            model_key: 模型键名

        Returns:
            bool: 模型是否已缓存
        """
        return model_key in self.loaded_models

    def get_optimal_generation_params(self, model_key: Optional[str] = None) -> Dict[str, Any]:
        """
        获取模型的最优生成参数

        Args:
            model_key: 模型键名，如果为None则使用当前模型

        Returns:
            Dict[str, Any]: 最优参数
        """
        if model_key is None:
            model_key = self.current_model_id

        if model_key is None or model_key not in self.MODEL_CONFIGS:
            return {}

        config = self.MODEL_CONFIGS[model_key]

        return {
            "cfg_scale_min": config["cfg_scale_range"][0],
            "cfg_scale_max": config["cfg_scale_range"][1],
            "cfg_scale_recommended": sum(config["cfg_scale_range"]) / 2,
            "steps_min": config["steps_range"][0],
            "steps_max": config["steps_range"][1],
            "steps_recommended": int(sum(config["steps_range"]) / 2)
        }
    
    def unload_model(self, model_key: Optional[str] = None):
        """
        卸载模型以释放内存
        
        Args:
            model_key: 要卸载的模型键名，如果为None则卸载当前模型
        """
        if model_key is None:
            model_key = self.current_model_id
        
        if model_key is not None and model_key in self.loaded_models:
            del self.loaded_models[model_key]
            logger.info(f"模型 {model_key} 已卸载")
        
        if model_key is not None and model_key == self.current_model_id:
            self.current_model = None
            self.current_model_id = None
    
    def clear_cache(self):
        """清空所有缓存的模型"""
        self.loaded_models.clear()
        self.current_model = None
        self.current_model_id = None

        # 强制垃圾回收
        import gc
        gc.collect()

        if self.device == "cuda" and torch is not None and torch.cuda.is_available():
            torch.cuda.empty_cache()

        logger.info("模型缓存已清空")
    def _load_rv6_offline(self, model_key: str, torch_dtype) -> 'StableDiffusionPipeline':
        """
        专门用于离线加载RV6模型的方法
        优先使用单文件safetensors，回退到diffusers格式
        """
        logger.info("使用专用RV6离线加载器...")

        # 构建模型目录路径，确保从项目根目录开始
        # 从 backend/app/services/ai_generation/model_manager.py 回到项目根目录
        current_file = Path(__file__)  # model_manager.py
        project_root = current_file.parent.parent.parent.parent.parent  # 回到项目根目录
        model_dir = project_root / "models" / model_key

        # 确保路径存在并且是绝对路径
        model_dir = model_dir.resolve()
        logger.info(f"当前文件: {current_file}")
        logger.info(f"项目根目录: {project_root.resolve()}")
        logger.info(f"模型目录绝对路径: {model_dir}")
        logger.info(f"目录是否存在: {model_dir.exists()}")

        # 方法1: 尝试单文件safetensors加载
        if model_dir.exists():
            # 列出目录中的所有文件
            all_files = list(model_dir.iterdir())
            logger.info(f"目录中的所有文件: {[f.name for f in all_files if f.is_file()]}")

            # 查找safetensors文件
            safetensors_files = [f for f in all_files if f.is_file() and f.suffix == '.safetensors']
            logger.info(f"找到的safetensors文件: {[f.name for f in safetensors_files]}")
        else:
            logger.error(f"模型目录不存在: {model_dir}")
            safetensors_files = []

        if safetensors_files:
            # 优先选择非inpainting文件，并按优先级排序
            priority_files = []
            for file in safetensors_files:
                if not file.name.endswith('_inpainting.safetensors') and not file.name.endswith('_inpainting_fp16.safetensors'):
                    priority_files.append(file)

            # 按文件名优先级排序：fp16版本优先（更小更快）
            priority_files.sort(key=lambda x: (
                0 if 'fp16' in x.name else 1,  # fp16优先
                x.name  # 字母顺序
            ))

            for main_file in priority_files:
                logger.info(f"尝试加载单文件: {main_file}")

                try:
                    pipeline = self._load_single_file_with_fallback(main_file, torch_dtype)
                    logger.info(f"✅ 单文件RV6模型加载成功: {main_file.name}")
                    return pipeline
                except Exception as e:
                    logger.warning(f"单文件加载失败 {main_file.name}: {str(e)}")
                    continue

        # 方法2: 尝试diffusers格式加载（如果单文件失败）
        logger.info("单文件加载失败，尝试diffusers格式...")

        # 检查是否有完整的diffusers结构
        required_components = ['unet', 'vae', 'text_encoder', 'tokenizer', 'scheduler']
        missing_components = []

        for component in required_components:
            component_dir = model_dir / component
            if not component_dir.exists():
                missing_components.append(component)
            else:
                # 检查是否有实际的权重文件
                weight_files = list(component_dir.glob("*.safetensors")) + list(component_dir.glob("*.bin"))
                if not weight_files and component in ['unet', 'vae']:  # 这两个组件必须有权重文件
                    missing_components.append(f"{component} (缺少权重文件)")

        if missing_components:
            logger.warning(f"diffusers格式不完整，缺少组件: {missing_components}")
            raise Exception(f"RV6模型加载失败：单文件和diffusers格式都不可用。缺少组件: {missing_components}")

        try:
            # 尝试多种diffusers格式加载方式
            diffusers_attempts = [
                {
                    "name": "标准diffusers加载",
                    "params": {
                        "torch_dtype": torch_dtype,
                        "safety_checker": None,
                        "requires_safety_checker": False,
                        "local_files_only": True,
                        "use_safetensors": True
                    }
                },
                {
                    "name": "无变体diffusers加载",
                    "params": {
                        "torch_dtype": torch_dtype,
                        "safety_checker": None,
                        "requires_safety_checker": False,
                        "local_files_only": True,
                        "variant": None
                    }
                },
                {
                    "name": "float32 diffusers加载",
                    "params": {
                        "torch_dtype": torch.float32 if torch is not None else None,
                        "safety_checker": None,
                        "requires_safety_checker": False,
                        "local_files_only": True
                    }
                }
            ]

            for attempt in diffusers_attempts:
                try:
                    logger.info(f"尝试{attempt['name']}...")
                    if StableDiffusionPipeline is None:
                        raise Exception("diffusers库未正确导入")
                    pipeline = StableDiffusionPipeline.from_pretrained(
                        str(model_dir),
                        **attempt['params']
                    )
                    logger.info(f"✅ {attempt['name']}成功")
                    return pipeline
                except Exception as attempt_e:
                    logger.warning(f"{attempt['name']}失败: {str(attempt_e)}")
                    continue

            raise Exception("所有diffusers格式加载尝试都失败了")

        except Exception as e:
            logger.error(f"diffusers格式加载也失败: {str(e)}")
            raise Exception(f"RV6模型加载完全失败：{str(e)}")

    def _load_single_file_with_fallback(self, safetensors_path: Path, torch_dtype) -> 'StableDiffusionPipeline':
        """
        使用多种回退策略加载单文件safetensors模型
        """
        logger.info(f"尝试加载单文件: {safetensors_path}")

        # 设置离线环境变量
        import os
        old_offline = os.environ.get('HF_HUB_OFFLINE', '0')
        old_disable_telemetry = os.environ.get('HF_HUB_DISABLE_TELEMETRY', '0')

        try:
            # 强制离线模式
            os.environ['HF_HUB_OFFLINE'] = '1'
            os.environ['HF_HUB_DISABLE_TELEMETRY'] = '1'

            # 尝试多种加载策略
            strategies = [
                {
                    "name": "完全离线 + 禁用安全检查器",
                    "params": {
                        "torch_dtype": torch_dtype,
                        "local_files_only": True,
                        "use_safetensors": True,
                        "load_safety_checker": False,
                        "safety_checker": None,
                        "feature_extractor": None
                    }
                },
                {
                    "name": "环境变量离线 + 禁用安全检查器",
                    "params": {
                        "torch_dtype": torch_dtype,
                        "use_safetensors": True,
                        "load_safety_checker": False,
                        "safety_checker": None,
                        "feature_extractor": None
                    }
                },
                {
                    "name": "float32 + 完全禁用安全检查",
                    "params": {
                        "torch_dtype": torch.float32 if torch is not None else None,
                        "local_files_only": True,
                        "use_safetensors": True,
                        "load_safety_checker": False,
                        "safety_checker": None,
                        "feature_extractor": None
                    }
                }
            ]

            for i, strategy in enumerate(strategies):
                try:
                    logger.info(f"尝试策略 {i+1}: {strategy['name']}")

                    if StableDiffusionPipeline is None:
                        raise Exception("diffusers库未正确导入")

                    pipeline = StableDiffusionPipeline.from_single_file(
                        str(safetensors_path),
                        requires_safety_checker=False,
                        **strategy['params']
                    )

                    logger.info(f"✅ 策略 {i+1} 成功")
                    return pipeline

                except Exception as e:
                    logger.warning(f"策略 {i+1} 失败: {str(e)}")
                    if i == len(strategies) - 1:  # 最后一个策略
                        raise e
                    continue

        finally:
            # 恢复环境变量
            os.environ['HF_HUB_OFFLINE'] = old_offline
            os.environ['HF_HUB_DISABLE_TELEMETRY'] = old_disable_telemetry


    def _load_single_file_offline(self, safetensors_path: Path, torch_dtype) -> 'StableDiffusionPipeline':
        """
        离线加载单文件safetensors模型
        
        Args:
            safetensors_path: safetensors文件路径
            torch_dtype: 数据类型
            
        Returns:
            StableDiffusionPipeline: 加载的模型管道
        """
        logger.info(f"尝试离线单文件加载: {safetensors_path}")
        
        try:
            # 检查依赖库
            if StableDiffusionPipeline is None:
                raise Exception("diffusers库未正确导入")

            # 方法1: 尝试直接加载，完全禁用安全检查器
            pipeline = StableDiffusionPipeline.from_single_file(
                str(safetensors_path),
                torch_dtype=torch_dtype,
                safety_checker=None,
                feature_extractor=None,
                requires_safety_checker=False,
                use_safetensors=True,
                load_safety_checker=False,
                local_files_only=True  # 强制本地文件
            )
            logger.info("✅ 离线单文件加载成功")
            return pipeline

        except Exception as e1:
            logger.warning(f"离线加载失败: {str(e1)}")

            try:
                # 方法2: 尝试不使用local_files_only，但设置离线环境变量
                import os
                old_offline = os.environ.get('HF_HUB_OFFLINE', '0')
                os.environ['HF_HUB_OFFLINE'] = '1'

                if StableDiffusionPipeline is None:
                    raise Exception("diffusers库未正确导入")

                pipeline = StableDiffusionPipeline.from_single_file(
                    str(safetensors_path),
                    torch_dtype=torch_dtype,
                    safety_checker=None,
                    feature_extractor=None,
                    requires_safety_checker=False,
                    use_safetensors=True,
                    load_safety_checker=False
                )

                # 恢复环境变量
                os.environ['HF_HUB_OFFLINE'] = old_offline

                logger.info("✅ 环境变量离线加载成功")
                return pipeline

            except Exception as e2:
                logger.warning(f"环境变量离线加载失败: {str(e2)}")
                raise e1  # 抛出原始错误
