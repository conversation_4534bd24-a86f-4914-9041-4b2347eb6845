"""
固定提示词服务
提供直接索引调用固定提示词的功能，替换动态生成逻辑
"""

import logging
from typing import Dict, List, Tuple, Any, Optional
from .fixed_prompt_matrix import (
    FIXED_PROMPT_MATRIX,
    SUPPORTED_VEHICLES,
    SUPPORTED_WEATHER,
    SUPPORTED_SCENES,
    get_fixed_prompt,
    get_all_combinations,
    validate_matrix_completeness
)

logger = logging.getLogger(__name__)

class FixedPromptService:
    """固定提示词服务类"""
    
    def __init__(self):
        """初始化固定提示词服务"""
        self.is_initialized = False
        self._validate_on_init()
        logger.info("固定提示词服务已创建")
    
    def _validate_on_init(self):
        """初始化时验证矩阵完整性"""
        try:
            validation_result = validate_matrix_completeness()
            if not validation_result["is_complete"]:
                logger.warning(f"提示词矩阵不完整: {validation_result}")
                if validation_result["missing_combinations"]:
                    logger.error(f"缺失的组合: {validation_result['missing_combinations']}")
                if validation_result["extra_combinations"]:
                    logger.warning(f"额外的组合: {validation_result['extra_combinations']}")
            else:
                logger.info(f"提示词矩阵验证通过，共 {validation_result['total_actual']} 个组合")
            
            self.is_initialized = True
            
        except Exception as e:
            logger.error(f"提示词矩阵验证失败: {str(e)}")
            raise
    
    def get_prompt(
        self,
        military_target: str,
        weather: str,
        scene: str,
        custom_prompt: str = "",
        **kwargs  # 兼容旧接口的其他参数
    ) -> Tuple[str, str]:
        """
        获取固定提示词
        
        Args:
            military_target: 军事目标类型 (坦克/战机/舰艇)
            weather: 天气条件 (雨天/雪天/大雾/夜间)
            scene: 场景环境 (城市/岛屿/乡村)
            custom_prompt: 自定义提示词（如果提供，将与固定提示词合并）
            **kwargs: 其他参数（为了兼容性，会被忽略）
            
        Returns:
            Tuple[str, str]: (正面提示词, 负面提示词)
            
        Raises:
            RuntimeError: 服务未初始化
            ValueError: 参数无效
            KeyError: 组合不存在
        """
        if not self.is_initialized:
            raise RuntimeError("固定提示词服务未初始化")
        
        try:
            # 获取固定提示词
            positive_prompt, negative_prompt = get_fixed_prompt(
                military_target, weather, scene
            )
            
            # 如果有自定义提示词，进行合并
            if custom_prompt.strip():
                positive_prompt = f"{custom_prompt.strip()}, {positive_prompt}"
            
            logger.debug(f"获取提示词成功: {military_target} + {weather} + {scene}")
            return positive_prompt, negative_prompt
            
        except (ValueError, KeyError) as e:
            logger.error(f"获取提示词失败: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"获取提示词时发生未知错误: {str(e)}")
            raise
    
    def build_prompt(
        self,
        military_target: str,
        weather: str,
        scene: str,
        style_strength: float = 0.7,
        technical_detail: float = 0.8,
        quality_boost: bool = True,
        target_size: str = "medium"
    ) -> Tuple[str, str]:
        """
        构建提示词（兼容旧接口）
        
        注意：此方法为了兼容性保留，但会忽略动态参数，直接返回固定提示词
        
        Args:
            military_target: 军事目标类型
            weather: 天气条件
            scene: 场景环境
            style_strength: 风格强度（忽略）
            technical_detail: 技术细节程度（忽略）
            quality_boost: 是否添加质量增强词（忽略）
            target_size: 目标尺寸（忽略）
            
        Returns:
            Tuple[str, str]: (正面提示词, 负面提示词)
        """
        logger.debug(f"使用兼容模式构建提示词，忽略动态参数")
        return self.get_prompt(military_target, weather, scene)
    
    def build_custom_prompt(
        self,
        base_prompt: str,
        military_target: str = None,
        weather: str = None,
        scene: str = None,
        enhance_quality: bool = True
    ) -> Tuple[str, str]:
        """
        基于自定义基础提示词构建（兼容旧接口）
        
        Args:
            base_prompt: 基础提示词
            military_target: 军事目标（可选）
            weather: 天气条件（可选）
            scene: 场景环境（可选）
            enhance_quality: 是否增强质量（忽略）
            
        Returns:
            Tuple[str, str]: (正面提示词, 负面提示词)
        """
        if military_target and weather and scene:
            # 如果提供了完整参数，使用固定提示词并合并
            return self.get_prompt(military_target, weather, scene, base_prompt)
        else:
            # 如果参数不完整，只返回基础提示词和通用负面提示词
            logger.warning("参数不完整，返回基础提示词")
            generic_negative = "low quality, blurry, cartoon, anime, close up, macro, oversized subject, fills frame"
            return base_prompt, generic_negative
    
    def get_supported_options(self) -> Dict[str, List[str]]:
        """
        获取支持的选项
        
        Returns:
            Dict[str, List[str]]: 支持的选项
        """
        return {
            "military_targets": SUPPORTED_VEHICLES.copy(),
            "weather_conditions": SUPPORTED_WEATHER.copy(),
            "scene_environments": SUPPORTED_SCENES.copy()
        }
    
    def get_all_combinations(self) -> List[Tuple[str, str, str]]:
        """
        获取所有支持的组合
        
        Returns:
            List[Tuple[str, str, str]]: 所有组合列表
        """
        return get_all_combinations()
    
    def validate_combination(
        self,
        military_target: str,
        weather: str,
        scene: str
    ) -> bool:
        """
        验证组合是否有效
        
        Args:
            military_target: 军事目标类型
            weather: 天气条件
            scene: 场景环境
            
        Returns:
            bool: 组合是否有效
        """
        try:
            get_fixed_prompt(military_target, weather, scene)
            return True
        except (ValueError, KeyError):
            return False
    
    def get_prompt_preview(
        self,
        military_target: str,
        weather: str,
        scene: str,
        max_length: int = 100
    ) -> Dict[str, str]:
        """
        获取提示词预览
        
        Args:
            military_target: 军事目标类型
            weather: 天气条件
            scene: 场景环境
            max_length: 预览最大长度
            
        Returns:
            Dict[str, str]: 提示词预览
        """
        try:
            positive, negative = self.get_prompt(military_target, weather, scene)
            
            positive_preview = positive[:max_length] + "..." if len(positive) > max_length else positive
            negative_preview = negative[:max_length] + "..." if len(negative) > max_length else negative
            
            return {
                "positive_preview": positive_preview,
                "negative_preview": negative_preview,
                "positive_length": len(positive),
                "negative_length": len(negative)
            }
        except Exception as e:
            logger.error(f"获取提示词预览失败: {str(e)}")
            return {
                "positive_preview": "错误",
                "negative_preview": "错误",
                "positive_length": 0,
                "negative_length": 0
            }
    
    def get_matrix_statistics(self) -> Dict[str, Any]:
        """
        获取矩阵统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        validation_result = validate_matrix_completeness()
        
        # 计算每种类型的组合数量
        vehicle_counts = {}
        weather_counts = {}
        scene_counts = {}
        
        for vehicle, weather, scene in get_all_combinations():
            vehicle_counts[vehicle] = vehicle_counts.get(vehicle, 0) + 1
            weather_counts[weather] = weather_counts.get(weather, 0) + 1
            scene_counts[scene] = scene_counts.get(scene, 0) + 1
        
        return {
            "total_combinations": validation_result["total_actual"],
            "is_complete": validation_result["is_complete"],
            "vehicle_counts": vehicle_counts,
            "weather_counts": weather_counts,
            "scene_counts": scene_counts,
            "supported_vehicles": len(SUPPORTED_VEHICLES),
            "supported_weather": len(SUPPORTED_WEATHER),
            "supported_scenes": len(SUPPORTED_SCENES)
        }
    
    def optimize_prompt(self, prompt: str) -> str:
        """
        优化提示词（兼容旧接口）
        
        注意：固定提示词已经优化，此方法主要用于兼容性
        
        Args:
            prompt: 原始提示词
            
        Returns:
            str: 优化后的提示词（实际上返回原始提示词）
        """
        logger.debug("固定提示词无需优化，返回原始提示词")
        return prompt
    
    def get_prompt_suggestions(self, military_target: str) -> Dict[str, List[str]]:
        """
        获取提示词建议（兼容旧接口）
        
        Args:
            military_target: 军事目标类型
            
        Returns:
            Dict[str, List[str]]: 提示词建议（返回空字典，因为使用固定提示词）
        """
        logger.debug("固定提示词服务不提供动态建议")
        return {
            "message": ["使用固定提示词矩阵，无需动态建议"],
            "available_combinations": [f"{military_target} + {w} + {s}" 
                                     for w in SUPPORTED_WEATHER 
                                     for s in SUPPORTED_SCENES]
        }
