"""
高级提示词构建器
专门针对小目标生成优化
"""

import random
from typing import Dict, List, Tuple, Any
import logging

logger = logging.getLogger(__name__)

class AdvancedPromptBuilder:
    """高级提示词构建器，专注于小目标生成"""
    
    def __init__(self):
        """初始化高级提示词构建器"""
        self._init_small_target_templates()
        self._init_composition_strategies()
        self._init_negative_prompts()
    
    def _init_small_target_templates(self):
        """初始化小目标模板"""
        self.small_target_templates = {
            "坦克": {
                "base": "small military tank in distance",
                "size_modifiers": [
                    "tiny tank", "distant armored vehicle", "small battle tank",
                    "miniature military vehicle", "far away tank", "compact armored unit"
                ],
                "composition_words": [
                    "wide landscape shot", "aerial view", "bird's eye view",
                    "panoramic scene", "vast battlefield", "expansive terrain",
                    "overhead perspective", "satellite view", "drone footage",
                    "long distance shot", "telephoto lens", "zoomed out view"
                ],
                "environment_emphasis": [
                    "large open field", "vast desert", "extensive urban area",
                    "wide battlefield", "spacious terrain", "broad landscape",
                    "massive environment", "huge scenery", "enormous backdrop"
                ]
            },
            "战机": {
                "base": "small fighter jet in sky",
                "size_modifiers": [
                    "tiny aircraft", "distant plane", "small warplane",
                    "miniature fighter", "far away jet", "compact aircraft"
                ],
                "composition_words": [
                    "wide sky shot", "aerial panorama", "vast sky view",
                    "expansive atmosphere", "broad horizon", "infinite sky",
                    "overhead sky view", "satellite perspective", "high altitude view",
                    "long distance aerial shot", "telephoto sky view", "zoomed out aerial"
                ],
                "environment_emphasis": [
                    "vast sky", "endless atmosphere", "huge cloud formations",
                    "massive sky dome", "enormous aerial space", "infinite horizon",
                    "expansive blue sky", "wide open air", "boundless atmosphere"
                ]
            },
            "舰艇": {
                "base": "small warship on ocean",
                "size_modifiers": [
                    "tiny naval vessel", "distant ship", "small battleship",
                    "miniature warship", "far away vessel", "compact naval unit"
                ],
                "composition_words": [
                    "wide ocean shot", "aerial sea view", "vast maritime scene",
                    "panoramic ocean view", "expansive seascape", "broad water view",
                    "overhead ocean perspective", "satellite sea view", "drone ocean footage",
                    "long distance maritime shot", "telephoto ocean view", "zoomed out seascape"
                ],
                "environment_emphasis": [
                    "vast ocean", "endless sea", "huge water expanse",
                    "massive maritime environment", "enormous seascape", "infinite waters",
                    "expansive blue ocean", "wide open sea", "boundless maritime space"
                ]
            }
        }
    
    def _init_composition_strategies(self):
        """初始化构图策略"""
        self.composition_strategies = {
            "ultra_wide": {
                "keywords": [
                    "ultra wide angle", "extreme wide shot", "panoramic view",
                    "fish eye lens", "16mm lens", "ultra wide perspective"
                ],
                "target_size_effect": "very_small"
            },
            "aerial": {
                "keywords": [
                    "aerial photography", "bird's eye view", "overhead shot",
                    "drone photography", "satellite view", "top down view"
                ],
                "target_size_effect": "small"
            },
            "telephoto": {
                "keywords": [
                    "telephoto lens", "200mm lens", "300mm lens", "long distance shot",
                    "compressed perspective", "distant subject", "zoomed out"
                ],
                "target_size_effect": "small"
            },
            "environmental": {
                "keywords": [
                    "environmental shot", "landscape photography", "scenic view",
                    "nature photography", "wide environmental context"
                ],
                "target_size_effect": "medium_small"
            }
        }
    
    def _init_negative_prompts(self):
        """初始化负面提示词"""
        self.negative_prompts = {
            "size_control": [
                "close up", "macro", "detailed view", "zoomed in",
                "large subject", "big object", "huge target", "massive vehicle",
                "giant", "enormous", "oversized", "dominant subject",
                "fills frame", "occupies most of image", "takes up entire frame"
            ],
            "composition_avoid": [
                "tight framing", "cropped view", "partial view", "cut off",
                "extreme close up", "macro photography", "detail shot",
                "focused on subject", "subject centered", "prominent subject"
            ],
            "quality_issues": [
                "blurry", "low quality", "pixelated", "distorted",
                "bad anatomy", "deformed", "mutated", "extra parts"
            ]
        }
    
    def build_small_target_prompt(
        self,
        military_target: str,
        weather: str,
        scene: str,
        target_size_ratio: float = 0.05,
        composition_strategy: str = "aerial"
    ) -> Tuple[str, str]:
        """
        构建专门用于小目标生成的提示词
        
        Args:
            military_target: 军事目标类型
            weather: 天气条件
            scene: 场景环境
            target_size_ratio: 目标尺寸比例
            composition_strategy: 构图策略
            
        Returns:
            Tuple[str, str]: (正面提示词, 负面提示词)
        """
        positive_parts = []
        
        # 1. 构图策略
        if composition_strategy in self.composition_strategies:
            strategy = self.composition_strategies[composition_strategy]
            comp_keyword = random.choice(strategy["keywords"])
            positive_parts.append(comp_keyword)
        
        # 2. 目标描述（强调小尺寸）
        if military_target in self.small_target_templates:
            template = self.small_target_templates[military_target]
            
            # 基础描述
            positive_parts.append(template["base"])
            
            # 尺寸修饰词
            size_modifier = random.choice(template["size_modifiers"])
            positive_parts.append(size_modifier)
            
            # 构图词汇
            comp_word = random.choice(template["composition_words"])
            positive_parts.append(comp_word)
            
            # 环境强调
            env_emphasis = random.choice(template["environment_emphasis"])
            positive_parts.append(env_emphasis)
        
        # 3. 根据目标尺寸比例调整强度
        if target_size_ratio <= 0.03:  # 极小目标
            positive_parts.extend([
                "extremely distant", "barely visible", "tiny speck",
                "ultra wide landscape", "vast scale difference"
            ])
        elif target_size_ratio <= 0.06:  # 小目标
            positive_parts.extend([
                "distant subject", "small in frame", "wide angle view",
                "environmental context", "scale reference"
            ])
        
        # 4. 天气和场景
        positive_parts.extend([weather, scene])
        
        # 5. 质量增强
        positive_parts.extend([
            "professional photography", "high resolution", "sharp focus",
            "realistic", "photorealistic", "detailed environment"
        ])
        
        # 构建负面提示词
        negative_parts = []
        for category, terms in self.negative_prompts.items():
            selected_terms = random.sample(terms, min(3, len(terms)))
            negative_parts.extend(selected_terms)
        
        positive_prompt = ", ".join(positive_parts)
        negative_prompt = ", ".join(negative_parts)
        
        return positive_prompt, negative_prompt
    
    def get_composition_strategies(self) -> Dict[str, Dict[str, Any]]:
        """获取可用的构图策略"""
        return self.composition_strategies.copy()
    
    def optimize_for_model(
        self,
        prompt: str,
        model_type: str = "stable-diffusion-v1-5"
    ) -> str:
        """
        根据模型类型优化提示词
        
        Args:
            prompt: 原始提示词
            model_type: 模型类型
            
        Returns:
            str: 优化后的提示词
        """
        # 不同模型的优化策略
        model_optimizations = {
            "stable-diffusion-v1-5": {
                "add_keywords": ["detailed", "high quality", "8k"],
                "weight_adjustments": {}
            },
            "stable-diffusion-2-1": {
                "add_keywords": ["photorealistic", "professional"],
                "weight_adjustments": {}
            },
            "dreamlike-photoreal": {
                "add_keywords": ["realistic", "natural lighting"],
                "weight_adjustments": {}
            },
            "realistic-vision": {
                "add_keywords": ["cinematic", "sharp focus"],
                "weight_adjustments": {}
            }
        }
        
        if model_type in model_optimizations:
            optimization = model_optimizations[model_type]
            additional_keywords = optimization["add_keywords"]
            
            # 添加模型特定的关键词
            prompt_parts = prompt.split(", ")
            prompt_parts.extend(additional_keywords)
            
            # 去重并重新组合
            unique_parts = []
            seen = set()
            for part in prompt_parts:
                if part.lower() not in seen:
                    unique_parts.append(part)
                    seen.add(part.lower())
            
            return ", ".join(unique_parts)
        
        return prompt
    
    def analyze_prompt_effectiveness(self, prompt: str) -> Dict[str, Any]:
        """
        分析提示词对小目标生成的有效性
        
        Args:
            prompt: 提示词
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        prompt_lower = prompt.lower()
        
        # 检查小目标相关词汇
        small_target_indicators = [
            "small", "tiny", "distant", "far", "miniature", "compact",
            "wide", "aerial", "overhead", "panoramic", "vast", "expansive"
        ]
        
        large_target_indicators = [
            "close", "large", "big", "huge", "giant", "enormous",
            "detailed", "macro", "zoomed", "focused"
        ]
        
        small_score = sum(1 for word in small_target_indicators if word in prompt_lower)
        large_score = sum(1 for word in large_target_indicators if word in prompt_lower)
        
        effectiveness_score = small_score - large_score
        
        return {
            "small_target_score": small_score,
            "large_target_score": large_score,
            "effectiveness_score": effectiveness_score,
            "recommendation": "good" if effectiveness_score > 0 else "needs_improvement",
            "suggestions": self._get_improvement_suggestions(prompt_lower)
        }
    
    def _get_improvement_suggestions(self, prompt_lower: str) -> List[str]:
        """获取改进建议"""
        suggestions = []
        
        if "close" in prompt_lower or "detailed" in prompt_lower:
            suggestions.append("移除近距离或详细描述词汇")
        
        if not any(word in prompt_lower for word in ["wide", "aerial", "distant"]):
            suggestions.append("添加广角或远距离构图词汇")
        
        if not any(word in prompt_lower for word in ["small", "tiny", "miniature"]):
            suggestions.append("添加尺寸控制词汇")
        
        return suggestions
