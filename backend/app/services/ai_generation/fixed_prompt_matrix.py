"""
固定提示词矩阵配置
为所有载具×天气×场景组合提供预定义的完整提示词
"""

from typing import Dict, List, Tuple, Any

# 固定提示词矩阵 - 3种载具 × 4种天气 × 3种场景 = 36种组合
FIXED_PROMPT_MATRIX = {
    # 坦克 - 雨天
    ("坦克", "雨天", "城市"): {
        "positive": "(one single M1A2 Abrams main battle tank:1.5), solo vehicle, one main cannon, realistic weathered armor, detailed tracks, dynamic low-angle shot, centered, sharp focus on the tank, rumbling through a wet city street, torrential downpour, heavy rain, water splashes, (rain-blurred urban background with bokeh city lights:0.8), cinematic lighting, dramatic rim light on wet metal, professional military photography, photorealistic, ultra-detailed, masterpiece, best quality, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple tanks, two tanks, group, convoy:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, fused parts, extra parts:1.3), multiple cannons, double barrels, multiple turrets, deformed hull, blurry subject, out of frame, cropped, distant view, sunny, dry, peaceful, parade, museum, drawing, painting, cartoon, sci-fi, text, watermark, signature"
    },
    ("坦克", "雨天", "岛屿"): {
        "positive": "(one single M1A2 Abrams main battle tank:1.5), solo vehicle, one main cannon, mud-splattered hull, close-up shot on turret, positioned on a rocky island shore, violent tropical storm, (stormy seas and dark clouds as a dramatic, blurred backdrop:0.9), sea spray splashing against armor, professional military photography, photorealistic, ultra-detailed, masterpiece, best quality, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple tanks, two tanks, group:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, fused parts, extra parts:1.3), multiple cannons, double barrels, multiple turrets, deformed hull, blurry subject, out of frame, cropped, distant, sunny, calm sea, peaceful, text, watermark, signature"
    },
    ("坦克", "雨天", "乡村"): {
        "positive": "(one single M1A2 Abrams main battle tank:1.5), solo vehicle, one main cannon, mud-caked tracks, medium shot with shallow depth of field, advancing through a rain-soaked muddy field, (rural landscape and storm clouds softly out of focus in the background:0.8), visible rain streaks, professional military photography, photorealistic, ultra-detailed, masterpiece, best quality, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple tanks, two tanks, group, convoy:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, fused parts, extra parts:1.3), multiple cannons, double barrels, multiple turrets, deformed hull, blurry subject, out of frame, cropped, distant, sunny, dry field, peaceful, text, watermark, signature"
    },
    
    # 坦克 - 雪天
    ("坦克", "雪天", "城市"): {
        "positive": "(one single Leopard 2A7 with winter camouflage:1.5), solo vehicle, one main cannon, frost-covered armor, snow-packed tracks, powerful composition, halted on a snow-blanketed city street during a blizzard, (snow-covered buildings with heavy bokeh in background:0.8), professional military photography, photorealistic, ultra-detailed, masterpiece, best quality, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple tanks, two tanks, group, convoy:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, fused parts, extra parts:1.3), multiple cannons, double barrels, multiple turrets, deformed hull, blurry subject, out of frame, cropped, distant, summer, rain, sun, peaceful, text, watermark, signature"
    },
    ("坦克", "雪天", "岛屿"): {
        "positive": "(one single T-90M with arctic camouflage:1.5), solo vehicle, one main cannon, dramatic shot against a vast frozen landscape, positioned on an icy island coast, (frozen sea and snow-covered terrain as a stark, blurred backdrop:0.9), sharp details on ice crystals on the cannon, professional military photography, photorealistic, ultra-detailed, masterpiece, best quality, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple tanks, two tanks, group:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, fused parts, extra parts:1.3), multiple cannons, double barrels, multiple turrets, deformed hull, blurry subject, out of frame, cropped, distant, tropical, warm, rain, peaceful, text, watermark, signature"
    },
    ("坦克", "雪天", "乡村"): {
        "positive": "(one single M1A2 Abrams with winter camouflage:1.5), solo vehicle, one main cannon, stunning medium shot, moving through a deep snowfield, sharp focus on the tank, visible exhaust vapor in the cold air, (background of snow-laden trees and fields beautifully blurred:0.8), professional military photography, photorealistic, ultra-detailed, masterpiece, best quality, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple tanks, two tanks, group, convoy:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, fused parts, extra parts:1.3), multiple cannons, double barrels, multiple turrets, deformed hull, blurry subject, out of frame, cropped, distant, summer, warm, rain, peaceful, text, watermark, signature"
    },
    
    # 坦克 - 大雾
    ("坦克", "大雾", "城市"): {
        "positive": "(one solitary modern main battle tank:1.5), solo vehicle, one main cannon, emerging from thick fog, silhouette, headlights cutting through dense mist, focus entirely on the tank, (fog-shrouded city buildings barely visible, creating an atmospheric, abstract background:0.7), mysterious and powerful image, professional military photography, photorealistic, ultra-detailed, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple tanks, two tanks, group, convoy:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, fused parts, extra parts:1.3), multiple cannons, double barrels, multiple turrets, deformed hull, blurry subject, out of frame, cropped, distant, clear weather, sunny, high visibility, text, watermark, signature"
    },
    ("坦克", "大雾", "岛屿"): {
        "positive": "(one single modern main battle tank:1.5), solo vehicle, one main cannon, form partially obscured by thick sea fog, close-up on front armor wet with condensation, guarding a foggy island coastline, (the sea and shore are lost in a heavy, diffused mist:0.8), eerie, quiet atmosphere, professional military photography, photorealistic, ultra-detailed, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple tanks, two tanks, group:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, fused parts, extra parts:1.3), multiple cannons, double barrels, multiple turrets, deformed hull, blurry subject, out of frame, cropped, distant, clear weather, sunny, high visibility, text, watermark, signature"
    },
    ("坦克", "大雾", "乡村"): {
        "positive": "(one single powerful main battle tank:1.5), solo vehicle, one main cannon, moving through a foggy rural landscape, tank is the only object in sharp focus, volumetric fog swirling around its massive frame, (background of fields and trees is a soft, misty blur:0.7), professional military photography, photorealistic, ultra-detailed, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple tanks, two tanks, group, convoy:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, fused parts, extra parts:1.3), multiple cannons, double barrels, multiple turrets, deformed hull, blurry subject, out of frame, cropped, distant, clear weather, sunny, high visibility, text, watermark, signature"
    },
    
    # 坦克 - 夜间
    ("坦克", "夜间", "城市"): {
        "positive": "(one single M1A2 Abrams main battle tank:1.5), solo vehicle, one main cannon, on a night mission, lit by dramatic high-contrast streetlights, casting long dark shadows, sharp focus on the tank's form, (background city at night is a beautiful bokeh of colorful lights:0.9), professional military photography, photorealistic, ultra-detailed, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple tanks, two tanks, group, convoy:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, fused parts, extra parts:1.3), multiple cannons, double barrels, multiple turrets, deformed hull, blurry subject, out of frame, cropped, distant, daytime, sun, bright sky, peaceful, text, watermark, signature"
    },
    ("坦克", "夜间", "岛屿"): {
        "positive": "(one single M1A2 Abrams main battle tank:1.5), solo vehicle, one main cannon, silhouette sharply defined against the night sky, positioned on a dark island, moonlight glinting off its cannon and turret, (starry sky and dark ocean serve as a simple, dark backdrop:0.8), professional military photography, photorealistic, ultra-detailed, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple tanks, two tanks, group:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, fused parts, extra parts:1.3), multiple cannons, double barrels, multiple turrets, deformed hull, blurry subject, out of frame, cropped, distant, daytime, sun, bright sky, peaceful, text, watermark, signature"
    },
    ("坦克", "夜间", "乡村"): {
        "positive": "(one lone M1A2 Abrams main battle tank:1.5), solo vehicle, one main cannon, in a stealth position in the dark countryside, tight shot, focus on the tank under moonlight, tactical lights off, silent waiting presence, (rural background is almost completely black, lost in shadow:0.8), professional military photography, photorealistic, ultra-detailed, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple tanks, two tanks, group, convoy:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, fused parts, extra parts:1.3), multiple cannons, double barrels, multiple turrets, deformed hull, blurry subject, out of frame, cropped, distant, daytime, sun, bright sky, peaceful, text, watermark, signature"
    },

    # 战机 - 雨天
    ("战机", "雨天", "城市"): {
        "positive": "(one single F-35 Lightning II fighter jet:1.5), solo aircraft, single cockpit, symmetrical wings, dynamic tracking shot, flying at high speed over a city in a downpour, sharp focus on the jet, water droplets streaking across its canopy, (rain-swept city below is blurred by motion and weather:0.8), professional aviation photography, photorealistic, ultra-detailed, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple jets, two jets, squadron, formation:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, extra wings, missing parts:1.3), multiple cockpits, deformed fuselage, propeller, biplane, blurry subject, static, on the ground, out of frame, cropped, distant, sunny, text, watermark, signature"
    },
    ("战机", "雨天", "岛屿"): {
        "positive": "(one single F-22 Raptor fighter jet:1.5), solo aircraft, single cockpit, symmetrical wings, banking sharply, close-up shot of aircraft battling a tropical storm near an island, (stormy sea and island are a dramatic, out-of-focus backdrop:0.9), vapor trails forming at wingtips, professional aviation photography, photorealistic, ultra-detailed, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple jets, two jets, squadron, formation:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, extra wings, missing parts:1.3), multiple cockpits, deformed fuselage, propeller, biplane, blurry subject, static, on the ground, out of frame, cropped, distant, sunny, calm sea, text, watermark, signature"
    },
    ("战机", "雨天", "乡村"): {
        "positive": "(one lone F-35 Lightning II fighter jet:1.5), solo aircraft, single cockpit, symmetrical wings, breathtaking low-altitude flight, tight focus on aircraft, afterburner glowing intensely against rain and dark clouds, (wet countryside below is a fast-moving blur:0.7), professional aviation photography, photorealistic, ultra-detailed, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple jets, two jets, squadron, formation:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, extra wings, missing parts:1.3), multiple cockpits, deformed fuselage, propeller, biplane, blurry subject, static, on the ground, out of frame, cropped, distant, sunny, dry, text, watermark, signature"
    },

    # 战机 - 雪天
    ("战机", "雪天", "城市"): {
        "positive": "(one single Su-57 with winter camouflage:1.5), solo aircraft, single cockpit, symmetrical wings, soaring over a city during heavy snowfall, jet in sharp focus, snowflakes swirling around it, (snow-covered city below is softened and blurred by distance and snow:0.8), professional aviation photography, photorealistic, ultra-detailed, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple jets, two jets, squadron, formation:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, extra wings, missing parts:1.3), multiple cockpits, deformed fuselage, propeller, biplane, blurry subject, static, on the ground, out of frame, cropped, distant, summer, rain, text, watermark, signature"
    },
    ("战机", "雪天", "岛屿"): {
        "positive": "(one single F-22 Raptor fighter jet:1.5), solo aircraft, single cockpit, symmetrical wings, tight shot, flying low over a frozen arctic island, sharp details of frost on its wings, (vast, icy landscape provides a simple, blurred background:0.9), professional aviation photography, photorealistic, ultra-detailed, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple jets, two jets, squadron, formation:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, extra wings, missing parts:1.3), multiple cockpits, deformed fuselage, propeller, biplane, blurry subject, static, on the ground, out of frame, cropped, distant, tropical, warm, rain, text, watermark, signature"
    },
    ("战机", "雪天", "乡村"): {
        "positive": "(one single F-35 Lightning II fighter jet:1.5), solo aircraft, single cockpit, symmetrical wings, high-speed pass, jet is crystal clear, leaving a sharp contrail in the cold air, (snow-blanketed rural scenery below is beautifully out of focus:0.8), dynamic action shot, professional aviation photography, photorealistic, ultra-detailed, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple jets, two jets, squadron, formation:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, extra wings, missing parts:1.3), multiple cockpits, deformed fuselage, propeller, biplane, blurry subject, static, on the ground, out of frame, cropped, distant, summer, warm, rain, text, watermark, signature"
    },

    # 战机 - 大雾
    ("战机", "大雾", "城市"): {
        "positive": "(one lone F-22 Raptor stealth fighter:1.5), solo aircraft, single cockpit, symmetrical wings, flying just above a thick sea of fog covering a city, jet is the only object in sharp focus, (tops of skyscrapers poking through the fog create a surreal, soft background:0.7), ethereal lighting, professional aviation photography, photorealistic, ultra-detailed, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple jets, two jets, squadron, formation:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, extra wings, missing parts:1.3), multiple cockpits, deformed fuselage, propeller, biplane, blurry subject, out of frame, cropped, distant, clear sky, sunny, high visibility, text, watermark, signature"
    },
    ("战机", "大雾", "岛屿"): {
        "positive": "(one single F-35 Lightning II fighter jet:1.5), solo aircraft, single cockpit, symmetrical wings, navigating through mist, cockpit and wing cutting through dense sea fog near an island, (island's cliffs are ghostly, blurred shapes in the background:0.8), tense, low-visibility atmosphere, professional aviation photography, photorealistic, ultra-detailed, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple jets, two jets, squadron, formation:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, extra wings, missing parts:1.3), multiple cockpits, deformed fuselage, propeller, biplane, blurry subject, out of frame, cropped, distant, clear sky, sunny, high visibility, text, watermark, signature"
    },
    ("战机", "大雾", "乡村"): {
        "positive": "(one single F-22 Raptor stealth fighter:1.5), solo aircraft, single cockpit, symmetrical wings, silhouette sharply defined as it emerges from a thick fog bank over the countryside, (rural landscape below is completely obscured by a blanket of white fog:0.8), dramatic and mysterious, professional aviation photography, photorealistic, ultra-detailed, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple jets, two jets, squadron, formation:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, extra wings, missing parts:1.3), multiple cockpits, deformed fuselage, propeller, biplane, blurry subject, out of frame, cropped, distant, clear sky, sunny, high visibility, text, watermark, signature"
    },

    # 战机 - 夜间
    ("战机", "夜间", "城市"): {
        "positive": "(one single B-2 Spirit stealth bomber:1.5), solo aircraft, coherent flying wing design, dark angular shape, gliding silently over a city at night, dark sharp silhouette against glowing urban sprawl, (city lights below are a sprawling, beautiful bokeh pattern:0.9), professional aviation photography, photorealistic, ultra-detailed, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple jets, two jets, squadron, formation:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, extra wings, missing parts:1.3), multiple cockpits, deformed fuselage, propeller, biplane, blurry subject, out of frame, cropped, distant, daytime, sun, bright sky, text, watermark, signature"
    },
    ("战机", "夜间", "岛屿"): {
        "positive": "(one single F-35 Lightning II fighter jet:1.5), solo aircraft, on a night mission, dramatic shot, glowing afterburner and cockpit lights are main light sources, (dark island and moonlit ocean provide a minimalist, dark background:0.8), professional aviation photography, photorealistic, ultra-detailed, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple jets, two jets, squadron, formation:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, extra wings, missing parts:1.3), multiple cockpits, deformed fuselage, propeller, biplane, blurry subject, out of frame, cropped, distant, daytime, sun, bright sky, text, watermark, signature"
    },
    ("战机", "夜间", "乡村"): {
        "positive": "(one single F-22 Raptor fighter jet:1.5), solo aircraft, at supersonic speed, glowing afterburners as a streak of light across a dark rural landscape, (moonlit countryside below is a blur of motion:0.7), high-speed, high-energy feel, dynamic action shot, professional aviation photography, photorealistic, ultra-detailed, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple jets, two jets, squadron, formation:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, extra wings, missing parts:1.3), multiple cockpits, deformed fuselage, propeller, biplane, blurry subject, out of frame, cropped, distant, daytime, sun, static, high altitude, text, watermark, signature"
    },

    # 舰艇 - 雨天
    ("舰艇", "雨天", "城市"): {
        "positive": "(one single Arleigh Burke-class destroyer:1.5), solo ship, single bow, coherent superstructure, docked in a harbor during a heavy storm, sharp focus on wet superstructure and radar arrays, (city skyline and harbor are a rain-blurred, atmospheric background:0.8), professional naval photography, photorealistic, ultra-detailed, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple ships, two ships, fleet:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, multiple hulls, multiple bows:1.3), deformed superstructure, blurry subject, out of frame, cropped, distant, sunny, clear sky, open sea, small boat, yacht, text, watermark, signature"
    },
    ("舰艇", "雨天", "岛屿"): {
        "positive": "(one massive Nimitz-class aircraft carrier:1.5), solo ship, single hull and island, powering through a storm, focus on the bow cutting through huge violent waves near an island, (stormy sea and dark island are a dramatic, blurred backdrop:0.9), epic scale, professional naval photography, photorealistic, ultra-detailed, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple ships, two ships, fleet:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, multiple hulls, multiple bows:1.3), deformed superstructure, blurry subject, out of frame, cropped, distant, sunny, calm sea, small boat, yacht, text, watermark, signature"
    },
    ("舰艇", "雨天", "乡村"): {
        "positive": "(one lone modern naval frigate:1.5), solo ship, single hull, patrolling a coast, shallow depth of field, ship in sharp focus during rain, (rural, forested coastline behind it is soft and out of focus:0.8), moody and cinematic, professional naval photography, photorealistic, ultra-detailed, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple ships, two ships, fleet:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, multiple hulls, multiple bows:1.3), deformed superstructure, blurry subject, out of frame, cropped, distant, sunny, clear sky, urban, harbor, text, watermark, signature"
    },

    # 舰艇 - 雪天
    ("舰艇", "雪天", "城市"): {
        "positive": "(one single Zumwalt-class destroyer covered in snow:1.5), solo ship, single bow, coherent superstructure, close-up shot on snow accumulating on its angular structure, moored in a frozen city harbor, (city background blurred by heavy snowfall and bokeh:0.8), professional naval photography, photorealistic, ultra-detailed, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple ships, two ships, fleet:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, multiple hulls, multiple bows:1.3), deformed superstructure, blurry subject, out of frame, cropped, distant, summer, warm, rain, small boat, text, watermark, signature"
    },
    ("舰艇", "雪天", "岛屿"): {
        "positive": "(one single Virginia-class submarine surfacing:1.5), solo vessel, single hull, black hull stark contrast to white environment, breaking through ice near a snowy arctic island, tightly focused on the submarine, (icy sea and island are a vast, blurred expanse:0.9), professional naval photography, photorealistic, ultra-detailed, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple ships, two ships, fleet:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, multiple hulls:1.3), deformed superstructure, surface ship, aircraft carrier, blurry subject, out of frame, cropped, distant, tropical, warm, rain, text, watermark, signature"
    },
    ("舰艇", "雪天", "乡村"): {
        "positive": "(one single Arleigh Burke-class destroyer:1.5), solo ship, single hull, sailing along a snow-covered rural coast, ship in crisp focus, (snowy pine forests on the shore are softly blurred in the background:0.8), serene and cold, professional naval photography, photorealistic, ultra-detailed, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple ships, two ships, fleet:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, multiple hulls, multiple bows:1.3), deformed superstructure, blurry subject, out of frame, cropped, distant, summer, warm, rain, urban, harbor, text, watermark, signature"
    },

    # 舰艇 - 大雾
    ("舰艇", "大雾", "城市"): {
        "positive": "(one enormous Nimitz-class aircraft carrier:1.5), solo ship, single island superstructure, silhouette emerging from thick harbor fog, focused on the ship's imposing island, (city and port are ghostly, out-of-focus shapes in the dense fog:0.7), epic and mysterious, professional naval photography, photorealistic, ultra-detailed, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple ships, two ships, fleet:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, multiple hulls, multiple bows:1.3), deformed superstructure, blurry subject, out of frame, cropped, distant, clear sky, sunny, high visibility, small boat, text, watermark, signature"
    },
    ("舰艇", "大雾", "岛屿"): {
        "positive": "(one single Zumwalt-class stealth destroyer:1.5), solo ship, single coherent hull, partially hidden by mist, patrolling silently in dense sea fog near an island, focus on the ship's angular lines, (foggy sea and island are a diffused, atmospheric background:0.8), professional naval photography, photorealistic, ultra-detailed, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple ships, two ships, fleet:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, multiple hulls, multiple bows:1.3), deformed superstructure, blurry subject, out of frame, cropped, distant, clear sky, sunny, high visibility, text, watermark, signature"
    },
    ("舰艇", "大雾", "乡村"): {
        "positive": "(one single Iowa-class battleship:1.5), solo ship, single hull, ghostly form anchored in thick fog off a rural coast, shot focused on the ship's massive gun batteries, (water and shoreline are lost in a heavy, uniform mist:0.7), eerie and cinematic, professional naval photography, photorealistic, ultra-detailed, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple ships, two ships, fleet:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, multiple hulls, multiple bows:1.3), deformed superstructure, blurry subject, out of frame, cropped, distant, clear sky, sunny, high visibility, urban, text, watermark, signature"
    },

    # 舰艇 - 夜间
    ("舰艇", "夜间", "城市"): {
        "positive": "(one single modern warship, Arleigh Burke-class destroyer:1.5), solo ship, single hull, docked in a city harbor at night, deck lights on, sharp focus on the ship, (background is a vibrant bokeh of city lights reflecting on the water:0.9), professional naval photography, photorealistic, ultra-detailed, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple ships, two ships, fleet:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, multiple hulls, multiple bows:1.3), deformed superstructure, blurry subject, out of frame, cropped, distant, daytime, sun, rural, open sea, text, watermark, signature"
    },
    ("舰艇", "夜间", "岛屿"): {
        "positive": "(one single Ticonderoga-class cruiser:1.5), solo ship, conducting night maneuvers near an island, under a starry sky, ship's lights illuminating the water, (dark island and ocean serve as a vast, dark canvas:0.8), epic and strategic, professional naval photography, photorealistic, ultra-detailed, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple ships, two ships, fleet, aircraft carrier group:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, multiple hulls, multiple bows:1.3), deformed superstructure, blurry subject, out of frame, cropped, distant, daytime, sun, urban, text, watermark, signature"
    },
    ("舰艇", "夜间", "乡村"): {
        "positive": "(one solitary Virginia-class submarine on the surface:1.5), solo vessel, single conning tower, in a dark rural bay under the moon, tight shot focusing on its sleek wet hull, (dark, tree-lined shores are a simple, shadowy backdrop:0.8), stealthy and tense, professional naval photography, photorealistic, ultra-detailed, 8k",
        "negative": "(worst quality, low quality, normal quality:1.4), (multiple ships, two ships, fleet:1.5), (deformed, disfigured, malformed, bad anatomy, asymmetrical, multiple hulls:1.3), deformed superstructure, surface ship, aircraft carrier, blurry subject, out of frame, cropped, distant, daytime, sun, urban, text, watermark, signature"
    }
}





# 支持的选项
SUPPORTED_VEHICLES = ["坦克", "战机", "舰艇"]
SUPPORTED_WEATHER = ["雨天", "雪天", "大雾", "夜间"]
SUPPORTED_SCENES = ["城市", "岛屿", "乡村"]

def get_fixed_prompt(vehicle: str, weather: str, scene: str) -> Tuple[str, str]:
    """
    获取固定提示词

    Args:
        vehicle: 载具类型 (坦克/战机/舰艇)
        weather: 天气条件 (雨天/雪天/大雾/夜间)
        scene: 场景环境 (城市/岛屿/乡村)

    Returns:
        tuple[str, str]: (正面提示词, 负面提示词)

    Raises:
        KeyError: 当组合不存在时
        ValueError: 当参数无效时
    """
    # 验证参数
    if vehicle not in SUPPORTED_VEHICLES:
        raise ValueError(f"不支持的载具类型: {vehicle}. 支持的类型: {SUPPORTED_VEHICLES}")
    if weather not in SUPPORTED_WEATHER:
        raise ValueError(f"不支持的天气条件: {weather}. 支持的条件: {SUPPORTED_WEATHER}")
    if scene not in SUPPORTED_SCENES:
        raise ValueError(f"不支持的场景环境: {scene}. 支持的环境: {SUPPORTED_SCENES}")

    # 获取提示词
    key = (vehicle, weather, scene)
    if key not in FIXED_PROMPT_MATRIX:
        raise KeyError(f"未找到组合 {key} 的提示词")

    prompt_data = FIXED_PROMPT_MATRIX[key]
    return prompt_data["positive"], prompt_data["negative"]

def get_all_combinations() -> List[Tuple[str, str, str]]:
    """
    获取所有支持的载具×天气×场景组合

    Returns:
        list[tuple[str, str, str]]: 所有组合的列表
    """
    return list(FIXED_PROMPT_MATRIX.keys())

def validate_matrix_completeness() -> Dict[str, Any]:
    """
    验证提示词矩阵的完整性

    Returns:
        dict: 验证结果
    """
    expected_combinations = []
    for vehicle in SUPPORTED_VEHICLES:
        for weather in SUPPORTED_WEATHER:
            for scene in SUPPORTED_SCENES:
                expected_combinations.append((vehicle, weather, scene))

    actual_combinations = set(FIXED_PROMPT_MATRIX.keys())
    expected_combinations_set = set(expected_combinations)

    missing = expected_combinations_set - actual_combinations
    extra = actual_combinations - expected_combinations_set

    return {
        "total_expected": len(expected_combinations),
        "total_actual": len(actual_combinations),
        "is_complete": len(missing) == 0 and len(extra) == 0,
        "missing_combinations": list(missing),
        "extra_combinations": list(extra)
    }
