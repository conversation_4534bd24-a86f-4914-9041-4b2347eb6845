"""
集成的传统图片生成服务
整合所有传统图片处理功能，提供统一的接口
"""
import os
import logging
import asyncio
from typing import Dict, Any, List, Optional, Tuple, Union
from pathlib import Path
import time

from .enhanced_image_processor import EnhancedImageProcessor
from .format_converter import ImageFormatConverter
from .advanced_effects import AdvancedEffectsProcessor
from .enhanced_compositor import EnhancedCompositor
from .performance_optimizer import PerformanceOptimizer
from .core_types import ImageData, ProcessingResult
from ..common.annotation_generator import AnnotationGenerator

logger = logging.getLogger(__name__)


class IntegratedTraditionalService:
    """集成的传统图片生成服务"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化集成服务
        
        Args:
            config: 配置参数
        """
        self.config = config or {
            'output_dir': 'data/generated/traditional',
            'temp_dir': 'data/temp',
            'quality': 95,
            'enable_optimization': True,
            'enable_caching': True,
            'max_workers': 4,
            'supported_formats': ['PNG', 'JPEG', 'WEBP', 'GIF', 'BMP', 'TIFF']
        }
        
        # 确保目录存在
        os.makedirs(self.config['output_dir'], exist_ok=True)
        os.makedirs(self.config['temp_dir'], exist_ok=True)
        
        # 初始化各个处理器
        self.image_processor = EnhancedImageProcessor(self.config)
        self.format_converter = ImageFormatConverter(self.config)
        self.effects_processor = AdvancedEffectsProcessor(self.config)
        self.compositor = EnhancedCompositor(self.config)
        self.annotation_generator = AnnotationGenerator()
        
        # 性能优化器（可选）
        self.optimizer = None
        if self.config.get('enable_optimization', True):
            self.optimizer = PerformanceOptimizer(self.config)
        
        self.is_initialized = False
        
        logger.info("集成传统图片生成服务初始化完成")
    
    async def initialize(self) -> bool:
        """
        初始化服务
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            self.is_initialized = True
            logger.info("集成传统图片生成服务初始化成功")
            return True
        except Exception as e:
            logger.error(f"集成传统图片生成服务初始化失败: {str(e)}")
            return False
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.optimizer:
                self.optimizer.cleanup()
            self.is_initialized = False
            logger.info("集成传统图片生成服务资源清理完成")
        except Exception as e:
            logger.error(f"集成传统图片生成服务清理失败: {str(e)}")
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取服务状态
        
        Returns:
            Dict: 服务状态信息
        """
        return {
            'initialized': self.is_initialized,
            'output_dir': self.config['output_dir'],
            'supported_formats': self.config['supported_formats'],
            'optimization_enabled': self.optimizer is not None,
            'processors': {
                'image_processor': True,
                'format_converter': True,
                'effects_processor': True,
                'compositor': True,
                'annotation_generator': True
            }
        }
    
    async def generate_images(
        self,
        military_target: str,
        weather: str,
        scene: str,
        num_images: int = 1,
        **kwargs
    ) -> Dict[str, Any]:
        """
        生成图像（主要接口）
        
        Args:
            military_target: 军事目标类型
            weather: 天气条件
            scene: 场景环境
            num_images: 生成图像数量
            **kwargs: 其他参数
            
        Returns:
            Dict: 生成结果
        """
        if not self.is_initialized:
            raise RuntimeError("服务未初始化")
        
        try:
            logger.info(f"开始集成传统生成: {military_target} + {weather} + {scene}")
            
            results = []
            for i in range(num_images):
                result = await self._generate_single_image(
                    military_target, weather, scene, i, **kwargs
                )
                if result:
                    results.append(result)
            
            return {
                "success": True,
                "message": f"成功生成 {len(results)} 张图像",
                "results": results,
                "total_generated": len(results)
            }
            
        except Exception as e:
            logger.error(f"集成传统生成失败: {str(e)}")
            return {
                "success": False,
                "message": f"生成失败: {str(e)}",
                "results": [],
                "total_generated": 0
            }
    
    async def _generate_single_image(
        self,
        military_target: str,
        weather: str,
        scene: str,
        index: int,
        **kwargs
    ) -> Optional[Dict[str, Any]]:
        """
        生成单张图像
        
        Args:
            military_target: 军事目标类型
            weather: 天气条件
            scene: 场景环境
            index: 图像索引
            **kwargs: 其他参数
            
        Returns:
            Dict: 生成结果
        """
        try:
            start_time = time.time()
            
            # 1. 选择和加载素材
            background_path = self._select_background(weather, scene)
            target_path = self._select_target(military_target)
            
            if not background_path or not target_path:
                logger.warning(f"无法找到合适的素材: bg={background_path}, target={target_path}")
                return self._create_fallback_image(military_target, weather, scene, index)
            
            # 2. 加载图片
            background_data = self.image_processor.load_image(background_path)
            target_data = self.image_processor.load_image(target_path)
            
            # 3. 预处理目标图片
            target_data = await self._preprocess_target(target_data, **kwargs)
            
            # 4. 应用效果
            if kwargs.get('apply_effects', True):
                target_data = await self._apply_effects(target_data, weather, **kwargs)
            
            # 5. 合成图片
            composite_result = await self._composite_images(
                background_data, target_data, **kwargs
            )
            
            if not composite_result.success:
                logger.error(f"图片合成失败: {composite_result.error_message}")
                return self._create_fallback_image(military_target, weather, scene, index)
            
            # 6. 后处理
            final_result = await self._postprocess_image(composite_result.image, **kwargs)
            
            # 7. 保存图片
            filename = f"{military_target}_{weather}_{scene}_{index:03d}.jpg"
            output_path = os.path.join(self.config['output_dir'], filename)
            
            save_success = self.image_processor.save_image(
                final_result, output_path, 
                format='JPEG', quality=self.config['quality']
            )
            
            if not save_success:
                logger.error(f"保存图片失败: {output_path}")
                return None
            
            # 8. 生成标注
            bbox, segmentation = self.annotation_generator.generate_auto_annotation(
                output_path, military_target
            )
            
            processing_time = time.time() - start_time
            
            return {
                "image_path": output_path,
                "filename": filename,
                "target_type": military_target,
                "weather": weather,
                "scene": scene,
                "bbox": bbox,
                "segmentation": segmentation,
                "size": final_result.image.size,
                "processing_time": processing_time,
                "metadata": final_result.metadata
            }
            
        except Exception as e:
            logger.error(f"生成单张图像失败: {str(e)}")
            return self._create_fallback_image(military_target, weather, scene, index)
    
    async def _preprocess_target(self, target_data: ImageData, **kwargs) -> ImageData:
        """
        预处理目标图片
        
        Args:
            target_data: 目标图片数据
            **kwargs: 处理参数
            
        Returns:
            ImageData: 处理后的图片数据
        """
        try:
            result_data = target_data
            
            # 调整尺寸
            target_size = kwargs.get('target_size', (256, 256))
            if target_size:
                resize_result = self.image_processor.resize_image(
                    result_data, target_size, 
                    maintain_aspect=kwargs.get('maintain_aspect', True)
                )
                if resize_result.success:
                    result_data = resize_result.image
            
            # 旋转
            rotation_angle = kwargs.get('rotation_angle', 0)
            if rotation_angle != 0:
                rotate_result = self.image_processor.rotate_image(
                    result_data, rotation_angle
                )
                if rotate_result.success:
                    result_data = rotate_result.image
            
            # 翻转
            if kwargs.get('flip_horizontal', False):
                flip_result = self.image_processor.flip_image(result_data, 'horizontal')
                if flip_result.success:
                    result_data = flip_result.image
            
            if kwargs.get('flip_vertical', False):
                flip_result = self.image_processor.flip_image(result_data, 'vertical')
                if flip_result.success:
                    result_data = flip_result.image
            
            return result_data
            
        except Exception as e:
            logger.error(f"目标图片预处理失败: {str(e)}")
            return target_data
    
    async def _apply_effects(self, image_data: ImageData, weather: str, **kwargs) -> ImageData:
        """
        应用效果
        
        Args:
            image_data: 图片数据
            weather: 天气条件
            **kwargs: 效果参数
            
        Returns:
            ImageData: 应用效果后的图片数据
        """
        try:
            result_data = image_data
            
            # 根据天气应用相应效果
            weather_effects = {
                'rainy': 'watercolor',
                'foggy': 'soft_light',
                'snowy': 'vintage',
                'night': 'sepia'
            }
            
            effect_type = weather_effects.get(weather.lower())
            if effect_type and kwargs.get('apply_weather_effects', True):
                effect_result = self.effects_processor.apply_artistic_filter(
                    result_data, effect_type, 
                    intensity=kwargs.get('effect_intensity', 0.3)
                )
                if effect_result.success:
                    result_data = effect_result.image
            
            # 色彩调整
            if kwargs.get('adjust_colors', True):
                color_result = self.image_processor.adjust_color(
                    result_data,
                    brightness=kwargs.get('brightness', 1.0),
                    contrast=kwargs.get('contrast', 1.0),
                    saturation=kwargs.get('saturation', 1.0)
                )
                if color_result.success:
                    result_data = color_result.image
            
            return result_data
            
        except Exception as e:
            logger.error(f"应用效果失败: {str(e)}")
            return image_data
    
    async def _composite_images(
        self, 
        background_data: ImageData, 
        target_data: ImageData, 
        **kwargs
    ) -> ProcessingResult:
        """
        合成图片
        
        Args:
            background_data: 背景图片数据
            target_data: 目标图片数据
            **kwargs: 合成参数
            
        Returns:
            ProcessingResult: 合成结果
        """
        try:
            # 计算目标位置
            bg_size = background_data.image.size
            target_size = target_data.image.size
            
            # 默认居中放置
            position = kwargs.get('position')
            if position is None:
                x = (bg_size[0] - target_size[0]) // 2
                y = (bg_size[1] - target_size[1]) // 2
                position = (x, y)
            
            # 执行合成
            result = self.compositor.composite_images(
                background_data, target_data,
                blend_mode=kwargs.get('blend_mode', 'normal'),
                opacity=kwargs.get('opacity', 1.0),
                position=position,
                enable_shadow=kwargs.get('enable_shadow', True),
                shadow_config=kwargs.get('shadow_config', {})
            )
            
            return result
            
        except Exception as e:
            logger.error(f"图片合成失败: {str(e)}")
            return ProcessingResult(
                success=False,
                error_message=str(e),
                processing_time=0.0
            )
    
    async def _postprocess_image(self, image_data: ImageData, **kwargs) -> ImageData:
        """
        后处理图片
        
        Args:
            image_data: 图片数据
            **kwargs: 处理参数
            
        Returns:
            ImageData: 处理后的图片数据
        """
        try:
            result_data = image_data
            
            # 应用滤镜
            filter_type = kwargs.get('filter_type')
            if filter_type:
                filter_result = self.image_processor.apply_filter(
                    result_data, filter_type, **kwargs.get('filter_params', {})
                )
                if filter_result.success:
                    result_data = filter_result.image
            
            # 最终尺寸调整
            final_size = kwargs.get('final_size', (512, 512))
            if final_size and result_data.image.size != final_size:
                resize_result = self.image_processor.resize_image(
                    result_data, final_size, maintain_aspect=False
                )
                if resize_result.success:
                    result_data = resize_result.image
            
            return result_data
            
        except Exception as e:
            logger.error(f"图片后处理失败: {str(e)}")
            return image_data
    
    def _select_background(self, weather: str, scene: str) -> Optional[str]:
        """选择背景图片"""
        # 这里应该实现从pic_resource目录选择背景的逻辑
        # 暂时返回None，让调用者处理
        return None
    
    def _select_target(self, military_target: str) -> Optional[str]:
        """选择目标图片"""
        # 这里应该实现从pic_resource目录选择目标的逻辑
        # 暂时返回None，让调用者处理
        return None
    
    def _create_fallback_image(
        self, military_target: str, weather: str, scene: str, index: int
    ) -> Dict[str, Any]:
        """创建备用图片"""
        # 实现备用图片创建逻辑
        return {
            "image_path": "",
            "filename": f"{military_target}_{weather}_{scene}_{index:03d}.jpg",
            "target_type": military_target,
            "weather": weather,
            "scene": scene,
            "bbox": [0, 0, 100, 100],
            "segmentation": [],
            "size": (512, 512),
            "fallback": True
        }
