"""
批量处理和性能优化模块
"""
import asyncio
import logging
from typing import Dict, Any, List, Optional, Callable
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import multiprocessing
import time
import gc
import psutil
import os
from dataclasses import dataclass

from .core_types import CompositionRequest, ProcessingResult
from .composition_service import TraditionalCompositionService

logger = logging.getLogger(__name__)


@dataclass
class BatchConfig:
    """批量处理配置"""
    max_workers: int = 4
    use_multiprocessing: bool = False
    memory_limit_mb: int = 2048
    enable_memory_monitoring: bool = True
    progress_callback: Optional[Callable] = None
    chunk_size: int = 10
    enable_gc: bool = True


@dataclass
class BatchProgress:
    """批量处理进度"""
    total_tasks: int
    completed_tasks: int
    failed_tasks: int
    current_task: str
    processing_time: float
    estimated_remaining: float
    memory_usage_mb: float


class MemoryMonitor:
    """内存监控器"""
    
    def __init__(self, limit_mb: int = 2048):
        self.limit_mb = limit_mb
        self.process = psutil.Process()
    
    def get_memory_usage(self) -> float:
        """获取当前内存使用量（MB）"""
        try:
            memory_info = self.process.memory_info()
            return memory_info.rss / 1024 / 1024
        except Exception:
            return 0.0
    
    def is_memory_limit_exceeded(self) -> bool:
        """检查是否超过内存限制"""
        return self.get_memory_usage() > self.limit_mb
    
    def force_gc(self):
        """强制垃圾回收"""
        gc.collect()


class BatchProcessor:
    """批量处理器"""
    
    def __init__(self, config: BatchConfig = None):
        self.config = config or BatchConfig()
        self.memory_monitor = MemoryMonitor(self.config.memory_limit_mb)
        self.composition_service = TraditionalCompositionService()
        self._is_cancelled = False
        
    async def initialize(self) -> bool:
        """初始化批量处理器"""
        try:
            success = await self.composition_service.initialize()
            if success:
                logger.info("批量处理器初始化成功")
            return success
        except Exception as e:
            logger.error(f"批量处理器初始化失败: {str(e)}")
            return False
    
    async def process_batch(self, requests: List[CompositionRequest]) -> Dict[str, Any]:
        """处理批量请求"""
        if not requests:
            return {
                "success": True,
                "results": [],
                "total_processed": 0,
                "total_failed": 0,
                "processing_time": 0.0
            }
        
        start_time = time.time()
        self._is_cancelled = False
        
        # 分块处理
        chunks = self._split_into_chunks(requests, self.config.chunk_size)
        all_results = []
        total_failed = 0
        
        try:
            for chunk_idx, chunk in enumerate(chunks):
                if self._is_cancelled:
                    break
                
                # 内存检查
                if self.config.enable_memory_monitoring:
                    if self.memory_monitor.is_memory_limit_exceeded():
                        logger.warning("内存使用超限，执行垃圾回收")
                        self.memory_monitor.force_gc()
                
                # 处理当前块
                chunk_results = await self._process_chunk(chunk, chunk_idx, len(chunks))
                
                # 统计结果
                for result in chunk_results:
                    if result.success:
                        all_results.append(result)
                    else:
                        total_failed += 1
                
                # 进度回调
                if self.config.progress_callback:
                    progress = BatchProgress(
                        total_tasks=len(requests),
                        completed_tasks=len(all_results),
                        failed_tasks=total_failed,
                        current_task=f"处理块 {chunk_idx + 1}/{len(chunks)}",
                        processing_time=time.time() - start_time,
                        estimated_remaining=self._estimate_remaining_time(
                            len(all_results) + total_failed, len(requests), time.time() - start_time
                        ),
                        memory_usage_mb=self.memory_monitor.get_memory_usage()
                    )
                    self.config.progress_callback(progress)
        
        except Exception as e:
            logger.error(f"批量处理失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "results": all_results,
                "total_processed": len(all_results),
                "total_failed": total_failed,
                "processing_time": time.time() - start_time
            }
        
        # 最终垃圾回收
        if self.config.enable_gc:
            self.memory_monitor.force_gc()
        
        processing_time = time.time() - start_time
        
        return {
            "success": True,
            "results": all_results,
            "total_processed": len(all_results),
            "total_failed": total_failed,
            "processing_time": processing_time,
            "cancelled": self._is_cancelled
        }
    
    async def _process_chunk(self, chunk: List[CompositionRequest], chunk_idx: int, total_chunks: int) -> List[ProcessingResult]:
        """处理单个块"""
        logger.info(f"处理块 {chunk_idx + 1}/{total_chunks}，包含 {len(chunk)} 个任务")
        
        if self.config.use_multiprocessing and len(chunk) > 1:
            return await self._process_chunk_multiprocessing(chunk)
        else:
            return await self._process_chunk_sequential(chunk)
    
    async def _process_chunk_sequential(self, chunk: List[CompositionRequest]) -> List[ProcessingResult]:
        """顺序处理块"""
        results = []
        
        for request in chunk:
            if self._is_cancelled:
                break
            
            try:
                result = await self.composition_service.compose_image(request)
                results.append(result)
            except Exception as e:
                logger.error(f"处理请求失败: {str(e)}")
                results.append(ProcessingResult(
                    success=False,
                    error_message=str(e)
                ))
        
        return results
    
    async def _process_chunk_multiprocessing(self, chunk: List[CompositionRequest]) -> List[ProcessingResult]:
        """多进程处理块"""
        try:
            # 使用进程池处理
            with ProcessPoolExecutor(max_workers=self.config.max_workers) as executor:
                loop = asyncio.get_event_loop()
                
                # 提交任务
                futures = []
                for request in chunk:
                    if self._is_cancelled:
                        break
                    
                    future = loop.run_in_executor(
                        executor, 
                        self._process_single_request_sync, 
                        request
                    )
                    futures.append(future)
                
                # 等待完成
                results = await asyncio.gather(*futures, return_exceptions=True)
                
                # 处理结果
                processed_results = []
                for result in results:
                    if isinstance(result, Exception):
                        logger.error(f"多进程处理异常: {str(result)}")
                        processed_results.append(ProcessingResult(
                            success=False,
                            error_message=str(result)
                        ))
                    else:
                        processed_results.append(result)
                
                return processed_results
                
        except Exception as e:
            logger.error(f"多进程处理失败: {str(e)}")
            # 回退到顺序处理
            return await self._process_chunk_sequential(chunk)
    
    def _process_single_request_sync(self, request: CompositionRequest) -> ProcessingResult:
        """同步处理单个请求（用于多进程）"""
        try:
            # 在子进程中创建新的服务实例
            service = TraditionalCompositionService()
            
            # 同步初始化
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                loop.run_until_complete(service.initialize())
                result = loop.run_until_complete(service.compose_image(request))
                return result
            finally:
                loop.close()
                
        except Exception as e:
            logger.error(f"同步处理失败: {str(e)}")
            return ProcessingResult(
                success=False,
                error_message=str(e)
            )
    
    def _split_into_chunks(self, items: List, chunk_size: int) -> List[List]:
        """将列表分割为块"""
        chunks = []
        for i in range(0, len(items), chunk_size):
            chunks.append(items[i:i + chunk_size])
        return chunks
    
    def _estimate_remaining_time(self, completed: int, total: int, elapsed_time: float) -> float:
        """估算剩余时间"""
        if completed == 0:
            return 0.0
        
        avg_time_per_task = elapsed_time / completed
        remaining_tasks = total - completed
        return avg_time_per_task * remaining_tasks
    
    def cancel(self):
        """取消批量处理"""
        self._is_cancelled = True
        logger.info("批量处理已取消")
    
    async def cleanup(self):
        """清理资源"""
        await self.composition_service.cleanup()
        if self.config.enable_gc:
            self.memory_monitor.force_gc()


class OptimizedBatchProcessor(BatchProcessor):
    """优化的批量处理器"""
    
    def __init__(self, config: BatchConfig = None):
        super().__init__(config)
        self.image_cache = {}
        self.max_cache_size = 50
    
    async def _process_chunk_sequential(self, chunk: List[CompositionRequest]) -> List[ProcessingResult]:
        """优化的顺序处理"""
        results = []
        
        # 预加载常用图像
        await self._preload_common_images(chunk)
        
        for request in chunk:
            if self._is_cancelled:
                break
            
            try:
                # 使用缓存优化
                result = await self._compose_with_cache(request)
                results.append(result)
                
                # 定期清理缓存
                if len(results) % 10 == 0:
                    self._cleanup_cache()
                    
            except Exception as e:
                logger.error(f"优化处理失败: {str(e)}")
                results.append(ProcessingResult(
                    success=False,
                    error_message=str(e)
                ))
        
        return results
    
    async def _preload_common_images(self, chunk: List[CompositionRequest]):
        """预加载常用图像"""
        # 统计图像使用频率
        image_freq = {}
        for request in chunk:
            bg_path = request.background_path
            target_path = request.target_path
            
            image_freq[bg_path] = image_freq.get(bg_path, 0) + 1
            image_freq[target_path] = image_freq.get(target_path, 0) + 1
        
        # 预加载高频图像
        for image_path, freq in image_freq.items():
            if freq > 2 and image_path not in self.image_cache:
                try:
                    from .core_types import load_image
                    self.image_cache[image_path] = load_image(image_path)
                except Exception as e:
                    logger.warning(f"预加载图像失败 {image_path}: {str(e)}")
    
    async def _compose_with_cache(self, request: CompositionRequest) -> ProcessingResult:
        """使用缓存的合成"""
        # 尝试从缓存获取图像
        bg_data = self.image_cache.get(request.background_path)
        target_data = self.image_cache.get(request.target_path)
        
        if bg_data is None:
            from .core_types import load_image
            bg_data = load_image(request.background_path)
        
        if target_data is None:
            from .core_types import load_image
            target_data = load_image(request.target_path)
        
        # 执行合成
        return await self.composition_service.compose_image(request)
    
    def _cleanup_cache(self):
        """清理图像缓存"""
        if len(self.image_cache) > self.max_cache_size:
            # 移除一半的缓存项
            items_to_remove = len(self.image_cache) // 2
            keys_to_remove = list(self.image_cache.keys())[:items_to_remove]
            
            for key in keys_to_remove:
                del self.image_cache[key]
            
            logger.info(f"清理了 {items_to_remove} 个缓存项")


def get_optimal_worker_count() -> int:
    """获取最优工作线程数"""
    cpu_count = multiprocessing.cpu_count()
    memory_gb = psutil.virtual_memory().total / (1024**3)
    
    # 基于CPU和内存计算最优线程数
    cpu_based = min(cpu_count, 8)  # 最多8个线程
    memory_based = max(1, int(memory_gb / 2))  # 每2GB内存一个线程
    
    return min(cpu_based, memory_based)


def create_batch_config(
    max_workers: Optional[int] = None,
    use_multiprocessing: bool = False,
    memory_limit_mb: int = 2048,
    progress_callback: Optional[Callable] = None
) -> BatchConfig:
    """创建批量处理配置"""
    if max_workers is None:
        max_workers = get_optimal_worker_count()
    
    return BatchConfig(
        max_workers=max_workers,
        use_multiprocessing=use_multiprocessing,
        memory_limit_mb=memory_limit_mb,
        progress_callback=progress_callback,
        chunk_size=max(1, max_workers * 2),
        enable_memory_monitoring=True,
        enable_gc=True
    )
