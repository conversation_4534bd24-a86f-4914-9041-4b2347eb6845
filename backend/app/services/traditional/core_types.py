"""
传统图像合成核心数据类型定义
"""
from typing import Dict, List, Tuple, Optional, Union, Any
from dataclasses import dataclass
from enum import Enum
import numpy as np
from PIL import Image


class BlendMode(Enum):
    """图层混合模式"""
    NORMAL = "normal"
    MULTIPLY = "multiply"
    SCREEN = "screen"
    OVERLAY = "overlay"
    SOFT_LIGHT = "soft_light"
    HARD_LIGHT = "hard_light"
    COLOR_DODGE = "color_dodge"
    COLOR_BURN = "color_burn"
    DARKEN = "darken"
    LIGHTEN = "lighten"


class MattingMethod(Enum):
    """抠图方法"""
    GRABCUT = "grabcut"
    WATERSHED = "watershed"
    REMBG = "rembg"
    THRESHOLD = "threshold"
    EDGE_DETECTION = "edge_detection"


class WeatherEffect(Enum):
    """天气效果类型"""
    SUNNY = "sunny"
    RAINY = "rainy"
    SNOWY = "snowy"
    FOGGY = "foggy"
    CLOUDY = "cloudy"
    NIGHT = "night"


@dataclass
class ImageData:
    """图像数据结构"""
    image: Union[Image.Image, np.ndarray]
    mask: Optional[np.ndarray] = None
    alpha: Optional[np.ndarray] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class BoundingBox:
    """边界框"""
    x: int
    y: int
    width: int
    height: int
    
    @property
    def x2(self) -> int:
        return self.x + self.width
    
    @property
    def y2(self) -> int:
        return self.y + self.height
    
    @property
    def center(self) -> Tuple[int, int]:
        return (self.x + self.width // 2, self.y + self.height // 2)


@dataclass
class MattingConfig:
    """抠图配置"""
    method: MattingMethod = MattingMethod.GRABCUT
    iterations: int = 5
    margin: int = 10
    feather_radius: int = 3
    edge_smooth: bool = True
    auto_refine: bool = True
    confidence_threshold: float = 0.8


@dataclass
class BlendConfig:
    """混合配置"""
    mode: BlendMode = BlendMode.NORMAL
    opacity: float = 1.0
    color_match: bool = True
    lighting_adjust: bool = True
    shadow_generate: bool = True
    shadow_opacity: float = 0.3
    shadow_blur: int = 5


@dataclass
class CompositionConfig:
    """合成配置"""
    target_size: Tuple[int, int] = (512, 512)
    target_position: Optional[Tuple[int, int]] = None
    target_scale: float = 1.0
    rotation_angle: float = 0.0
    flip_horizontal: bool = False
    flip_vertical: bool = False


@dataclass
class ProcessingResult:
    """处理结果"""
    success: bool
    image: Optional[ImageData] = None
    mask: Optional[np.ndarray] = None
    error_message: Optional[str] = None
    processing_time: float = 0.0
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class CompositionRequest:
    """合成请求"""
    background_path: str
    target_path: str
    weather_effect: WeatherEffect
    military_target: str
    scene_type: str
    matting_config: MattingConfig
    blend_config: BlendConfig
    composition_config: CompositionConfig
    output_path: Optional[str] = None
    save_intermediate: bool = False


class ProcessingPipeline:
    """处理管道基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.steps = []
    
    def add_step(self, step_func, **kwargs):
        """添加处理步骤"""
        self.steps.append((step_func, kwargs))
    
    def process(self, data: Any) -> ProcessingResult:
        """执行处理管道"""
        raise NotImplementedError
    
    def validate_input(self, data: Any) -> bool:
        """验证输入数据"""
        raise NotImplementedError


class ImageProcessor:
    """图像处理器基类"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
    
    def process(self, image_data: ImageData) -> ProcessingResult:
        """处理图像"""
        raise NotImplementedError
    
    def validate_config(self) -> bool:
        """验证配置"""
        return True


class MattingProcessor(ImageProcessor):
    """抠图处理器基类"""
    
    def __init__(self, config: MattingConfig):
        super().__init__()
        self.config = config
    
    def extract_foreground(self, image_data: ImageData, bbox: Optional[BoundingBox] = None) -> ProcessingResult:
        """提取前景对象"""
        raise NotImplementedError
    
    def refine_mask(self, mask: np.ndarray, image: np.ndarray) -> np.ndarray:
        """优化蒙版"""
        raise NotImplementedError


class BlendProcessor(ImageProcessor):
    """混合处理器基类"""
    
    def __init__(self, config: BlendConfig):
        super().__init__()
        self.config = config
    
    def blend_layers(self, background: ImageData, foreground: ImageData, mask: np.ndarray) -> ProcessingResult:
        """混合图层"""
        raise NotImplementedError
    
    def match_colors(self, source: np.ndarray, target: np.ndarray, mask: np.ndarray) -> np.ndarray:
        """色彩匹配"""
        raise NotImplementedError
    
    def generate_shadow(self, foreground: np.ndarray, mask: np.ndarray, light_direction: Tuple[float, float]) -> np.ndarray:
        """生成阴影"""
        raise NotImplementedError


class EffectProcessor(ImageProcessor):
    """效果处理器基类"""
    
    def apply_weather_effect(self, image_data: ImageData, weather: WeatherEffect) -> ProcessingResult:
        """应用天气效果"""
        raise NotImplementedError
    
    def adjust_lighting(self, image_data: ImageData, lighting_params: Dict[str, float]) -> ProcessingResult:
        """调整光照"""
        raise NotImplementedError


# 工具函数
def load_image(path: str) -> ImageData:
    """加载图像"""
    try:
        image = Image.open(path).convert('RGB')
        return ImageData(image=image, metadata={'path': path})
    except Exception as e:
        raise ValueError(f"无法加载图像 {path}: {str(e)}")


def save_image(image_data: ImageData, path: str, quality: int = 95) -> bool:
    """保存图像"""
    try:
        if isinstance(image_data.image, np.ndarray):
            image = Image.fromarray(image_data.image)
        else:
            image = image_data.image
        
        image.save(path, quality=quality)
        return True
    except Exception as e:
        print(f"保存图像失败 {path}: {str(e)}")
        return False


def numpy_to_pil(array: np.ndarray) -> Image.Image:
    """NumPy数组转PIL图像"""
    if array.dtype != np.uint8:
        array = (array * 255).astype(np.uint8)
    return Image.fromarray(array)


def pil_to_numpy(image: Image.Image) -> np.ndarray:
    """PIL图像转NumPy数组"""
    return np.array(image)


def create_bbox_from_mask(mask: np.ndarray) -> BoundingBox:
    """从蒙版创建边界框"""
    coords = np.where(mask > 0)
    if len(coords[0]) == 0:
        return BoundingBox(0, 0, 0, 0)
    
    y_min, y_max = coords[0].min(), coords[0].max()
    x_min, x_max = coords[1].min(), coords[1].max()
    
    return BoundingBox(
        x=int(x_min),
        y=int(y_min),
        width=int(x_max - x_min + 1),
        height=int(y_max - y_min + 1)
    )
