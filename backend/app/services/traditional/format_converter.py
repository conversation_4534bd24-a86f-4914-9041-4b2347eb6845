"""
图片格式转换工具
支持多种图片格式之间的转换，包括质量控制和优化
"""
import os
import logging
from typing import Dict, Any, List, Optional, Tuple, Union
from PIL import Image, ImageOps
import numpy as np
from pathlib import Path
import io

logger = logging.getLogger(__name__)


class ImageFormatConverter:
    """图片格式转换器"""
    
    # 支持的格式配置
    FORMAT_CONFIG = {
        'JPEG': {
            'extensions': ['.jpg', '.jpeg'],
            'mode': 'RGB',
            'supports_quality': True,
            'supports_transparency': False,
            'default_quality': 95,
            'description': 'JPEG格式，适合照片，支持有损压缩'
        },
        'PNG': {
            'extensions': ['.png'],
            'mode': 'RGBA',
            'supports_quality': False,
            'supports_transparency': True,
            'default_quality': None,
            'description': 'PNG格式，支持透明度，无损压缩'
        },
        'WEBP': {
            'extensions': ['.webp'],
            'mode': 'RGBA',
            'supports_quality': True,
            'supports_transparency': True,
            'default_quality': 90,
            'description': 'WebP格式，现代格式，支持透明度和高压缩率'
        },
        'GIF': {
            'extensions': ['.gif'],
            'mode': 'P',
            'supports_quality': False,
            'supports_transparency': True,
            'default_quality': None,
            'description': 'GIF格式，支持动画和透明度'
        },
        'BMP': {
            'extensions': ['.bmp'],
            'mode': 'RGB',
            'supports_quality': False,
            'supports_transparency': False,
            'default_quality': None,
            'description': 'BMP格式，无压缩位图'
        },
        'TIFF': {
            'extensions': ['.tiff', '.tif'],
            'mode': 'RGB',
            'supports_quality': True,
            'supports_transparency': True,
            'default_quality': None,
            'description': 'TIFF格式，专业图像格式，支持多页'
        }
    }
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化格式转换器
        
        Args:
            config: 配置参数
        """
        self.config = config or {
            'default_quality': 95,
            'preserve_metadata': True,
            'optimize': True,
            'progressive': True  # JPEG渐进式加载
        }
    
    def get_supported_formats(self) -> Dict[str, Dict[str, Any]]:
        """获取支持的格式列表"""
        return self.FORMAT_CONFIG.copy()
    
    def detect_format(self, file_path: Union[str, Path]) -> Optional[str]:
        """
        检测文件格式
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 格式名称，如果无法检测则返回None
        """
        try:
            path = Path(file_path)
            
            # 首先尝试从扩展名检测
            ext = path.suffix.lower()
            for format_name, config in self.FORMAT_CONFIG.items():
                if ext in config['extensions']:
                    return format_name
            
            # 如果扩展名检测失败，尝试从文件内容检测
            if path.exists():
                try:
                    with Image.open(path) as img:
                        return img.format
                except Exception:
                    pass
            
            return None
            
        except Exception as e:
            logger.error(f"格式检测失败: {str(e)}")
            return None
    
    def convert_format(
        self,
        input_path: Union[str, Path],
        output_path: Union[str, Path],
        target_format: str,
        quality: Optional[int] = None,
        preserve_transparency: bool = True,
        background_color: Tuple[int, int, int] = (255, 255, 255)
    ) -> Dict[str, Any]:
        """
        转换图片格式
        
        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径
            target_format: 目标格式
            quality: 图片质量（如果支持）
            preserve_transparency: 是否保持透明度
            background_color: 背景颜色（当目标格式不支持透明度时使用）
            
        Returns:
            Dict: 转换结果
        """
        try:
            input_path = Path(input_path)
            output_path = Path(output_path)
            
            if not input_path.exists():
                raise FileNotFoundError(f"输入文件不存在: {input_path}")
            
            # 检查目标格式
            target_format = target_format.upper()
            if target_format not in self.FORMAT_CONFIG:
                raise ValueError(f"不支持的目标格式: {target_format}")
            
            format_config = self.FORMAT_CONFIG[target_format]
            
            # 加载图片
            with Image.open(input_path) as img:
                original_format = img.format
                original_mode = img.mode
                original_size = img.size
                
                # 获取元数据
                metadata = {}
                if self.config['preserve_metadata'] and hasattr(img, 'info'):
                    metadata = img.info.copy()
                
                # 处理颜色模式转换
                converted_img = self._convert_color_mode(
                    img, format_config, preserve_transparency, background_color
                )
                
                # 设置保存参数
                save_kwargs = self._get_save_kwargs(
                    target_format, format_config, quality, metadata
                )
                
                # 确保输出目录存在
                output_path.parent.mkdir(parents=True, exist_ok=True)
                
                # 保存图片
                converted_img.save(output_path, format=target_format, **save_kwargs)
                
                # 获取输出文件信息
                output_size = output_path.stat().st_size
                
                return {
                    'success': True,
                    'input_path': str(input_path),
                    'output_path': str(output_path),
                    'original_format': original_format,
                    'target_format': target_format,
                    'original_mode': original_mode,
                    'target_mode': converted_img.mode,
                    'original_size': original_size,
                    'target_size': converted_img.size,
                    'file_size': output_size,
                    'quality': quality,
                    'metadata_preserved': bool(metadata)
                }
                
        except Exception as e:
            logger.error(f"格式转换失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'input_path': str(input_path),
                'output_path': str(output_path),
                'target_format': target_format
            }
    
    def _convert_color_mode(
        self,
        img: Image.Image,
        format_config: Dict[str, Any],
        preserve_transparency: bool,
        background_color: Tuple[int, int, int]
    ) -> Image.Image:
        """
        转换颜色模式
        
        Args:
            img: 原始图片
            format_config: 格式配置
            preserve_transparency: 是否保持透明度
            background_color: 背景颜色
            
        Returns:
            Image.Image: 转换后的图片
        """
        target_mode = format_config['mode']
        supports_transparency = format_config['supports_transparency']
        
        # 如果模式已经匹配，直接返回
        if img.mode == target_mode:
            return img.copy()
        
        # 处理透明度
        if img.mode in ['RGBA', 'LA'] and not supports_transparency:
            # 目标格式不支持透明度，需要合成背景
            if img.mode == 'RGBA':
                background = Image.new('RGB', img.size, background_color)
                background.paste(img, mask=img.split()[-1])
                img = background
            elif img.mode == 'LA':
                background = Image.new('L', img.size, background_color[0])
                background.paste(img, mask=img.split()[-1])
                img = background
        
        # 转换到目标模式
        if img.mode != target_mode:
            if target_mode == 'P':
                # 转换为调色板模式
                img = img.convert('RGB').quantize()
            else:
                img = img.convert(target_mode)
        
        return img
    
    def _get_save_kwargs(
        self,
        target_format: str,
        format_config: Dict[str, Any],
        quality: Optional[int],
        metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        获取保存参数
        
        Args:
            target_format: 目标格式
            format_config: 格式配置
            quality: 质量设置
            metadata: 元数据
            
        Returns:
            Dict: 保存参数
        """
        save_kwargs = {}
        
        # 质量设置
        if format_config['supports_quality']:
            if quality is None:
                quality = format_config['default_quality'] or self.config['default_quality']
            save_kwargs['quality'] = quality
        
        # 优化设置
        if self.config['optimize'] and target_format in ['JPEG', 'PNG', 'WEBP']:
            save_kwargs['optimize'] = True
        
        # 渐进式JPEG
        if target_format == 'JPEG' and self.config['progressive']:
            save_kwargs['progressive'] = True
        
        # 元数据
        if metadata and self.config['preserve_metadata']:
            # 过滤掉可能导致问题的元数据
            filtered_metadata = {k: v for k, v in metadata.items() 
                               if k not in ['transparency', 'gamma']}
            if filtered_metadata:
                save_kwargs.update(filtered_metadata)
        
        return save_kwargs
    
    def batch_convert(
        self,
        input_dir: Union[str, Path],
        output_dir: Union[str, Path],
        target_format: str,
        pattern: str = "*",
        quality: Optional[int] = None,
        preserve_transparency: bool = True
    ) -> Dict[str, Any]:
        """
        批量转换格式
        
        Args:
            input_dir: 输入目录
            output_dir: 输出目录
            target_format: 目标格式
            pattern: 文件匹配模式
            quality: 图片质量
            preserve_transparency: 是否保持透明度
            
        Returns:
            Dict: 批量转换结果
        """
        try:
            input_dir = Path(input_dir)
            output_dir = Path(output_dir)
            
            if not input_dir.exists():
                raise FileNotFoundError(f"输入目录不存在: {input_dir}")
            
            # 创建输出目录
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 查找匹配的文件
            files = list(input_dir.glob(pattern))
            image_files = []
            
            for file in files:
                if self.detect_format(file):
                    image_files.append(file)
            
            if not image_files:
                return {
                    'success': False,
                    'error': f"在目录 {input_dir} 中未找到图片文件",
                    'processed': 0,
                    'failed': 0
                }
            
            # 批量转换
            results = []
            success_count = 0
            failed_count = 0
            
            for input_file in image_files:
                # 生成输出文件名
                output_file = output_dir / f"{input_file.stem}.{target_format.lower()}"
                
                # 转换单个文件
                result = self.convert_format(
                    input_file, output_file, target_format,
                    quality=quality, preserve_transparency=preserve_transparency
                )
                
                results.append(result)
                
                if result['success']:
                    success_count += 1
                else:
                    failed_count += 1
            
            return {
                'success': True,
                'input_dir': str(input_dir),
                'output_dir': str(output_dir),
                'target_format': target_format,
                'total_files': len(image_files),
                'processed': success_count,
                'failed': failed_count,
                'results': results
            }
            
        except Exception as e:
            logger.error(f"批量转换失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'processed': 0,
                'failed': 0
            }
