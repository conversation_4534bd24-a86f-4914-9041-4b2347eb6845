"""
抠图处理器
实现基于深度学习和传统算法的军事目标抠图功能
"""
import os
import time
import hashlib
import logging
from typing import Optional, Tuple, Dict, Any
from pathlib import Path
from PIL import Image
import numpy as np
import cv2

logger = logging.getLogger(__name__)


class MattingProcessor:
    """抠图处理器"""
    
    def __init__(self, cache_dir: str = None):
        """
        初始化抠图处理器
        
        Args:
            cache_dir: 缓存目录路径
        """
        # 设置缓存目录
        if cache_dir is None:
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
            cache_dir = os.path.join(project_root, "data", "matting_cache")
        
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 抠图方法配置
        self.methods = {
            "rembg": self._rembg_matting,
            "grabcut": self._grabcut_matting
        }
        
        # 性能统计
        self.stats = {
            "total_processed": 0,
            "cache_hits": 0,
            "rembg_success": 0,
            "grabcut_success": 0,
            "failures": 0
        }
        
        # 检查rembg可用性
        self.rembg_available = self._check_rembg_availability()
        
        logger.info(f"抠图处理器初始化完成，缓存目录: {self.cache_dir}")
        logger.info(f"rembg可用性: {self.rembg_available}")
    
    def _check_rembg_availability(self) -> bool:
        """检查rembg库是否可用"""
        try:
            import rembg
            return True
        except ImportError:
            logger.warning("rembg库不可用，将使用GrabCut作为备选方案")
            return False
    
    def process_image(
        self, 
        image_path: str, 
        method: str = "auto",
        force_refresh: bool = False
    ) -> Optional[Image.Image]:
        """
        处理图片进行抠图
        
        Args:
            image_path: 图片路径
            method: 抠图方法 ("auto", "rembg", "grabcut")
            force_refresh: 是否强制刷新缓存
            
        Returns:
            Optional[Image.Image]: 抠图后的RGBA图片，失败返回None
        """
        start_time = time.time()
        
        try:
            # 检查缓存
            if not force_refresh:
                cached_result = self._get_cached_result(image_path)
                if cached_result:
                    self.stats["cache_hits"] += 1
                    logger.debug(f"使用缓存结果: {image_path}")
                    return cached_result
            
            # 加载原始图片
            original_img = Image.open(image_path).convert('RGB')
            
            # 选择抠图方法
            if method == "auto":
                method = "rembg" if self.rembg_available else "grabcut"
            
            # 执行抠图
            result_img = None
            if method == "rembg" and self.rembg_available:
                result_img = self._rembg_matting(original_img)
                if result_img:
                    self.stats["rembg_success"] += 1
                else:
                    logger.warning("rembg抠图失败，尝试GrabCut")
                    result_img = self._grabcut_matting(original_img)
                    if result_img:
                        self.stats["grabcut_success"] += 1
            elif method == "grabcut":
                result_img = self._grabcut_matting(original_img)
                if result_img:
                    self.stats["grabcut_success"] += 1
            else:
                logger.error(f"不支持的抠图方法: {method}")
                return None
            
            if result_img:
                # 保存到缓存
                self._save_to_cache(image_path, result_img)
                self.stats["total_processed"] += 1
                
                processing_time = time.time() - start_time
                logger.info(f"抠图完成: {os.path.basename(image_path)} ({processing_time:.2f}秒)")
                
                return result_img
            else:
                self.stats["failures"] += 1
                logger.error(f"抠图失败: {image_path}")
                return None
                
        except Exception as e:
            self.stats["failures"] += 1
            logger.error(f"抠图处理异常 {image_path}: {str(e)}")
            return None
    
    def _rembg_matting(self, image: Image.Image) -> Optional[Image.Image]:
        """
        使用rembg进行抠图
        
        Args:
            image: 输入图片
            
        Returns:
            Optional[Image.Image]: 抠图结果
        """
        try:
            import rembg
            
            # 转换为字节数据
            import io
            img_byte_arr = io.BytesIO()
            image.save(img_byte_arr, format='PNG')
            img_byte_arr = img_byte_arr.getvalue()
            
            # 执行抠图
            output_data = rembg.remove(img_byte_arr)
            
            # 转换回PIL图片
            result_img = Image.open(io.BytesIO(output_data)).convert('RGBA')
            
            # 验证抠图质量
            if self._validate_matting_result(result_img):
                return result_img
            else:
                logger.warning("rembg抠图质量验证失败")
                return None
                
        except Exception as e:
            logger.error(f"rembg抠图失败: {str(e)}")
            return None
    
    def _grabcut_matting(self, image: Image.Image) -> Optional[Image.Image]:
        """
        使用GrabCut算法进行抠图
        
        Args:
            image: 输入图片
            
        Returns:
            Optional[Image.Image]: 抠图结果
        """
        try:
            # 转换为OpenCV格式
            img_array = np.array(image)
            img_cv = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
            
            height, width = img_cv.shape[:2]
            
            # 创建掩码
            mask = np.zeros((height, width), np.uint8)
            
            # 定义前景区域（中心80%区域）
            margin_x = int(width * 0.1)
            margin_y = int(height * 0.1)
            rect = (margin_x, margin_y, width - 2*margin_x, height - 2*margin_y)
            
            # 初始化GrabCut
            bgd_model = np.zeros((1, 65), np.float64)
            fgd_model = np.zeros((1, 65), np.float64)
            
            # 执行GrabCut
            cv2.grabCut(img_cv, mask, rect, bgd_model, fgd_model, 5, cv2.GC_INIT_WITH_RECT)
            
            # 创建最终掩码
            mask2 = np.where((mask == 2) | (mask == 0), 0, 1).astype('uint8')
            
            # 应用掩码
            result_array = img_array * mask2[:, :, np.newaxis]
            
            # 创建alpha通道
            alpha = mask2 * 255
            
            # 合并RGBA
            result_rgba = np.dstack((result_array, alpha))
            
            # 转换为PIL图片
            result_img = Image.fromarray(result_rgba, 'RGBA')
            
            # 验证抠图质量
            if self._validate_matting_result(result_img):
                return result_img
            else:
                logger.warning("GrabCut抠图质量验证失败")
                return None
                
        except Exception as e:
            logger.error(f"GrabCut抠图失败: {str(e)}")
            return None
    
    def _validate_matting_result(self, image: Image.Image) -> bool:
        """
        验证抠图结果质量
        
        Args:
            image: 抠图结果
            
        Returns:
            bool: 是否通过验证
        """
        try:
            # 检查是否有alpha通道
            if image.mode != 'RGBA':
                return False
            
            # 获取alpha通道
            alpha = np.array(image.split()[-1])
            
            # 检查透明像素比例
            transparent_pixels = np.sum(alpha == 0)
            total_pixels = alpha.size
            transparent_ratio = transparent_pixels / total_pixels
            
            # 透明像素应该在10%-90%之间（避免全透明或全不透明）
            if 0.1 <= transparent_ratio <= 0.9:
                return True
            else:
                logger.debug(f"抠图质量验证失败，透明像素比例: {transparent_ratio:.2f}")
                return False
                
        except Exception as e:
            logger.error(f"抠图质量验证异常: {str(e)}")
            return False
    
    def _get_cache_key(self, image_path: str) -> str:
        """
        生成缓存键
        
        Args:
            image_path: 图片路径
            
        Returns:
            str: 缓存键
        """
        # 使用文件路径和修改时间生成唯一键
        stat = os.stat(image_path)
        content = f"{image_path}_{stat.st_mtime}_{stat.st_size}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def _get_cached_result(self, image_path: str) -> Optional[Image.Image]:
        """
        获取缓存结果
        
        Args:
            image_path: 图片路径
            
        Returns:
            Optional[Image.Image]: 缓存的抠图结果
        """
        try:
            cache_key = self._get_cache_key(image_path)
            cache_file = self.cache_dir / f"{cache_key}.png"
            
            if cache_file.exists():
                return Image.open(cache_file).convert('RGBA')
            else:
                return None
                
        except Exception as e:
            logger.error(f"读取缓存失败: {str(e)}")
            return None
    
    def _save_to_cache(self, image_path: str, result_img: Image.Image):
        """
        保存结果到缓存
        
        Args:
            image_path: 原始图片路径
            result_img: 抠图结果
        """
        try:
            cache_key = self._get_cache_key(image_path)
            cache_file = self.cache_dir / f"{cache_key}.png"
            
            result_img.save(cache_file, 'PNG')
            logger.debug(f"抠图结果已缓存: {cache_file}")
            
        except Exception as e:
            logger.error(f"保存缓存失败: {str(e)}")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取处理统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            **self.stats,
            "cache_hit_rate": self.stats["cache_hits"] / max(1, self.stats["total_processed"] + self.stats["cache_hits"]),
            "rembg_available": self.rembg_available
        }
    
    def clear_cache(self):
        """清空缓存"""
        try:
            for cache_file in self.cache_dir.glob("*.png"):
                cache_file.unlink()
            logger.info("抠图缓存已清空")
        except Exception as e:
            logger.error(f"清空缓存失败: {str(e)}")


# 全局实例
_matting_processor = None


def get_matting_processor() -> MattingProcessor:
    """获取抠图处理器实例"""
    global _matting_processor
    if _matting_processor is None:
        _matting_processor = MattingProcessor()
    return _matting_processor
