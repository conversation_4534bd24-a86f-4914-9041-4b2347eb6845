"""
增强的图片处理器
提供高级图片处理功能，包括格式转换、滤镜效果、色彩调整等
"""
import os
import logging
from typing import Dict, Any, List, Optional, Tuple, Union
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance, ImageOps
import numpy as np
import cv2
from pathlib import Path
import io

from .core_types import ImageData, ProcessingResult

logger = logging.getLogger(__name__)


class EnhancedImageProcessor:
    """增强的图片处理器"""
    
    # 支持的图片格式
    SUPPORTED_FORMATS = {
        'PNG': {'ext': ['.png'], 'mode': 'RGBA', 'quality': None},
        'JPEG': {'ext': ['.jpg', '.jpeg'], 'mode': 'RGB', 'quality': 95},
        'GIF': {'ext': ['.gif'], 'mode': 'P', 'quality': None},
        'BMP': {'ext': ['.bmp'], 'mode': 'RGB', 'quality': None},
        'TIFF': {'ext': ['.tiff', '.tif'], 'mode': 'RGB', 'quality': None},
        'WEBP': {'ext': ['.webp'], 'mode': 'RGB', 'quality': 90},
    }
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化图片处理器
        
        Args:
            config: 配置参数
        """
        self.config = config or {
            'default_quality': 95,
            'max_size': (4096, 4096),
            'memory_limit': 512 * 1024 * 1024,  # 512MB
            'temp_dir': 'data/temp'
        }
        
        # 确保临时目录存在
        os.makedirs(self.config['temp_dir'], exist_ok=True)
    
    def load_image(self, path: Union[str, Path], target_format: str = None) -> ImageData:
        """
        加载图片，支持多种格式
        
        Args:
            path: 图片路径
            target_format: 目标格式（可选）
            
        Returns:
            ImageData: 图片数据
        """
        try:
            path = Path(path)
            if not path.exists():
                raise FileNotFoundError(f"图片文件不存在: {path}")
            
            # 检查文件大小
            file_size = path.stat().st_size
            if file_size > self.config['memory_limit']:
                raise ValueError(f"图片文件过大: {file_size / 1024 / 1024:.1f}MB")
            
            # 加载图片
            with Image.open(path) as img:
                # 获取原始信息
                original_format = img.format
                original_mode = img.mode
                original_size = img.size
                
                # 转换为RGB模式（如果需要）
                if target_format:
                    target_mode = self.SUPPORTED_FORMATS.get(target_format, {}).get('mode', 'RGB')
                    if img.mode != target_mode:
                        if target_mode == 'RGBA' and img.mode == 'RGB':
                            img = img.convert('RGBA')
                        elif target_mode == 'RGB' and img.mode in ['RGBA', 'P']:
                            # 处理透明背景
                            if img.mode == 'RGBA':
                                background = Image.new('RGB', img.size, (255, 255, 255))
                                background.paste(img, mask=img.split()[-1])
                                img = background
                            else:
                                img = img.convert('RGB')
                        else:
                            img = img.convert(target_mode)
                
                # 复制图片数据
                img_copy = img.copy()
                
                metadata = {
                    'path': str(path),
                    'original_format': original_format,
                    'original_mode': original_mode,
                    'original_size': original_size,
                    'file_size': file_size
                }
                
                return ImageData(image=img_copy, metadata=metadata)
                
        except Exception as e:
            logger.error(f"加载图片失败 {path}: {str(e)}")
            raise
    
    def save_image(
        self, 
        image_data: ImageData, 
        path: Union[str, Path], 
        format: str = None,
        quality: int = None,
        optimize: bool = True
    ) -> bool:
        """
        保存图片，支持多种格式和质量设置
        
        Args:
            image_data: 图片数据
            path: 保存路径
            format: 图片格式
            quality: 图片质量
            optimize: 是否优化
            
        Returns:
            bool: 是否成功
        """
        try:
            path = Path(path)
            
            # 自动检测格式
            if not format:
                format = self._detect_format_from_path(path)
            
            # 获取格式配置
            format_config = self.SUPPORTED_FORMATS.get(format.upper(), {})
            if not format_config:
                raise ValueError(f"不支持的图片格式: {format}")
            
            # 设置质量参数
            if quality is None:
                quality = format_config.get('quality', self.config['default_quality'])
            
            # 转换图片模式
            image = image_data.image
            target_mode = format_config.get('mode', 'RGB')
            if image.mode != target_mode:
                if target_mode == 'RGB' and image.mode == 'RGBA':
                    # 处理透明背景
                    background = Image.new('RGB', image.size, (255, 255, 255))
                    background.paste(image, mask=image.split()[-1])
                    image = background
                else:
                    image = image.convert(target_mode)
            
            # 保存参数
            save_kwargs = {'optimize': optimize}
            if quality is not None and format.upper() in ['JPEG', 'WEBP']:
                save_kwargs['quality'] = quality
            
            # 确保目录存在
            path.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存图片
            image.save(path, format=format.upper(), **save_kwargs)
            
            logger.info(f"图片保存成功: {path}")
            return True
            
        except Exception as e:
            logger.error(f"保存图片失败 {path}: {str(e)}")
            return False
    
    def resize_image(
        self, 
        image_data: ImageData, 
        size: Tuple[int, int], 
        method: str = 'lanczos',
        maintain_aspect: bool = True
    ) -> ProcessingResult:
        """
        调整图片尺寸
        
        Args:
            image_data: 图片数据
            size: 目标尺寸 (width, height)
            method: 缩放方法
            maintain_aspect: 是否保持宽高比
            
        Returns:
            ProcessingResult: 处理结果
        """
        try:
            image = image_data.image
            original_size = image.size
            
            # 计算目标尺寸
            if maintain_aspect:
                # 计算保持宽高比的尺寸
                ratio = min(size[0] / original_size[0], size[1] / original_size[1])
                new_size = (int(original_size[0] * ratio), int(original_size[1] * ratio))
            else:
                new_size = size
            
            # 选择缩放方法
            resample_methods = {
                'nearest': Image.NEAREST,
                'bilinear': Image.BILINEAR,
                'bicubic': Image.BICUBIC,
                'lanczos': Image.LANCZOS,
                'hamming': Image.HAMMING,
                'box': Image.BOX
            }
            
            resample = resample_methods.get(method.lower(), Image.LANCZOS)
            
            # 执行缩放
            resized_image = image.resize(new_size, resample)
            
            # 如果需要填充到目标尺寸
            if maintain_aspect and new_size != size:
                # 创建目标尺寸的画布
                canvas = Image.new(image.mode, size, (255, 255, 255))
                # 计算居中位置
                x = (size[0] - new_size[0]) // 2
                y = (size[1] - new_size[1]) // 2
                canvas.paste(resized_image, (x, y))
                resized_image = canvas
            
            result_data = ImageData(
                image=resized_image,
                metadata={
                    **image_data.metadata,
                    'resize_method': method,
                    'original_size': original_size,
                    'new_size': resized_image.size,
                    'maintain_aspect': maintain_aspect
                }
            )
            
            return ProcessingResult(
                success=True,
                image=result_data,
                processing_time=0.0,
                metadata={'operation': 'resize', 'size': size}
            )
            
        except Exception as e:
            logger.error(f"图片缩放失败: {str(e)}")
            return ProcessingResult(
                success=False,
                error_message=str(e),
                processing_time=0.0
            )
    
    def apply_filter(self, image_data: ImageData, filter_type: str, **kwargs) -> ProcessingResult:
        """
        应用滤镜效果
        
        Args:
            image_data: 图片数据
            filter_type: 滤镜类型
            **kwargs: 滤镜参数
            
        Returns:
            ProcessingResult: 处理结果
        """
        try:
            image = image_data.image
            
            # 滤镜映射
            filters = {
                'blur': lambda img, radius=2: img.filter(ImageFilter.GaussianBlur(radius=radius)),
                'sharpen': lambda img: img.filter(ImageFilter.SHARPEN),
                'smooth': lambda img: img.filter(ImageFilter.SMOOTH),
                'edge_enhance': lambda img: img.filter(ImageFilter.EDGE_ENHANCE),
                'emboss': lambda img: img.filter(ImageFilter.EMBOSS),
                'contour': lambda img: img.filter(ImageFilter.CONTOUR),
                'detail': lambda img: img.filter(ImageFilter.DETAIL),
                'find_edges': lambda img: img.filter(ImageFilter.FIND_EDGES),
            }
            
            if filter_type not in filters:
                raise ValueError(f"不支持的滤镜类型: {filter_type}")
            
            # 应用滤镜
            filtered_image = filters[filter_type](image, **kwargs)
            
            result_data = ImageData(
                image=filtered_image,
                metadata={
                    **image_data.metadata,
                    'filter_type': filter_type,
                    'filter_params': kwargs
                }
            )
            
            return ProcessingResult(
                success=True,
                image=result_data,
                processing_time=0.0,
                metadata={'operation': 'filter', 'type': filter_type}
            )
            
        except Exception as e:
            logger.error(f"应用滤镜失败: {str(e)}")
            return ProcessingResult(
                success=False,
                error_message=str(e),
                processing_time=0.0
            )
    
    def adjust_color(
        self,
        image_data: ImageData,
        brightness: float = 1.0,
        contrast: float = 1.0,
        saturation: float = 1.0,
        hue_shift: float = 0.0
    ) -> ProcessingResult:
        """
        调整图片色彩

        Args:
            image_data: 图片数据
            brightness: 亮度调整 (0.0-2.0, 1.0为原始)
            contrast: 对比度调整 (0.0-2.0, 1.0为原始)
            saturation: 饱和度调整 (0.0-2.0, 1.0为原始)
            hue_shift: 色相偏移 (-180到180度)

        Returns:
            ProcessingResult: 处理结果
        """
        try:
            image = image_data.image

            # 亮度调整
            if brightness != 1.0:
                enhancer = ImageEnhance.Brightness(image)
                image = enhancer.enhance(brightness)

            # 对比度调整
            if contrast != 1.0:
                enhancer = ImageEnhance.Contrast(image)
                image = enhancer.enhance(contrast)

            # 饱和度调整
            if saturation != 1.0:
                enhancer = ImageEnhance.Color(image)
                image = enhancer.enhance(saturation)

            # 色相偏移（需要转换到HSV空间）
            if hue_shift != 0.0:
                image_array = np.array(image)
                if image.mode == 'RGB':
                    hsv = cv2.cvtColor(image_array, cv2.COLOR_RGB2HSV)
                    hsv[:, :, 0] = (hsv[:, :, 0] + hue_shift) % 180
                    image_array = cv2.cvtColor(hsv, cv2.COLOR_HSV2RGB)
                    image = Image.fromarray(image_array)

            result_data = ImageData(
                image=image,
                metadata={
                    **image_data.metadata,
                    'color_adjustments': {
                        'brightness': brightness,
                        'contrast': contrast,
                        'saturation': saturation,
                        'hue_shift': hue_shift
                    }
                }
            )

            return ProcessingResult(
                success=True,
                image=result_data,
                processing_time=0.0,
                metadata={'operation': 'color_adjust'}
            )

        except Exception as e:
            logger.error(f"色彩调整失败: {str(e)}")
            return ProcessingResult(
                success=False,
                error_message=str(e),
                processing_time=0.0
            )

    def crop_image(
        self,
        image_data: ImageData,
        box: Tuple[int, int, int, int],
        center_crop: bool = False
    ) -> ProcessingResult:
        """
        裁剪图片

        Args:
            image_data: 图片数据
            box: 裁剪区域 (left, top, right, bottom) 或 (width, height) 如果center_crop=True
            center_crop: 是否居中裁剪

        Returns:
            ProcessingResult: 处理结果
        """
        try:
            image = image_data.image

            if center_crop:
                # 居中裁剪
                width, height = box[:2]  # 只使用前两个值作为目标尺寸
                img_width, img_height = image.size

                left = (img_width - width) // 2
                top = (img_height - height) // 2
                right = left + width
                bottom = top + height

                # 确保裁剪区域在图片范围内
                left = max(0, left)
                top = max(0, top)
                right = min(img_width, right)
                bottom = min(img_height, bottom)

                crop_box = (left, top, right, bottom)
            else:
                crop_box = box

            # 执行裁剪
            cropped_image = image.crop(crop_box)

            result_data = ImageData(
                image=cropped_image,
                metadata={
                    **image_data.metadata,
                    'crop_box': crop_box,
                    'center_crop': center_crop,
                    'original_size': image.size,
                    'cropped_size': cropped_image.size
                }
            )

            return ProcessingResult(
                success=True,
                image=result_data,
                processing_time=0.0,
                metadata={'operation': 'crop', 'box': crop_box}
            )

        except Exception as e:
            logger.error(f"图片裁剪失败: {str(e)}")
            return ProcessingResult(
                success=False,
                error_message=str(e),
                processing_time=0.0
            )

    def rotate_image(
        self,
        image_data: ImageData,
        angle: float,
        expand: bool = True,
        fill_color: Tuple[int, int, int] = (255, 255, 255)
    ) -> ProcessingResult:
        """
        旋转图片

        Args:
            image_data: 图片数据
            angle: 旋转角度（度）
            expand: 是否扩展画布以容纳旋转后的图片
            fill_color: 填充颜色

        Returns:
            ProcessingResult: 处理结果
        """
        try:
            image = image_data.image

            # 执行旋转
            rotated_image = image.rotate(
                angle,
                expand=expand,
                fillcolor=fill_color,
                resample=Image.BICUBIC
            )

            result_data = ImageData(
                image=rotated_image,
                metadata={
                    **image_data.metadata,
                    'rotation_angle': angle,
                    'expand': expand,
                    'fill_color': fill_color,
                    'original_size': image.size,
                    'rotated_size': rotated_image.size
                }
            )

            return ProcessingResult(
                success=True,
                image=result_data,
                processing_time=0.0,
                metadata={'operation': 'rotate', 'angle': angle}
            )

        except Exception as e:
            logger.error(f"图片旋转失败: {str(e)}")
            return ProcessingResult(
                success=False,
                error_message=str(e),
                processing_time=0.0
            )

    def flip_image(self, image_data: ImageData, direction: str) -> ProcessingResult:
        """
        翻转图片

        Args:
            image_data: 图片数据
            direction: 翻转方向 ('horizontal', 'vertical')

        Returns:
            ProcessingResult: 处理结果
        """
        try:
            image = image_data.image

            if direction.lower() == 'horizontal':
                flipped_image = image.transpose(Image.FLIP_LEFT_RIGHT)
            elif direction.lower() == 'vertical':
                flipped_image = image.transpose(Image.FLIP_TOP_BOTTOM)
            else:
                raise ValueError(f"不支持的翻转方向: {direction}")

            result_data = ImageData(
                image=flipped_image,
                metadata={
                    **image_data.metadata,
                    'flip_direction': direction
                }
            )

            return ProcessingResult(
                success=True,
                image=result_data,
                processing_time=0.0,
                metadata={'operation': 'flip', 'direction': direction}
            )

        except Exception as e:
            logger.error(f"图片翻转失败: {str(e)}")
            return ProcessingResult(
                success=False,
                error_message=str(e),
                processing_time=0.0
            )

    def _detect_format_from_path(self, path: Path) -> str:
        """从文件路径检测格式"""
        ext = path.suffix.lower()
        for format_name, config in self.SUPPORTED_FORMATS.items():
            if ext in config['ext']:
                return format_name
        return 'JPEG'  # 默认格式
