"""
增强的图片合成器
提供高级图片合成功能，包括多种混合模式、透明度处理、阴影生成等
"""
import logging
from typing import Dict, Any, List, Optional, Tuple, Union
from PIL import Image, ImageDraw, ImageFilter
import numpy as np
import cv2
from scipy import ndimage
from skimage import morphology, filters
import time

from .core_types import ImageData, ProcessingResult, BlendMode

logger = logging.getLogger(__name__)


class EnhancedCompositor:
    """增强的图片合成器"""
    
    # 支持的混合模式
    BLEND_MODES = {
        'normal': 'Normal',
        'multiply': 'Multiply',
        'screen': 'Screen',
        'overlay': 'Overlay',
        'soft_light': 'Soft Light',
        'hard_light': 'Hard Light',
        'color_dodge': 'Color Dodge',
        'color_burn': 'Color Burn',
        'darken': 'Darken',
        'lighten': 'Lighten',
        'difference': 'Difference',
        'exclusion': 'Exclusion',
        'hue': 'Hue',
        'saturation': 'Saturation',
        'color': 'Color',
        'luminosity': 'Luminosity'
    }
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化合成器
        
        Args:
            config: 配置参数
        """
        self.config = config or {
            'default_opacity': 1.0,
            'shadow_blur': 5,
            'shadow_opacity': 0.3,
            'shadow_offset': (5, 5),
            'edge_feather': 2
        }
    
    def composite_images(
        self,
        background: ImageData,
        foreground: ImageData,
        mask: Optional[np.ndarray] = None,
        blend_mode: str = 'normal',
        opacity: float = 1.0,
        position: Tuple[int, int] = (0, 0),
        enable_shadow: bool = False,
        shadow_config: Dict[str, Any] = None
    ) -> ProcessingResult:
        """
        合成图片
        
        Args:
            background: 背景图片
            foreground: 前景图片
            mask: 蒙版（可选）
            blend_mode: 混合模式
            opacity: 透明度
            position: 前景图片位置
            enable_shadow: 是否启用阴影
            shadow_config: 阴影配置
            
        Returns:
            ProcessingResult: 合成结果
        """
        start_time = time.time()
        
        try:
            # 转换为numpy数组
            bg_array = np.array(background.image)
            fg_array = np.array(foreground.image)
            
            # 确保都是RGB格式
            if len(bg_array.shape) == 2:
                bg_array = cv2.cvtColor(bg_array, cv2.COLOR_GRAY2RGB)
            if len(fg_array.shape) == 2:
                fg_array = cv2.cvtColor(fg_array, cv2.COLOR_GRAY2RGB)
            
            # 如果没有提供蒙版，创建一个全白蒙版
            if mask is None:
                mask = np.ones(fg_array.shape[:2], dtype=np.uint8) * 255
            
            # 调整前景图片位置和大小
            positioned_fg, positioned_mask = self._position_foreground(
                bg_array, fg_array, mask, position
            )
            
            # 生成阴影（如果启用）
            shadow_layer = None
            if enable_shadow:
                shadow_layer = self._generate_shadow(
                    positioned_fg, positioned_mask, shadow_config or {}
                )
            
            # 执行图层混合
            result_array = self._blend_layers(
                bg_array, positioned_fg, positioned_mask, blend_mode, opacity
            )
            
            # 添加阴影
            if shadow_layer is not None:
                result_array = self._add_shadow_layer(result_array, shadow_layer)
            
            # 转换回PIL图像
            result_image = Image.fromarray(result_array.astype(np.uint8))
            
            processing_time = time.time() - start_time
            
            result_data = ImageData(
                image=result_image,
                metadata={
                    **background.metadata,
                    'composite_info': {
                        'blend_mode': blend_mode,
                        'opacity': opacity,
                        'position': position,
                        'shadow_enabled': enable_shadow,
                        'processing_time': processing_time
                    }
                }
            )
            
            return ProcessingResult(
                success=True,
                image=result_data,
                processing_time=processing_time,
                metadata={'operation': 'composite', 'blend_mode': blend_mode}
            )
            
        except Exception as e:
            logger.error(f"图片合成失败: {str(e)}")
            return ProcessingResult(
                success=False,
                error_message=str(e),
                processing_time=time.time() - start_time
            )
    
    def _position_foreground(
        self,
        background: np.ndarray,
        foreground: np.ndarray,
        mask: np.ndarray,
        position: Tuple[int, int]
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        调整前景图片位置
        
        Args:
            background: 背景图片数组
            foreground: 前景图片数组
            mask: 蒙版数组
            position: 位置 (x, y)
            
        Returns:
            Tuple: (定位后的前景图片, 定位后的蒙版)
        """
        bg_h, bg_w = background.shape[:2]
        fg_h, fg_w = foreground.shape[:2]
        
        # 创建与背景同尺寸的画布
        positioned_fg = np.zeros_like(background)
        positioned_mask = np.zeros((bg_h, bg_w), dtype=np.uint8)
        
        # 计算实际放置区域
        x, y = position
        
        # 确保不超出边界
        x_start = max(0, x)
        y_start = max(0, y)
        x_end = min(bg_w, x + fg_w)
        y_end = min(bg_h, y + fg_h)
        
        # 计算前景图片的对应区域
        fg_x_start = max(0, -x)
        fg_y_start = max(0, -y)
        fg_x_end = fg_x_start + (x_end - x_start)
        fg_y_end = fg_y_start + (y_end - y_start)
        
        # 放置前景图片和蒙版
        if x_end > x_start and y_end > y_start:
            positioned_fg[y_start:y_end, x_start:x_end] = foreground[fg_y_start:fg_y_end, fg_x_start:fg_x_end]
            positioned_mask[y_start:y_end, x_start:x_end] = mask[fg_y_start:fg_y_end, fg_x_start:fg_x_end]
        
        return positioned_fg, positioned_mask
    
    def _blend_layers(
        self,
        background: np.ndarray,
        foreground: np.ndarray,
        mask: np.ndarray,
        blend_mode: str,
        opacity: float
    ) -> np.ndarray:
        """
        混合图层
        
        Args:
            background: 背景图层
            foreground: 前景图层
            mask: 蒙版
            blend_mode: 混合模式
            opacity: 透明度
            
        Returns:
            np.ndarray: 混合结果
        """
        # 归一化到0-1范围
        bg = background.astype(np.float32) / 255.0
        fg = foreground.astype(np.float32) / 255.0
        mask_norm = mask.astype(np.float32) / 255.0
        
        # 扩展蒙版到3通道
        mask_3d = np.stack([mask_norm] * 3, axis=2)
        
        # 应用混合模式 - 优化背景保持
        if blend_mode == 'normal':
            # 改进的normal模式，保持背景可见性
            # 使用更柔和的混合，避免完全覆盖背景
            alpha = 0.8  # 降低前景不透明度，保持背景可见
            blended = bg * (1 - alpha) + fg * alpha
        elif blend_mode == 'multiply':
            blended = bg * fg
        elif blend_mode == 'screen':
            blended = 1 - (1 - bg) * (1 - fg)
        elif blend_mode == 'overlay':
            blended = np.where(bg < 0.5, 2 * bg * fg, 1 - 2 * (1 - bg) * (1 - fg))
        elif blend_mode == 'soft_light':
            blended = np.where(fg < 0.5,
                             bg - (1 - 2 * fg) * bg * (1 - bg),
                             bg + (2 * fg - 1) * (np.sqrt(bg) - bg))
        elif blend_mode == 'hard_light':
            blended = np.where(fg < 0.5, 2 * bg * fg, 1 - 2 * (1 - bg) * (1 - fg))
        elif blend_mode == 'color_dodge':
            blended = np.where(fg == 1, 1, np.minimum(1, bg / (1 - fg + 1e-10)))
        elif blend_mode == 'color_burn':
            blended = np.where(fg == 0, 0, 1 - np.minimum(1, (1 - bg) / (fg + 1e-10)))
        elif blend_mode == 'darken':
            blended = np.minimum(bg, fg)
        elif blend_mode == 'lighten':
            blended = np.maximum(bg, fg)
        elif blend_mode == 'difference':
            blended = np.abs(bg - fg)
        elif blend_mode == 'exclusion':
            blended = bg + fg - 2 * bg * fg
        elif blend_mode == 'enhanced_normal':
            # 新增的增强normal模式，更好地保持背景
            # 使用渐变混合，边缘更自然
            edge_softness = 0.1
            soft_mask = np.clip(mask_norm + edge_softness, 0, 1)
            soft_mask_3d = np.stack([soft_mask] * 3, axis=2)
            blended = bg * (1 - 0.7 * soft_mask_3d) + fg * (0.7 * soft_mask_3d)
        else:
            # 默认使用改进的normal模式
            alpha = 0.8
            blended = bg * (1 - alpha) + fg * alpha

        # 应用透明度 - 调整混合强度以保持背景
        # 降低opacity的影响，确保背景始终可见
        effective_opacity = opacity * 0.85  # 最大不透明度限制为85%
        final_blend = bg * (1 - effective_opacity * mask_3d) + blended * effective_opacity * mask_3d
        
        # 转换回0-255范围
        result = np.clip(final_blend * 255, 0, 255)
        
        return result
    
    def _generate_shadow(
        self,
        foreground: np.ndarray,
        mask: np.ndarray,
        shadow_config: Dict[str, Any]
    ) -> np.ndarray:
        """
        生成阴影
        
        Args:
            foreground: 前景图片
            mask: 蒙版
            shadow_config: 阴影配置
            
        Returns:
            np.ndarray: 阴影图层
        """
        # 获取阴影配置
        blur_radius = shadow_config.get('blur', self.config['shadow_blur'])
        opacity = shadow_config.get('opacity', self.config['shadow_opacity'])
        offset_x, offset_y = shadow_config.get('offset', self.config['shadow_offset'])
        color = shadow_config.get('color', (0, 0, 0))  # 默认黑色阴影
        
        # 创建阴影蒙版
        shadow_mask = mask.copy()
        
        # 应用偏移
        if offset_x != 0 or offset_y != 0:
            M = np.float32([[1, 0, offset_x], [0, 1, offset_y]])
            shadow_mask = cv2.warpAffine(shadow_mask, M, (shadow_mask.shape[1], shadow_mask.shape[0]))
        
        # 应用模糊
        if blur_radius > 0:
            shadow_mask = cv2.GaussianBlur(shadow_mask, (blur_radius*2+1, blur_radius*2+1), 0)
        
        # 创建阴影图层
        shadow_layer = np.zeros_like(foreground)
        shadow_layer[:, :] = color
        
        # 应用阴影蒙版和透明度
        shadow_alpha = shadow_mask.astype(np.float32) / 255.0 * opacity
        shadow_alpha_3d = np.stack([shadow_alpha] * 3, axis=2)
        
        return shadow_layer, shadow_alpha_3d
    
    def _add_shadow_layer(
        self,
        image: np.ndarray,
        shadow_data: Tuple[np.ndarray, np.ndarray]
    ) -> np.ndarray:
        """
        添加阴影图层
        
        Args:
            image: 原图像
            shadow_data: 阴影数据 (阴影图层, 阴影透明度)
            
        Returns:
            np.ndarray: 添加阴影后的图像
        """
        shadow_layer, shadow_alpha = shadow_data
        
        # 混合阴影
        image_norm = image.astype(np.float32) / 255.0
        shadow_norm = shadow_layer.astype(np.float32) / 255.0
        
        # 使用multiply混合模式添加阴影
        result = image_norm * (1 - shadow_alpha) + (image_norm * shadow_norm) * shadow_alpha
        
        return np.clip(result * 255, 0, 255)
    
    def create_gradient_mask(
        self,
        size: Tuple[int, int],
        gradient_type: str = 'linear',
        direction: str = 'horizontal',
        **kwargs
    ) -> np.ndarray:
        """
        创建渐变蒙版
        
        Args:
            size: 蒙版尺寸 (width, height)
            gradient_type: 渐变类型 ('linear', 'radial')
            direction: 方向 ('horizontal', 'vertical', 'diagonal')
            **kwargs: 其他参数
            
        Returns:
            np.ndarray: 渐变蒙版
        """
        width, height = size
        
        if gradient_type == 'linear':
            if direction == 'horizontal':
                gradient = np.linspace(0, 255, width, dtype=np.uint8)
                mask = np.tile(gradient, (height, 1))
            elif direction == 'vertical':
                gradient = np.linspace(0, 255, height, dtype=np.uint8)
                mask = np.tile(gradient.reshape(-1, 1), (1, width))
            elif direction == 'diagonal':
                x = np.linspace(0, 1, width)
                y = np.linspace(0, 1, height)
                X, Y = np.meshgrid(x, y)
                mask = ((X + Y) / 2 * 255).astype(np.uint8)
            else:
                raise ValueError(f"不支持的线性渐变方向: {direction}")
                
        elif gradient_type == 'radial':
            center_x = kwargs.get('center_x', width // 2)
            center_y = kwargs.get('center_y', height // 2)
            max_radius = kwargs.get('max_radius', min(width, height) // 2)
            
            y, x = np.ogrid[:height, :width]
            distance = np.sqrt((x - center_x)**2 + (y - center_y)**2)
            mask = np.clip(255 - (distance / max_radius * 255), 0, 255).astype(np.uint8)
            
        else:
            raise ValueError(f"不支持的渐变类型: {gradient_type}")
        
        return mask
