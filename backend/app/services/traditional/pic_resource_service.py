"""
图片资源管理服务
负责扫描和管理 pic_resource 目录中的图片资源
"""

import os
import random
import logging
from typing import Dict, List, Optional, Tuple
from pathlib import Path
from PIL import Image

logger = logging.getLogger(__name__)


class PicResourceService:
    """图片资源管理服务"""
    
    def __init__(self, resource_dir: str = "../pic_resource"):
        """
        初始化图片资源服务
        
        Args:
            resource_dir: 图片资源目录路径
        """
        self.resource_dir = Path(resource_dir)
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.webp', '.tiff'}
        
        # 天气条件映射
        self.weather_mapping = {
            "雨天": "rainy",
            "雪天": "snowy", 
            "大雾": "foggy",
            "夜间": "night",
            "白天": "day",
            "晴天": "day"
        }
        
        # 场景环境映射
        self.scene_mapping = {
            "城市": "city",
            "岛屿": "island", 
            "乡村": "rural"
        }
        
        # 缓存图片列表
        self._image_cache = {}
        self._cache_valid = False
        
        logger.info(f"图片资源服务初始化完成，资源目录: {self.resource_dir}")
    
    def initialize(self) -> bool:
        """
        初始化服务，扫描图片资源
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            if not self.resource_dir.exists():
                logger.error(f"图片资源目录不存在: {self.resource_dir}")
                return False
            
            # 扫描图片资源
            self._scan_images()
            self._cache_valid = True
            
            logger.info("图片资源服务初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"图片资源服务初始化失败: {str(e)}")
            return False
    
    def _scan_images(self):
        """扫描图片资源目录"""
        self._image_cache.clear()
        
        try:
            for weather_dir in self.resource_dir.iterdir():
                if not weather_dir.is_dir():
                    continue
                    
                weather_key = weather_dir.name
                self._image_cache[weather_key] = {}
                
                for scene_dir in weather_dir.iterdir():
                    if not scene_dir.is_dir():
                        continue
                        
                    scene_key = scene_dir.name
                    images = []
                    
                    # 扫描图片文件
                    for img_file in scene_dir.iterdir():
                        if (img_file.is_file() and 
                            img_file.suffix.lower() in self.supported_formats):
                            images.append(str(img_file))
                    
                    if images:
                        self._image_cache[weather_key][scene_key] = images
                        logger.debug(f"发现 {len(images)} 张图片: {weather_key}/{scene_key}")
            
            total_images = sum(
                len(scenes.get(scene, [])) 
                for scenes in self._image_cache.values() 
                for scene in scenes
            )
            logger.info(f"图片扫描完成，共发现 {total_images} 张图片")
            
        except Exception as e:
            logger.error(f"扫描图片资源失败: {str(e)}")
            raise
    
    def get_random_image(self, weather: str, scene: str) -> Optional[str]:
        """
        根据天气和场景获取随机图片
        
        Args:
            weather: 天气条件（中文）
            scene: 场景环境（中文）
            
        Returns:
            Optional[str]: 图片文件路径，如果没有找到则返回None
        """
        try:
            # 映射中文到英文目录名
            weather_en = self.weather_mapping.get(weather)
            scene_en = self.scene_mapping.get(scene)
            
            if not weather_en or not scene_en:
                logger.warning(f"无法映射天气或场景: {weather} -> {weather_en}, {scene} -> {scene_en}")
                return None
            
            # 检查缓存是否有效
            if not self._cache_valid:
                self._scan_images()
                self._cache_valid = True
            
            # 获取匹配的图片列表
            images = self._image_cache.get(weather_en, {}).get(scene_en, [])
            
            if not images:
                logger.warning(f"未找到匹配的图片: {weather_en}/{scene_en}")
                return None
            
            # 随机选择一张图片
            selected_image = random.choice(images)
            logger.debug(f"选择图片: {selected_image}")
            
            return selected_image
            
        except Exception as e:
            logger.error(f"获取随机图片失败: {str(e)}")
            return None
    
    def get_available_resources(self) -> Dict[str, Dict[str, int]]:
        """
        获取可用的图片资源统计
        
        Returns:
            Dict[str, Dict[str, int]]: 按天气和场景分类的图片数量统计
        """
        try:
            if not self._cache_valid:
                self._scan_images()
                self._cache_valid = True
            
            result = {}
            for weather_en, scenes in self._image_cache.items():
                # 反向映射英文到中文
                weather_cn = None
                for cn, en in self.weather_mapping.items():
                    if en == weather_en:
                        weather_cn = cn
                        break
                
                if weather_cn:
                    result[weather_cn] = {}
                    for scene_en, images in scenes.items():
                        # 反向映射英文到中文
                        scene_cn = None
                        for cn, en in self.scene_mapping.items():
                            if en == scene_en:
                                scene_cn = cn
                                break
                        
                        if scene_cn:
                            result[weather_cn][scene_cn] = len(images)
            
            return result
            
        except Exception as e:
            logger.error(f"获取资源统计失败: {str(e)}")
            return {}
    
    def validate_image(self, image_path: str) -> bool:
        """
        验证图片文件是否有效
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            bool: 图片是否有效
        """
        try:
            if not os.path.exists(image_path):
                return False
            
            with Image.open(image_path) as img:
                # 尝试加载图片以验证格式
                img.verify()
                return True
                
        except Exception as e:
            logger.warning(f"图片验证失败 {image_path}: {str(e)}")
            return False
    
    def refresh_cache(self):
        """刷新图片缓存"""
        try:
            self._scan_images()
            self._cache_valid = True
            logger.info("图片缓存刷新完成")
        except Exception as e:
            logger.error(f"刷新图片缓存失败: {str(e)}")
            self._cache_valid = False


# 全局实例
_pic_resource_service = None


def get_pic_resource_service() -> PicResourceService:
    """获取图片资源服务实例"""
    global _pic_resource_service
    if _pic_resource_service is None:
        _pic_resource_service = PicResourceService()
        _pic_resource_service.initialize()
    return _pic_resource_service
