"""
简化的图片合成服务
专注于背景图片选择和军事目标叠加的最简化实现
"""
import os
import asyncio
import logging
import random
import time
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
from PIL import Image, ImageEnhance
import numpy as np

from ..common.annotation_generator import AnnotationGenerator
from ..common.dataset_manager import DatasetManager
from .simple_background_selector import get_simple_background_selector
from .simple_target_processor import get_simple_target_processor
from .simple_compositor import get_simple_compositor

logger = logging.getLogger(__name__)


class SimpleCompositionService:
    """简化的图片合成服务"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化简化合成服务

        Args:
            config: 配置字典
        """
        self.config = config or {
            "output_dir": "data/generated/traditional",
            "quality": 95
        }

        # 确保输出目录存在
        os.makedirs(self.config["output_dir"], exist_ok=True)

        # 初始化组件
        self.background_selector = get_simple_background_selector()
        self.target_processor = get_simple_target_processor()
        self.compositor = get_simple_compositor()
        self.annotation_generator = AnnotationGenerator()

        # 初始化数据集管理器
        self.dataset_manager = DatasetManager()

        # 传统图片数据集名称
        self.traditional_dataset_name = None

        # 服务状态
        self.is_initialized = False

        logger.info("简化合成服务初始化完成")

    def _ensure_traditional_dataset(self) -> str:
        """
        确保传统图片数据集存在

        Returns:
            str: 数据集文件夹名称
        """
        if self.traditional_dataset_name:
            return self.traditional_dataset_name

        try:
            # 创建传统图片数据集
            folder_name, folder_path = self.dataset_manager.create_dataset_folder(
                name="传统图片合成",
                naming_format="timestamp"
            )

            self.traditional_dataset_name = folder_name
            logger.info(f"创建传统图片数据集: {folder_name}")

            return folder_name

        except Exception as e:
            logger.error(f"创建传统图片数据集失败: {str(e)}")
            # 使用默认名称
            self.traditional_dataset_name = "traditional_composition"
            return self.traditional_dataset_name

    async def initialize(self) -> bool:
        """
        初始化服务
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 初始化各个组件
            bg_init = self.background_selector.initialize()
            target_init = self.target_processor.initialize()

            if not bg_init:
                logger.error("背景选择器初始化失败")
                return False

            if not target_init:
                logger.error("目标处理器初始化失败")
                return False

            self.is_initialized = True
            logger.info("简化合成服务初始化成功")
            return True

        except Exception as e:
            logger.error(f"简化合成服务初始化失败: {str(e)}")
            return False
    
    async def generate_images(
        self,
        military_target: str,
        weather: str,
        scene: str,
        num_images: int = 1,
        **kwargs
    ) -> Dict[str, Any]:
        """
        生成图像
        
        Args:
            military_target: 军事目标类型
            weather: 天气条件
            scene: 场景环境
            num_images: 生成图像数量
            **kwargs: 其他参数
            
        Returns:
            Dict: 生成结果
        """
        if not self.is_initialized:
            raise RuntimeError("服务未初始化")
        
        try:
            logger.info(f"开始简化合成: {military_target} + {weather} + {scene}")
            
            results = []
            for i in range(num_images):
                result = await self._compose_single_image(
                    military_target, weather, scene, i, **kwargs
                )
                if result:
                    results.append(result)
            
            return {
                "success": True,
                "message": f"成功生成 {len(results)} 张图像",
                "results": results,
                "total_generated": len(results)
            }
            
        except Exception as e:
            logger.error(f"简化合成失败: {str(e)}")
            return {
                "success": False,
                "message": f"生成失败: {str(e)}",
                "results": [],
                "total_generated": 0
            }
    
    async def _compose_single_image(
        self,
        military_target: str,
        weather: str,
        scene: str,
        index: int,
        **kwargs
    ) -> Optional[Dict[str, Any]]:
        """
        合成单张图像
        
        Args:
            military_target: 军事目标类型
            weather: 天气条件
            scene: 场景环境
            index: 图像索引
            **kwargs: 其他参数
            
        Returns:
            Optional[Dict]: 合成结果
        """
        start_time = time.time()
        
        try:
            # 1. 选择背景图片
            background_path = self.background_selector.get_background_image(weather, scene)
            if not background_path:
                logger.error(f"未找到合适的背景图片: {weather} + {scene}")
                return None

            # 2. 选择军事目标图片
            target_path = self.target_processor.get_random_target(military_target)
            if not target_path:
                logger.error(f"未找到合适的目标图片: {military_target}")
                return None

            # 3. 加载背景图片（保持原始尺寸）
            background_img = Image.open(background_path).convert('RGB')

            # 4. 获取背景图片的原始尺寸
            background_size = background_img.size
            logger.debug(f"使用背景图片原始尺寸: {background_size}")

            # 5. 加载并处理军事目标图片
            processed_target = self.target_processor.load_and_process_target(
                target_path, background_size, **kwargs
            )
            if not processed_target:
                logger.error(f"处理目标图片失败: {target_path}")
                return None

            # 6. 合成图片
            composite_img, composition_info = self.compositor.composite_images(
                background_img, processed_target, **kwargs
            )

            # 7. 保存图片
            filename = f"{military_target}_{weather}_{scene}_{index:03d}.jpg"
            output_path = os.path.join(self.config['output_dir'], filename)

            composite_img.save(output_path, 'JPEG', quality=self.config['quality'])

            # 8. 添加到传统图片数据集
            try:
                dataset_folder = self._ensure_traditional_dataset()
                logger.info(f"使用传统数据集: {dataset_folder}")

                # 构建生成参数信息
                generation_params = {
                    "military_target": military_target,
                    "weather": weather,
                    "scene": scene,
                    "background_path": background_path,
                    "target_path": target_path,
                    "background_size": background_size,
                    "processing_time": time.time() - start_time,
                    **kwargs
                }

                # 添加到数据集
                success = self.dataset_manager.add_generated_image_to_dataset(
                    dataset_folder,
                    output_path,
                    "traditional_generation",
                    generation_params,
                    {
                        "military_target": military_target,
                        "weather": weather,
                        "scene": scene,
                        "description": f"传统合成图片: {military_target}在{weather}天气下的{scene}场景"
                    }
                )

                if success:
                    logger.info(f"图片已添加到传统数据集: {filename}")
                else:
                    logger.warning(f"添加图片到数据集失败: {filename}")

            except Exception as e:
                logger.error(f"添加图片到数据集时发生错误: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())

            # 9. 保存到数据库（保持原有功能）
            self._save_to_database(
                output_path, filename, military_target, weather, scene, background_size
            )

            # 10. 生成标注
            bbox = self.compositor.generate_bbox_annotation(
                background_size,
                composition_info.get('target_size', processed_target.size),
                composition_info.get('target_position', (0, 0))
            )
            segmentation = self.compositor.generate_segmentation_annotation(
                background_size,
                composition_info.get('target_size', processed_target.size),
                composition_info.get('target_position', (0, 0))
            )
            
            processing_time = time.time() - start_time

            return {
                "image_path": output_path,
                "filename": filename,
                "target_type": military_target,
                "weather": weather,
                "scene": scene,
                "bbox": bbox,
                "segmentation": segmentation,
                "size": background_size,
                "processing_time": processing_time,
                "metadata": {
                    "background_path": background_path,
                    "target_path": target_path,
                    "composition_method": "simple_overlay",
                    "composition_info": composition_info
                }
            }
            
        except Exception as e:
            logger.error(f"合成单张图像失败: {str(e)}")
            return None

    def get_status(self) -> Dict[str, Any]:
        """
        获取服务状态

        Returns:
            Dict[str, Any]: 服务状态信息
        """
        return {
            "service_name": "SimpleCompositionService",
            "is_initialized": self.is_initialized,
            "config": self.config,
            "background_selector_status": self.background_selector.get_available_combinations() if self.is_initialized else {},
            "target_processor_status": self.target_processor.get_available_targets() if self.is_initialized else {}
        }

    def _save_to_database(
        self,
        file_path: str,
        filename: str,
        military_target: str,
        weather: str,
        scene: str,
        image_size: Tuple[int, int]
    ):
        """
        保存图片到数据库

        Args:
            file_path: 文件路径
            filename: 文件名
            military_target: 军事目标
            weather: 天气
            scene: 场景
            image_size: 图片尺寸
        """
        try:
            import sys
            import os

            # 添加项目根目录到Python路径
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
            backend_path = os.path.join(project_root, 'backend')
            if backend_path not in sys.path:
                sys.path.insert(0, backend_path)

            from app.crud.dataset import ImageCRUD
            from app.db.session import get_db

            # 获取图片信息
            file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
            width, height = image_size

            # 获取数据库会话
            db_gen = get_db()
            db = next(db_gen)

            try:
                # 创建图片记录
                image_record = ImageCRUD.create_image(
                    db=db,
                    filename=filename,
                    file_path=file_path,
                    file_size=file_size,
                    width=width,
                    height=height,
                    generation_type="traditional_simple",
                    military_target=military_target,
                    weather=weather,
                    scene=scene,
                    # generation_params={
                    #     "method": "simple_composition",
                    #     "service": "SimpleCompositionService"
                    # }
                )

                logger.info(f"图片已保存到数据库: {filename} (ID: {image_record.id})")

            finally:
                db.close()

        except Exception as e:
            logger.error(f"保存图片到数据库失败: {str(e)}")
            # 不抛出异常，避免影响图片生成流程


