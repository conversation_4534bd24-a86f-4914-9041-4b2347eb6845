"""
传统生成服务
基于蒙版合成的传统图像生成服务
"""

import os
import logging
from typing import Dict, List, Any, Optional, Tuple
from PIL import Image
import asyncio

from ..common.annotation_generator import AnnotationGenerator

logger = logging.getLogger(__name__)

class TraditionalGenerationService:
    """传统生成服务"""
    
    def __init__(self):
        """初始化传统生成服务"""
        self.annotation_generator = AnnotationGenerator()
        self.is_initialized = False
        
        # 配置参数
        self.config = {
            "output_dir": "data/generated/traditional",
            "supported_formats": [".jpg", ".jpeg", ".png", ".bmp"],
            "max_image_size": (2048, 2048),
            "quality": 95
        }
        
        logger.info("传统生成服务已创建")
    
    async def initialize(self):
        """初始化服务"""
        try:
            logger.info("正在初始化传统生成服务...")
            
            # 创建输出目录
            os.makedirs(self.config["output_dir"], exist_ok=True)
            
            # 检查必要的资源
            await self._check_resources()
            
            self.is_initialized = True
            logger.info("传统生成服务初始化完成")
            
        except Exception as e:
            logger.error(f"传统生成服务初始化失败: {str(e)}")
            raise
    
    async def _check_resources(self):
        """检查必要的资源文件"""
        # 检查数据目录
        data_dirs = [
            "data/raw_assets/targets",
            "data/raw_assets/backgrounds"
        ]
        
        for dir_path in data_dirs:
            if not os.path.exists(dir_path):
                os.makedirs(dir_path, exist_ok=True)
                logger.info(f"创建目录: {dir_path}")
    
    async def generate_images(
        self,
        military_target: str,
        weather: str,
        scene: str,
        num_images: int = 1,
        **kwargs
    ) -> Dict[str, Any]:
        """
        生成图像
        
        Args:
            military_target: 军事目标类型
            weather: 天气条件
            scene: 场景环境
            num_images: 生成图像数量
            **kwargs: 其他参数
            
        Returns:
            Dict: 生成结果
        """
        if not self.is_initialized:
            raise RuntimeError("服务未初始化")
        
        try:
            logger.info(f"开始传统生成: {military_target} + {weather} + {scene}")
            
            # 模拟传统生成过程
            results = []
            for i in range(num_images):
                # 这里应该实现实际的蒙版合成逻辑
                result = await self._generate_single_image(
                    military_target, weather, scene, i
                )
                results.append(result)
            
            return {
                "success": True,
                "message": f"成功生成 {len(results)} 张图像",
                "results": results,
                "total_generated": len(results)
            }
            
        except Exception as e:
            logger.error(f"传统生成失败: {str(e)}")
            return {
                "success": False,
                "message": f"生成失败: {str(e)}",
                "results": [],
                "total_generated": 0
            }
    
    async def _generate_single_image(
        self,
        military_target: str,
        weather: str,
        scene: str,
        index: int
    ) -> Dict[str, Any]:
        """
        生成单张图像 - 实现真正的图片合成逻辑

        Args:
            military_target: 军事目标
            weather: 天气条件
            scene: 场景环境
            index: 图像索引

        Returns:
            Dict: 生成结果
        """
        try:
            # 导入简化合成服务
            from ..traditional.simple_composition_service import SimpleCompositionService

            # 创建简化合成服务实例
            composition_config = {
                "output_dir": self.config["output_dir"],
                "quality": 95
            }
            composition_service = SimpleCompositionService(composition_config)
            await composition_service.initialize()

            # 调用简化合成服务生成图像
            result = await composition_service._compose_single_image(
                military_target, weather, scene, index
            )

            return result if result else self._create_fallback_image(
                military_target, weather, scene, index
            )

        except Exception as e:
            logger.error(f"生成单张图像失败: {str(e)}")
            # 创建备用图像
            return self._create_fallback_image(military_target, weather, scene, index)

    def _create_fallback_image(
        self,
        military_target: str,
        weather: str,
        scene: str,
        index: int
    ) -> Dict[str, Any]:
        """
        创建备用图像（当合成失败时使用）
        """
        try:
            # 创建一个带有文本标识的图像
            image_size = (512, 512)
            image = Image.new('RGB', image_size, color=(64, 64, 64))

            # 添加文本标识
            try:
                from PIL import ImageDraw, ImageFont
                draw = ImageDraw.Draw(image)

                # 尝试使用默认字体
                try:
                    font = ImageFont.truetype("arial.ttf", 24)
                except:
                    font = ImageFont.load_default()

                text = f"{military_target}\n{weather}\n{scene}"

                # 计算文本位置
                bbox = draw.textbbox((0, 0), text, font=font)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]

                x = (image_size[0] - text_width) // 2
                y = (image_size[1] - text_height) // 2

                draw.text((x, y), text, fill=(255, 255, 255), font=font)

            except Exception as text_error:
                logger.warning(f"添加文本标识失败: {str(text_error)}")

            # 生成文件名
            filename = f"{military_target}_{weather}_{scene}_{index:03d}.jpg"
            output_path = os.path.join(self.config["output_dir"], filename)

            # 保存图像
            image.save(output_path, quality=self.config["quality"])

            # 保存到数据库
            self._save_fallback_to_database(
                output_path,
                filename,
                military_target,
                weather,
                scene,
                image_size
            )

            # 生成标注
            bbox, segmentation = self.annotation_generator.generate_auto_annotation(
                output_path, military_target
            )

            # 转换为相对路径，便于前端访问
            # 如果当前在backend目录，需要相对于上级目录计算
            current_dir = os.getcwd()
            if current_dir.endswith('backend'):
                project_root = os.path.dirname(current_dir)
                relative_path = os.path.relpath(output_path, start=project_root)
            else:
                relative_path = os.path.relpath(output_path, start=current_dir)

            return {
                "file_path": relative_path,  # 使用file_path字段保持一致性
                "image_path": output_path,  # 保留原字段以兼容
                "filename": filename,
                "target_type": military_target,
                "weather": weather,
                "scene": scene,
                "bbox": bbox,
                "segmentation": segmentation,
                "size": image_size,
                "fallback": True  # 标记为备用图像
            }

        except Exception as e:
            logger.error(f"创建备用图像失败: {str(e)}")
            raise

    def _save_fallback_to_database(
        self,
        file_path: str,
        filename: str,
        military_target: str,
        weather: str,
        scene: str,
        image_size: tuple
    ):
        """
        保存fallback图片到数据库

        Args:
            file_path: 文件路径
            filename: 文件名
            military_target: 军事目标
            weather: 天气
            scene: 场景
            image_size: 图片尺寸
        """
        try:
            import sys
            import os
            import time

            # 添加项目根目录到Python路径
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
            if project_root not in sys.path:
                sys.path.insert(0, project_root)

            from app.crud.dataset import ImageCRUD
            from app.db.session import get_db

            # 获取图片信息
            file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
            width, height = image_size

            # 获取数据库会话
            db_gen = get_db()
            db = next(db_gen)

            try:
                # 创建图片记录
                image_record = ImageCRUD.create_image(
                    db=db,
                    filename=filename,
                    file_path=file_path,
                    file_size=file_size,
                    width=width,
                    height=height,
                    generation_type="traditional",  # 标记为传统生成
                    format="JPEG",
                    military_target=military_target,
                    weather=weather,
                    scene=scene,
                    generation_id=f"traditional_fallback_{int(time.time())}",
                    description=f"传统合成图片(备用): {military_target} in {weather} {scene}",
                    tags=f"{military_target},{weather},{scene},traditional,fallback",
                    category="traditional_generation",
                    metadata_json={
                        "generation_method": "traditional_fallback",
                        "is_fallback": True
                    }
                )

                logger.info(f"传统合成备用图片已保存到数据库: {filename} (ID: {image_record.id})")

            finally:
                db.close()

        except Exception as e:
            logger.error(f"保存传统合成备用图片到数据库失败: {str(e)}")
            # 不抛出异常，避免影响图片生成流程

    async def create_dataset(
        self,
        generation_results: List[Dict[str, Any]],
        dataset_name: str = "traditional_dataset"
    ) -> Dict[str, Any]:
        """
        创建数据集
        
        Args:
            generation_results: 生成结果列表
            dataset_name: 数据集名称
            
        Returns:
            Dict: 数据集创建结果
        """
        try:
            # 创建COCO数据集
            coco_dataset = self.annotation_generator.create_coco_dataset(
                dataset_name=dataset_name,
                description="Traditional generation dataset"
            )
            
            # 添加图像和标注
            for result in generation_results:
                if result.get("image_path"):
                    self.annotation_generator.add_image_annotation(
                        coco_dataset,
                        result["image_path"],
                        result["target_type"],
                        result.get("bbox"),
                        result.get("segmentation")
                    )
            
            # 保存标注文件
            annotation_path = os.path.join(
                self.config["output_dir"],
                f"{dataset_name}_annotations.json"
            )
            
            success = self.annotation_generator.save_annotations(
                coco_dataset, annotation_path
            )
            
            if success:
                return {
                    "success": True,
                    "message": "数据集创建成功",
                    "annotation_path": annotation_path,
                    "num_images": len(generation_results),
                    "num_annotations": len(coco_dataset["annotations"])
                }
            else:
                return {
                    "success": False,
                    "message": "数据集创建失败"
                }
                
        except Exception as e:
            logger.error(f"创建数据集失败: {str(e)}")
            return {
                "success": False,
                "message": f"创建数据集失败: {str(e)}"
            }
    
    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        return {
            "service_name": "traditional_generation",
            "status": "running" if self.is_initialized else "stopped",
            "initialized": self.is_initialized,
            "config": self.config
        }
    
    def cleanup(self):
        """清理资源"""
        try:
            logger.info("清理传统生成服务资源...")
            self.is_initialized = False
            logger.info("传统生成服务资源清理完成")
            
        except Exception as e:
            logger.error(f"清理传统生成服务失败: {str(e)}")
    
    def get_available_options(self) -> Dict[str, List[str]]:
        """获取可用选项"""
        return {
            "military_targets": ["坦克", "战机", "舰艇"],
            "weather_conditions": ["雨天", "雪天", "大雾", "夜间"],
            "scene_environments": ["城市", "岛屿", "乡村"]
        } 