"""
数据库模型定义
"""
from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy import Column, Integer, String, DateTime, Text, Float, Boolean, JSON, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()


class GeneratedImage(Base):
    """生成的图片模型"""
    __tablename__ = "generated_images"

    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String(255), nullable=False, index=True)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=False)
    file_hash = Column(String(64), nullable=False, index=True)

    # 图片基本信息
    width = Column(Integer, nullable=False)
    height = Column(Integer, nullable=False)
    format = Column(String(10), nullable=False, default="PNG")

    # 生成信息
    generation_type = Column(String(20), nullable=False, index=True)  # ai, traditional, uploaded
    generation_id = Column(String(50), nullable=True, index=True)

    # AI生成参数
    military_target = Column(String(50), nullable=True, index=True)
    weather = Column(String(50), nullable=True, index=True)
    scene = Column(String(50), nullable=True, index=True)
    prompt = Column(Text, nullable=True)
    negative_prompt = Column(Text, nullable=True)
    steps = Column(Integer, nullable=True)
    cfg_scale = Column(Float, nullable=True)
    seed = Column(Integer, nullable=True)
    scheduler_name = Column(String(50), nullable=True)
    model_name = Column(String(100), nullable=True)

    # 用户标注和分类
    tags = Column(JSON, nullable=True)  # 标签列表
    description = Column(Text, nullable=True)
    category = Column(String(50), nullable=True, index=True)
    is_favorite = Column(Boolean, default=False, index=True)

    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # 关联关系
    annotations = relationship("ImageAnnotation", back_populates="image", cascade="all, delete-orphan")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "filename": self.filename,
            "file_path": self.file_path,
            "file_size": self.file_size,
            "file_hash": self.file_hash,
            "width": self.width,
            "height": self.height,
            "format": self.format,
            "generation_type": self.generation_type,
            "generation_id": self.generation_id,
            "military_target": self.military_target,
            "weather": self.weather,
            "scene": self.scene,
            "prompt": self.prompt,
            "negative_prompt": self.negative_prompt,
            "steps": self.steps,
            "cfg_scale": self.cfg_scale,
            "seed": self.seed,
            "scheduler_name": self.scheduler_name,
            "model_name": self.model_name,
            "tags": self.tags or [],
            "description": self.description,
            "category": self.category,
            "is_favorite": self.is_favorite,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "annotation_count": len(self.annotations) if self.annotations else 0
        }


class ImageAnnotation(Base):
    """图片标注模型"""
    __tablename__ = "image_annotations"

    id = Column(Integer, primary_key=True, index=True)
    image_id = Column(Integer, ForeignKey("generated_images.id"), nullable=False, index=True)

    # 标注基本信息
    annotation_type = Column(String(20), nullable=False, default="bbox")  # bbox, polygon, point
    format = Column(String(20), nullable=False, default="coco")  # coco, yolo, pascal_voc

    # 边界框坐标 (COCO格式: x, y, width, height)
    bbox_x = Column(Float, nullable=True)
    bbox_y = Column(Float, nullable=True)
    bbox_width = Column(Float, nullable=True)
    bbox_height = Column(Float, nullable=True)

    # 标注内容
    label = Column(String(100), nullable=False, index=True)
    confidence = Column(Float, nullable=True)  # 检测置信度

    # 额外的标注数据 (如多边形坐标等)
    annotation_data = Column(JSON, nullable=True)

    # 标注来源
    source = Column(String(20), nullable=False, default="manual")  # manual, gsa, yolo

    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # 关联关系
    image = relationship("GeneratedImage", back_populates="annotations")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "image_id": self.image_id,
            "annotation_type": self.annotation_type,
            "format": self.format,
            "bbox_x": self.bbox_x,
            "bbox_y": self.bbox_y,
            "bbox_width": self.bbox_width,
            "bbox_height": self.bbox_height,
            "label": self.label,
            "confidence": self.confidence,
            "annotation_data": self.annotation_data,
            "source": self.source,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }


class Dataset(Base):
    """数据集模型"""
    __tablename__ = "datasets"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)

    # 数据集配置
    generation_type = Column(String(20), nullable=False, index=True)
    target_count = Column(Integer, nullable=False, default=0)
    generated_count = Column(Integer, nullable=False, default=0)

    # 生成参数
    generation_config = Column(JSON, nullable=True)

    # 状态
    status = Column(String(20), nullable=False, default="pending", index=True)  # pending, generating, completed, failed

    # 文件路径
    output_path = Column(String(500), nullable=True)

    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    completed_at = Column(DateTime, nullable=True)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "generation_type": self.generation_type,
            "target_count": self.target_count,
            "generated_count": self.generated_count,
            "generation_config": self.generation_config,
            "status": self.status,
            "output_path": self.output_path,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None
        }