"""
自定义模型管理API路由
"""

from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from pydantic import BaseModel, Field

from ...services.ai_generation.custom_model_manager import CustomModelManager
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/custom-models", tags=["自定义模型管理"])

# 全局自定义模型管理器实例
custom_model_manager = CustomModelManager()

# Pydantic模型
class CustomModelAddRequest(BaseModel):
    """添加自定义模型请求"""
    model_key: str = Field(..., description="模型唯一标识符")
    model_name: str = Field(..., description="模型显示名称")
    model_path: str = Field(..., description="模型路径或HuggingFace ID")
    description: str = Field(default="", description="模型描述")
    model_type: str = Field(default="diffusers", description="模型类型")
    cfg_scale_range: List[float] = Field(default=[7.0, 12.0], description="CFG范围")
    steps_range: List[int] = Field(default=[20, 50], description="步数范围")
    target_size_bias: str = Field(default="medium", description="目标大小偏好")
    recommended_for: List[str] = Field(default=["自定义场景"], description="推荐用途")

class CustomModelUpdateRequest(BaseModel):
    """更新自定义模型请求"""
    model_name: Optional[str] = None
    description: Optional[str] = None
    enabled: Optional[bool] = None
    cfg_scale_range: Optional[List[float]] = None
    steps_range: Optional[List[int]] = None
    target_size_bias: Optional[str] = None
    recommended_for: Optional[List[str]] = None

class APIResponse(BaseModel):
    """API响应"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None

@router.get("/list", response_model=APIResponse)
async def list_custom_models():
    """列出所有自定义模型"""
    try:
        models = custom_model_manager.get_custom_models()
        return APIResponse(
            success=True,
            message=f"获取到 {len(models)} 个自定义模型",
            data={
                "models": models,
                "total": len(models)
            }
        )
    except Exception as e:
        logger.error(f"列出自定义模型失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"列出自定义模型失败: {str(e)}")

@router.get("/enabled", response_model=APIResponse)
async def list_enabled_custom_models():
    """列出启用的自定义模型"""
    try:
        models = custom_model_manager.get_enabled_custom_models()
        return APIResponse(
            success=True,
            message=f"获取到 {len(models)} 个启用的自定义模型",
            data={
                "models": models,
                "total": len(models)
            }
        )
    except Exception as e:
        logger.error(f"列出启用的自定义模型失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"列出启用的自定义模型失败: {str(e)}")

@router.post("/add", response_model=APIResponse)
async def add_custom_model(request: CustomModelAddRequest):
    """添加自定义模型"""
    try:
        success, message = custom_model_manager.add_custom_model(
            model_key=request.model_key,
            model_name=request.model_name,
            model_path=request.model_path,
            description=request.description,
            model_type=request.model_type,
            cfg_scale_range=request.cfg_scale_range,
            steps_range=request.steps_range,
            target_size_bias=request.target_size_bias,
            recommended_for=request.recommended_for
        )
        
        if success:
            return APIResponse(success=True, message=message)
        else:
            raise HTTPException(status_code=400, detail=message)
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"添加自定义模型失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"添加自定义模型失败: {str(e)}")

@router.put("/{model_key}", response_model=APIResponse)
async def update_custom_model(model_key: str, request: CustomModelUpdateRequest):
    """更新自定义模型"""
    try:
        # 过滤None值
        updates = {k: v for k, v in request.dict().items() if v is not None}
        
        if not updates:
            raise HTTPException(status_code=400, detail="没有提供要更新的字段")
        
        success, message = custom_model_manager.update_custom_model(model_key, **updates)
        
        if success:
            return APIResponse(success=True, message=message)
        else:
            raise HTTPException(status_code=400, detail=message)
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新自定义模型失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新自定义模型失败: {str(e)}")

@router.delete("/{model_key}", response_model=APIResponse)
async def remove_custom_model(model_key: str, delete_files: bool = False):
    """移除自定义模型"""
    try:
        success, message = custom_model_manager.remove_custom_model(model_key, delete_files)
        
        if success:
            return APIResponse(success=True, message=message)
        else:
            raise HTTPException(status_code=404, detail=message)
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"移除自定义模型失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"移除自定义模型失败: {str(e)}")

@router.get("/{model_key}", response_model=APIResponse)
async def get_custom_model(model_key: str):
    """获取指定自定义模型"""
    try:
        model = custom_model_manager.get_custom_model(model_key)
        
        if model:
            return APIResponse(
                success=True,
                message="获取自定义模型成功",
                data={"model": model}
            )
        else:
            raise HTTPException(status_code=404, detail=f"自定义模型 '{model_key}' 不存在")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取自定义模型失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取自定义模型失败: {str(e)}")

@router.post("/{model_key}/enable", response_model=APIResponse)
async def enable_custom_model(model_key: str):
    """启用自定义模型"""
    try:
        success, message = custom_model_manager.enable_custom_model(model_key)
        
        if success:
            return APIResponse(success=True, message=message)
        else:
            raise HTTPException(status_code=400, detail=message)
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启用自定义模型失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"启用自定义模型失败: {str(e)}")

@router.post("/{model_key}/disable", response_model=APIResponse)
async def disable_custom_model(model_key: str):
    """禁用自定义模型"""
    try:
        success, message = custom_model_manager.disable_custom_model(model_key)
        
        if success:
            return APIResponse(success=True, message=message)
        else:
            raise HTTPException(status_code=400, detail=message)
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"禁用自定义模型失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"禁用自定义模型失败: {str(e)}")

@router.post("/{model_key}/validate", response_model=APIResponse)
async def validate_custom_model(model_key: str):
    """验证自定义模型"""
    try:
        is_valid, message = custom_model_manager.validate_custom_model(model_key)
        
        return APIResponse(
            success=is_valid,
            message=message,
            data={"is_valid": is_valid}
        )
        
    except Exception as e:
        logger.error(f"验证自定义模型失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"验证自定义模型失败: {str(e)}")

@router.post("/import-from-directory", response_model=APIResponse)
async def import_model_from_directory(
    source_dir: str = Form(...),
    model_key: str = Form(...),
    model_name: str = Form(...)
):
    """从目录导入模型"""
    try:
        success, message = custom_model_manager.import_model_from_directory(
            source_dir, model_key, model_name
        )
        
        if success:
            return APIResponse(success=True, message=message)
        else:
            raise HTTPException(status_code=400, detail=message)
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"从目录导入模型失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"从目录导入模型失败: {str(e)}")

@router.get("/statistics", response_model=APIResponse)
async def get_custom_model_statistics():
    """获取自定义模型统计信息"""
    try:
        stats = custom_model_manager.get_model_statistics()
        return APIResponse(
            success=True,
            message="获取统计信息成功",
            data=stats
        )
    except Exception as e:
        logger.error(f"获取自定义模型统计信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")
