"""
图片管理API路由
"""
import os
import shutil
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Query
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from PIL import Image
import math

from ...db.session import get_db
from ...crud.dataset import ImageCRUD, AnnotationCRUD
from ...schemas.image_management import (
    ImageResponse, ImageListRequest, ImageListResponse, ImageUpdate,
    BatchDeleteRequest, BatchUpdateRequest, AnnotationCreate, AnnotationResponse,
    ImageStatistics, UploadResponse, APIResponse
)
from ...core.config import get_settings

# 获取设置
settings = get_settings()

router = APIRouter(prefix="/images", tags=["图片管理"])


@router.get("/", response_model=ImageListResponse)
async def get_images(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    generation_type: Optional[str] = Query(None, description="生成类型筛选"),
    military_target: Optional[str] = Query(None, description="军事目标筛选"),
    weather: Optional[str] = Query(None, description="天气筛选"),
    scene: Optional[str] = Query(None, description="场景筛选"),
    category: Optional[str] = Query(None, description="分类筛选"),
    is_favorite: Optional[bool] = Query(None, description="收藏筛选"),
    search_text: Optional[str] = Query(None, description="搜索文本"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", description="排序方向"),
    db: Session = Depends(get_db)
):
    """获取图片列表"""
    try:
        skip = (page - 1) * page_size
        
        images, total = ImageCRUD.get_images(
            db=db,
            skip=skip,
            limit=page_size,
            generation_type=generation_type,
            military_target=military_target,
            weather=weather,
            scene=scene,
            category=category,
            is_favorite=is_favorite,
            search_text=search_text,
            sort_by=sort_by,
            sort_order=sort_order
        )
        
        total_pages = math.ceil(total / page_size)
        
        return ImageListResponse(
            images=[ImageResponse.model_validate(img.to_dict()) for img in images],
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取图片列表失败: {str(e)}")


@router.get("/{image_id}", response_model=ImageResponse)
async def get_image(image_id: int, db: Session = Depends(get_db)):
    """获取单个图片详情"""
    image = ImageCRUD.get_image(db, image_id)
    if not image:
        raise HTTPException(status_code=404, detail="图片不存在")
    
    return ImageResponse.model_validate(image.to_dict())


@router.get("/{image_id}/file")
async def get_image_file(image_id: int, db: Session = Depends(get_db)):
    """获取图片文件"""
    image = ImageCRUD.get_image(db, image_id)
    if not image:
        raise HTTPException(status_code=404, detail="图片不存在")
    
    if not os.path.exists(image.file_path):
        raise HTTPException(status_code=404, detail="图片文件不存在")
    
    return FileResponse(
        path=image.file_path,
        filename=image.filename,
        media_type="image/png"
    )


@router.post("/upload", response_model=UploadResponse)
async def upload_image(
    file: UploadFile = File(...),
    description: Optional[str] = Form(None),
    tags: Optional[str] = Form(None),  # JSON字符串
    category: Optional[str] = Form(None),
    military_target: Optional[str] = Form(None),
    weather: Optional[str] = Form(None),
    scene: Optional[str] = Form(None),
    db: Session = Depends(get_db)
):
    """上传图片"""
    try:
        # 验证文件类型
        if not file.content_type.startswith("image/"):
            raise HTTPException(status_code=400, detail="只支持图片文件")
        
        # 确保上传目录存在
        upload_dir = os.path.join(settings.upload_dir, "manual")
        os.makedirs(upload_dir, exist_ok=True)
        
        # 保存文件
        file_path = os.path.join(upload_dir, file.filename)
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # 获取图片信息
        with Image.open(file_path) as img:
            width, height = img.size
            format_name = img.format or "PNG"
        
        file_size = os.path.getsize(file_path)
        
        # 处理标签
        tag_list = []
        if tags:
            import json
            try:
                tag_list = json.loads(tags)
            except:
                tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()]
        
        # 创建数据库记录
        image = ImageCRUD.create_image(
            db=db,
            filename=file.filename,
            file_path=file_path,
            file_size=file_size,
            width=width,
            height=height,
            generation_type="uploaded",
            description=description,
            tags=tag_list,
            category=category,
            military_target=military_target,
            weather=weather,
            scene=scene,
            format=format_name
        )
        
        return UploadResponse(
            success=True,
            message="图片上传成功",
            image=ImageResponse.model_validate(image.to_dict())
        )
        
    except ValueError as e:
        # 删除已保存的文件
        if 'file_path' in locals() and os.path.exists(file_path):
            os.remove(file_path)
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        # 删除已保存的文件
        if 'file_path' in locals() and os.path.exists(file_path):
            os.remove(file_path)
        raise HTTPException(status_code=500, detail=f"上传失败: {str(e)}")


@router.put("/{image_id}", response_model=ImageResponse)
async def update_image(
    image_id: int,
    update_data: ImageUpdate,
    db: Session = Depends(get_db)
):
    """更新图片信息"""
    # 只更新非None的字段
    update_dict = {k: v for k, v in update_data.model_dump().items() if v is not None}
    
    image = ImageCRUD.update_image(db, image_id, **update_dict)
    if not image:
        raise HTTPException(status_code=404, detail="图片不存在")
    
    return ImageResponse.model_validate(image.to_dict())


@router.delete("/{image_id}", response_model=APIResponse)
async def delete_image(image_id: int, db: Session = Depends(get_db)):
    """删除图片"""
    success = ImageCRUD.delete_image(db, image_id)
    if not success:
        raise HTTPException(status_code=404, detail="图片不存在")
    
    return APIResponse(success=True, message="图片删除成功")


@router.post("/batch-delete", response_model=APIResponse)
async def batch_delete_images(
    request: BatchDeleteRequest,
    db: Session = Depends(get_db)
):
    """批量删除图片"""
    deleted_count = ImageCRUD.delete_images_batch(db, request.image_ids)
    
    return APIResponse(
        success=True,
        message=f"成功删除 {deleted_count} 张图片",
        data={"deleted_count": deleted_count}
    )


@router.post("/batch-update", response_model=APIResponse)
async def batch_update_images(
    request: BatchUpdateRequest,
    db: Session = Depends(get_db)
):
    """批量更新图片"""
    update_dict = {k: v for k, v in request.updates.model_dump().items() if v is not None}
    
    updated_count = 0
    for image_id in request.image_ids:
        image = ImageCRUD.update_image(db, image_id, **update_dict)
        if image:
            updated_count += 1
    
    return APIResponse(
        success=True,
        message=f"成功更新 {updated_count} 张图片",
        data={"updated_count": updated_count}
    )


@router.get("/statistics/overview", response_model=ImageStatistics)
async def get_image_statistics(db: Session = Depends(get_db)):
    """获取图片统计信息"""
    stats = ImageCRUD.get_statistics(db)
    return ImageStatistics(**stats)


# 标注相关API
@router.get("/{image_id}/annotations", response_model=List[AnnotationResponse])
async def get_image_annotations(image_id: int, db: Session = Depends(get_db)):
    """获取图片的标注"""
    # 先检查图片是否存在
    image = ImageCRUD.get_image(db, image_id)
    if not image:
        raise HTTPException(status_code=404, detail="图片不存在")
    
    annotations = AnnotationCRUD.get_annotations_by_image(db, image_id)
    return [AnnotationResponse.model_validate(ann.to_dict()) for ann in annotations]


@router.post("/{image_id}/annotations", response_model=AnnotationResponse)
async def create_annotation(
    image_id: int,
    annotation_data: AnnotationCreate,
    db: Session = Depends(get_db)
):
    """为图片创建标注"""
    # 先检查图片是否存在
    image = ImageCRUD.get_image(db, image_id)
    if not image:
        raise HTTPException(status_code=404, detail="图片不存在")
    
    annotation = AnnotationCRUD.create_annotation(
        db=db,
        image_id=image_id,
        **annotation_data.model_dump()
    )
    
    return AnnotationResponse.model_validate(annotation.to_dict())


@router.delete("/annotations/{annotation_id}", response_model=APIResponse)
async def delete_annotation(annotation_id: int, db: Session = Depends(get_db)):
    """删除标注"""
    success = AnnotationCRUD.delete_annotation(db, annotation_id)
    if not success:
        raise HTTPException(status_code=404, detail="标注不存在")
    
    return APIResponse(success=True, message="标注删除成功")
