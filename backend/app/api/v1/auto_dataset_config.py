"""
自动数据集配置API
管理AI生成图片的自动数据集配置
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, Any, Optional, List
import logging

from ...services.ai_generation.auto_dataset_manager import AutoDatasetManager

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/auto-dataset", tags=["auto-dataset"])

# 全局自动数据集管理器实例
auto_dataset_manager = AutoDatasetManager()

# Pydantic模型
class AutoDatasetConfigRequest(BaseModel):
    auto_add_to_dataset: Optional[bool] = None
    default_dataset_name: Optional[str] = None
    dataset_naming_format: Optional[str] = None  # "timestamp" 或 "sequential"
    create_daily_datasets: Optional[bool] = None
    max_images_per_dataset: Optional[int] = None
    auto_organize_by_target: Optional[bool] = None

class DatasetCleanupRequest(BaseModel):
    max_datasets: int = 10

@router.get("/config")
async def get_auto_dataset_config():
    """获取自动数据集配置"""
    try:
        config = auto_dataset_manager.get_config()
        return {
            "success": True,
            "data": config
        }
    except Exception as e:
        logger.error(f"获取自动数据集配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取配置失败: {str(e)}")

@router.post("/config")
async def update_auto_dataset_config(request: AutoDatasetConfigRequest):
    """更新自动数据集配置"""
    try:
        # 构建更新配置
        update_config = {}
        if request.auto_add_to_dataset is not None:
            update_config["auto_add_to_dataset"] = request.auto_add_to_dataset
        if request.default_dataset_name is not None:
            update_config["default_dataset_name"] = request.default_dataset_name
        if request.dataset_naming_format is not None:
            if request.dataset_naming_format not in ["timestamp", "sequential"]:
                raise HTTPException(status_code=400, detail="命名格式必须是 'timestamp' 或 'sequential'")
            update_config["dataset_naming_format"] = request.dataset_naming_format
        if request.create_daily_datasets is not None:
            update_config["create_daily_datasets"] = request.create_daily_datasets
        if request.max_images_per_dataset is not None:
            if request.max_images_per_dataset < 1:
                raise HTTPException(status_code=400, detail="每个数据集最大图片数必须大于0")
            update_config["max_images_per_dataset"] = request.max_images_per_dataset
        if request.auto_organize_by_target is not None:
            update_config["auto_organize_by_target"] = request.auto_organize_by_target
        
        # 更新配置
        success = auto_dataset_manager.update_config(update_config)
        
        if success:
            return {
                "success": True,
                "message": "配置更新成功",
                "data": auto_dataset_manager.get_config()
            }
        else:
            raise HTTPException(status_code=500, detail="配置更新失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新自动数据集配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新配置失败: {str(e)}")

@router.get("/datasets")
async def get_ai_generated_datasets():
    """获取所有AI生成的数据集"""
    try:
        datasets = auto_dataset_manager.get_ai_generated_datasets()
        return {
            "success": True,
            "data": {
                "datasets": datasets,
                "total_count": len(datasets)
            }
        }
    except Exception as e:
        logger.error(f"获取AI生成数据集失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取数据集失败: {str(e)}")

@router.post("/create-default")
async def create_default_dataset(military_target: Optional[str] = None):
    """创建或获取默认数据集"""
    try:
        dataset_folder = auto_dataset_manager.get_or_create_default_dataset(military_target)
        
        if dataset_folder:
            return {
                "success": True,
                "message": "默认数据集创建或获取成功",
                "data": {
                    "dataset_folder": dataset_folder,
                    "military_target": military_target
                }
            }
        else:
            raise HTTPException(status_code=500, detail="创建或获取默认数据集失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建默认数据集失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建数据集失败: {str(e)}")

@router.post("/cleanup")
async def cleanup_old_datasets(request: DatasetCleanupRequest):
    """清理旧的AI生成数据集"""
    try:
        success = auto_dataset_manager.cleanup_old_datasets(request.max_datasets)
        
        if success:
            return {
                "success": True,
                "message": f"数据集清理完成，保留最新的 {request.max_datasets} 个数据集"
            }
        else:
            raise HTTPException(status_code=500, detail="数据集清理失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"清理数据集失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清理失败: {str(e)}")

@router.get("/status")
async def get_auto_dataset_status():
    """获取自动数据集管理状态"""
    try:
        config = auto_dataset_manager.get_config()
        ai_datasets = auto_dataset_manager.get_ai_generated_datasets()
        
        # 计算统计信息
        total_datasets = len(ai_datasets)
        total_images = sum(dataset.get("total_images", 0) for dataset in ai_datasets)
        
        # 获取最新数据集信息
        latest_dataset = None
        if ai_datasets:
            latest_dataset = max(ai_datasets, key=lambda x: x.get("created_at", ""))
        
        status = {
            "enabled": config.get("auto_add_to_dataset", True),
            "default_dataset_name": config.get("default_dataset_name", "AI_Generated_Images"),
            "naming_format": config.get("dataset_naming_format", "timestamp"),
            "total_ai_datasets": total_datasets,
            "total_ai_images": total_images,
            "latest_dataset": latest_dataset,
            "config": config
        }
        
        return {
            "success": True,
            "data": status
        }
        
    except Exception as e:
        logger.error(f"获取自动数据集状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")

@router.post("/test")
async def test_auto_dataset_processing():
    """测试自动数据集处理功能"""
    try:
        # 创建测试数据
        test_generation_metadata = {
            "filename": "test_image.png",
            "path": "test/path/test_image.png",
            "size": 1024000,
            "hash": "test_hash",
            "created_at": "2025-06-28T14:00:00",
            "generation_type": "ai",
            "metadata": {
                "military_target": "坦克",
                "weather": "晴天",
                "scene": "沙漠"
            }
        }
        
        test_generation_params = {
            "military_target": "坦克",
            "weather": "晴天",
            "scene": "沙漠",
            "steps": 30,
            "cfg_scale": 7.5,
            "seed": 12345,
            "width": 512,
            "height": 512,
            "prompt": "a tank in desert, sunny weather",
            "negative_prompt": "blurry, low quality",
            "model": "stable-diffusion-v1-5"
        }
        
        # 测试元数据提取
        content_info = auto_dataset_manager._extract_content_info(
            test_generation_metadata, test_generation_params
        )
        
        ai_params = auto_dataset_manager._extract_ai_generation_params(
            test_generation_params
        )
        
        return {
            "success": True,
            "message": "自动数据集处理测试完成",
            "data": {
                "content_info": content_info,
                "ai_generation_params": ai_params,
                "test_metadata": test_generation_metadata,
                "test_params": test_generation_params
            }
        }
        
    except Exception as e:
        logger.error(f"测试自动数据集处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"测试失败: {str(e)}")

@router.get("/help")
async def get_auto_dataset_help():
    """获取自动数据集管理帮助信息"""
    help_info = {
        "description": "AI生成图片自动数据集管理功能",
        "features": [
            "自动创建默认数据集",
            "自动添加AI生成的图片到数据集",
            "标准化元数据提取和转换",
            "支持按军事目标分类",
            "支持按日期创建数据集",
            "自动清理旧数据集"
        ],
        "config_options": {
            "auto_add_to_dataset": "是否自动添加到数据集 (true/false)",
            "default_dataset_name": "默认数据集名称 (字符串)",
            "dataset_naming_format": "命名格式 (timestamp/sequential)",
            "create_daily_datasets": "是否按日期创建数据集 (true/false)",
            "max_images_per_dataset": "每个数据集最大图片数 (数字)",
            "auto_organize_by_target": "是否按军事目标自动分类 (true/false)"
        },
        "api_endpoints": {
            "GET /config": "获取配置",
            "POST /config": "更新配置",
            "GET /datasets": "获取AI生成的数据集",
            "POST /create-default": "创建默认数据集",
            "POST /cleanup": "清理旧数据集",
            "GET /status": "获取状态信息",
            "POST /test": "测试功能",
            "GET /help": "获取帮助信息"
        }
    }
    
    return {
        "success": True,
        "data": help_info
    }
