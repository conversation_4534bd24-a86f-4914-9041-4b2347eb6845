#!/usr/bin/env python3
"""
数据集管理API端点
提供数据集验证、清理和统计功能
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any, List
import logging

from ..services.common.strict_dataset_manager import StrictDatasetManager
from ..core.dependencies import get_strict_dataset_manager

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/dataset-management", tags=["dataset-management"])

@router.get("/validate", response_model=Dict[str, Any])
async def validate_all_datasets(
    dataset_manager: StrictDatasetManager = Depends(get_strict_dataset_manager)
):
    """验证所有数据集的完整性"""
    try:
        logger.info("开始验证所有数据集")
        validation_results = dataset_manager.validate_all_datasets()
        
        return {
            "success": True,
            "message": "数据集验证完成",
            "data": validation_results
        }
        
    except Exception as e:
        logger.error(f"数据集验证失败: {e}")
        raise HTTPException(status_code=500, detail=f"数据集验证失败: {str(e)}")

@router.post("/cleanup/invalid", response_model=Dict[str, Any])
async def cleanup_invalid_datasets(
    dataset_manager: StrictDatasetManager = Depends(get_strict_dataset_manager)
):
    """清理无效的数据集"""
    try:
        logger.info("开始清理无效数据集")
        cleanup_results = dataset_manager.cleanup_invalid_datasets()
        
        return {
            "success": True,
            "message": f"清理完成，删除了 {cleanup_results['total_cleaned']} 个无效数据集",
            "data": cleanup_results
        }
        
    except Exception as e:
        logger.error(f"清理无效数据集失败: {e}")
        raise HTTPException(status_code=500, detail=f"清理无效数据集失败: {str(e)}")

@router.post("/cleanup/orphaned-files", response_model=Dict[str, Any])
async def cleanup_orphaned_files(
    dataset_manager: StrictDatasetManager = Depends(get_strict_dataset_manager)
):
    """清理孤立的生成文件"""
    try:
        logger.info("开始清理孤立的生成文件")
        cleanup_results = dataset_manager.cleanup_orphaned_generated_files()
        
        return {
            "success": True,
            "message": f"清理完成，删除了 {cleanup_results['total_cleaned']} 个孤立文件",
            "data": cleanup_results
        }
        
    except Exception as e:
        logger.error(f"清理孤立文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"清理孤立文件失败: {str(e)}")

@router.get("/statistics", response_model=Dict[str, Any])
async def get_dataset_statistics(
    dataset_manager: StrictDatasetManager = Depends(get_strict_dataset_manager)
):
    """获取数据集统计信息"""
    try:
        logger.info("获取数据集统计信息")
        statistics = dataset_manager.get_dataset_statistics()
        
        return {
            "success": True,
            "message": "获取统计信息成功",
            "data": statistics
        }
        
    except Exception as e:
        logger.error(f"获取数据集统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取数据集统计信息失败: {str(e)}")

@router.post("/cleanup/all", response_model=Dict[str, Any])
async def cleanup_all_issues(
    dataset_manager: StrictDatasetManager = Depends(get_strict_dataset_manager)
):
    """清理所有数据集问题（无效数据集 + 孤立文件）"""
    try:
        logger.info("开始全面清理数据集问题")
        
        # 1. 清理无效数据集
        invalid_cleanup = dataset_manager.cleanup_invalid_datasets()
        
        # 2. 清理孤立文件
        orphaned_cleanup = dataset_manager.cleanup_orphaned_generated_files()
        
        # 3. 重新验证
        validation_results = dataset_manager.validate_all_datasets()
        
        total_cleaned = invalid_cleanup['total_cleaned'] + orphaned_cleanup['total_cleaned']
        
        return {
            "success": True,
            "message": f"全面清理完成，删除了 {total_cleaned} 个问题项",
            "data": {
                "invalid_datasets_cleanup": invalid_cleanup,
                "orphaned_files_cleanup": orphaned_cleanup,
                "final_validation": validation_results,
                "total_items_cleaned": total_cleaned
            }
        }
        
    except Exception as e:
        logger.error(f"全面清理失败: {e}")
        raise HTTPException(status_code=500, detail=f"全面清理失败: {str(e)}")

@router.get("/health", response_model=Dict[str, Any])
async def dataset_management_health():
    """数据集管理服务健康检查"""
    return {
        "success": True,
        "message": "数据集管理服务正常",
        "data": {
            "service": "dataset-management",
            "status": "healthy"
        }
    }
