"""
AI生成相关的API路由
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from fastapi.responses import FileResponse
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
import logging
import asyncio

from ..services.ai_generation.ai_service import AIGenerationService
from ..core.dependencies import get_ai_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/ai", tags=["AI生成"])

# Pydantic模型定义
class GenerationRequest(BaseModel):
    """图像生成请求"""
    military_target: str = Field(..., description="军事目标类型")
    weather: str = Field(..., description="天气条件")
    scene: str = Field(..., description="场景环境")
    num_images: int = Field(1, ge=1, le=10, description="生成图像数量")
    steps: int = Field(30, ge=10, le=100, description="采样步数")
    cfg_scale: float = Field(7.5, ge=1.0, le=20.0, description="CFG引导强度")
    seed: int = Field(-1, description="随机种子，-1表示随机")
    width: int = Field(512, ge=256, le=1024, description="图像宽度")
    height: int = Field(512, ge=256, le=1024, description="图像高度")
    scheduler_name: str = Field("DPM++ 2M Karras", description="采样器名称")
    custom_prompt: str = Field("", description="自定义提示词")
    style_strength: float = Field(0.7, ge=0.0, le=1.0, description="风格强度")
    technical_detail: float = Field(0.8, ge=0.0, le=1.0, description="技术细节程度")
    target_size_ratio: float = Field(0.10, ge=0.03, le=0.20, description="目标尺寸比例")
    save_images: bool = Field(True, description="是否保存图像")
    generate_annotations: bool = Field(True, description="是否生成标注")
    # GSA检测参数
    enable_gsa_detection: bool = Field(False, description="是否启用GSA检测")
    gsa_confidence: float = Field(0.25, ge=0.1, le=1.0, description="GSA检测置信度阈值")
    gsa_nms_threshold: float = Field(0.4, ge=0.1, le=1.0, description="GSA NMS阈值")
    gsa_save_original: bool = Field(True, description="是否保存原始图片")
    gsa_save_annotated: bool = Field(True, description="是否保存标注图片")
    # 新增优化参数
    gsa_enable_multi_scale: bool = Field(True, description="是否启用多尺度检测")
    gsa_enable_enhancement: bool = Field(True, description="是否启用图像增强")
    gsa_preset: str = Field("enhanced_detection", description="GSA预设配置")
    # 模型信息参数（可选，用于日志记录）
    model_info: Optional[Dict[str, Any]] = Field(None, description="模型信息，用于日志记录")

class PromptBuildRequest(BaseModel):
    """提示词构建请求"""
    military_target: str = Field(..., description="军事目标类型")
    weather: str = Field(..., description="天气条件")
    scene: str = Field(..., description="场景环境")
    custom_prompt: str = Field("", description="自定义提示词")
    style_strength: float = Field(0.7, ge=0.0, le=1.0, description="风格强度")
    technical_detail: float = Field(0.8, ge=0.0, le=1.0, description="技术细节程度")

class BatchGenerationRequest(BaseModel):
    """批量生成请求"""
    configs: List[GenerationRequest] = Field(..., description="生成配置列表")

class GenerationResponse(BaseModel):
    """生成响应"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None

# API路由
@router.post("/generate", response_model=GenerationResponse)
async def generate_images(
    request: GenerationRequest,
    ai_service: AIGenerationService = Depends(get_ai_service)
):
    """
    生成图像
    """
    try:
        logger.info(f"收到图像生成请求: {request.military_target}, {request.weather}, {request.scene}")
        
        # 检查AI服务是否可用
        if not ai_service.is_model_loaded:
            logger.warning("AI模型未加载，尝试重新初始化...")
            try:
                await ai_service.initialize()
            except Exception as e:
                logger.error(f"AI模型初始化失败: {str(e)}")
                raise HTTPException(
                    status_code=503, 
                    detail="AI生成服务暂时不可用，请检查模型配置或使用传统生成模式"
                )
        
        # 记录模型信息（如果提供）
        if request.model_info:
            logger.info(f"使用模型信息: {request.model_info}")

        result = await ai_service.generate_images(
            military_target=request.military_target,
            weather=request.weather,
            scene=request.scene,
            num_images=request.num_images,
            steps=request.steps,
            cfg_scale=request.cfg_scale,
            seed=request.seed,
            width=request.width,
            height=request.height,
            scheduler_name=request.scheduler_name,
            custom_prompt=request.custom_prompt,
            style_strength=request.style_strength,
            technical_detail=request.technical_detail,
            target_size_ratio=request.target_size_ratio,
            save_images=request.save_images,
            generate_annotations=request.generate_annotations,
            # GSA检测参数
            enable_gsa_detection=request.enable_gsa_detection,
            gsa_confidence=request.gsa_confidence,
            gsa_nms_threshold=request.gsa_nms_threshold,
            gsa_save_original=request.gsa_save_original,
            gsa_save_annotated=request.gsa_save_annotated
        )
        
        return GenerationResponse(
            success=True,
            message=f"成功生成 {len(result['images'])} 张图像",
            data=result
        )
        
    except Exception as e:
        logger.error(f"图像生成失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"图像生成失败: {str(e)}")

@router.post("/build-prompt")
async def build_prompt(
    request: PromptBuildRequest,
    ai_service: AIGenerationService = Depends(get_ai_service)
):
    """
    构建提示词
    """
    try:
        positive_prompt, negative_prompt = ai_service.build_prompt_from_selection(
            military_target=request.military_target,
            weather=request.weather,
            scene=request.scene,
            custom_prompt=request.custom_prompt,
            style_strength=request.style_strength,
            technical_detail=request.technical_detail
        )
        
        return {
            "success": True,
            "data": {
                "positive_prompt": positive_prompt,
                "negative_prompt": negative_prompt
            }
        }
        
    except Exception as e:
        logger.error(f"提示词构建失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"提示词构建失败: {str(e)}")

@router.get("/prompt-suggestions/{military_target}")
async def get_prompt_suggestions(
    military_target: str,
    ai_service: AIGenerationService = Depends(get_ai_service)
):
    """
    获取提示词建议
    """
    try:
        suggestions = ai_service.get_prompt_suggestions(military_target)
        
        return {
            "success": True,
            "data": suggestions
        }
        
    except Exception as e:
        logger.error(f"获取提示词建议失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取提示词建议失败: {str(e)}")

@router.post("/optimize-prompt")
async def optimize_prompt(
    prompt: str,
    ai_service: AIGenerationService = Depends(get_ai_service)
):
    """
    优化提示词
    """
    try:
        optimized_prompt = ai_service.optimize_prompt(prompt)
        
        return {
            "success": True,
            "data": {
                "original_prompt": prompt,
                "optimized_prompt": optimized_prompt
            }
        }
        
    except Exception as e:
        logger.error(f"提示词优化失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"提示词优化失败: {str(e)}")

@router.post("/batch-generate", response_model=GenerationResponse)
async def batch_generate(
    request: BatchGenerationRequest,
    background_tasks: BackgroundTasks,
    ai_service: AIGenerationService = Depends(get_ai_service)
):
    """
    批量生成图像
    """
    try:
        logger.info(f"收到批量生成请求，共 {len(request.configs)} 个配置")
        
        # 将批量生成任务添加到后台任务
        task_id = f"batch_{len(request.configs)}_{asyncio.get_event_loop().time()}"
        
        # 这里可以集成Celery来处理长时间运行的任务
        # 现在先直接执行
        results = await ai_service.batch_generate(
            generation_configs=[config.dict() for config in request.configs]
        )
        
        return GenerationResponse(
            success=True,
            message=f"批量生成完成，共处理 {len(results)} 个任务",
            data={
                "task_id": task_id,
                "results": results,
                "total_configs": len(request.configs),
                "successful_count": sum(1 for r in results if r.get('success', True))
            }
        )
        
    except Exception as e:
        logger.error(f"批量生成失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批量生成失败: {str(e)}")

@router.get("/model-info")
async def get_model_info(
    ai_service: AIGenerationService = Depends(get_ai_service)
):
    """
    获取模型信息
    """
    try:
        info = ai_service.get_model_info()
        
        return {
            "success": True,
            "data": info
        }
        
    except Exception as e:
        logger.error(f"获取模型信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取模型信息失败: {str(e)}")

@router.get("/generation-history")
async def get_generation_history(
    limit: int = 50,
    ai_service: AIGenerationService = Depends(get_ai_service)
):
    """
    获取生成历史
    """
    try:
        history = ai_service.get_generation_history(limit=limit)
        
        return {
            "success": True,
            "data": {
                "history": history,
                "total_count": len(history)
            }
        }
        
    except Exception as e:
        logger.error(f"获取生成历史失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取生成历史失败: {str(e)}")

@router.delete("/generation-history")
async def clear_generation_history(
    ai_service: AIGenerationService = Depends(get_ai_service)
):
    """
    清空生成历史
    """
    try:
        ai_service.clear_generation_history()
        
        return {
            "success": True,
            "message": "生成历史已清空"
        }
        
    except Exception as e:
        logger.error(f"清空生成历史失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清空生成历史失败: {str(e)}")

@router.post("/initialize")
async def initialize_service(
    ai_service: AIGenerationService = Depends(get_ai_service)
):
    """
    初始化AI生成服务
    """
    try:
        logger.info("开始初始化AI生成服务...")
        
        success = await ai_service.initialize()
        
        if success:
            return {
                "success": True,
                "message": "AI生成服务初始化成功"
            }
        else:
            return {
                "success": False,
                "message": "AI生成服务初始化失败，请检查模型配置"
            }
            
    except Exception as e:
        logger.error(f"AI生成服务初始化失败: {str(e)}")
        return {
            "success": False,
            "message": f"AI生成服务初始化失败: {str(e)}"
        }

@router.get("/status")
async def get_service_status(
    ai_service: AIGenerationService = Depends(get_ai_service)
):
    """
    获取服务状态
    """
    try:
        info = ai_service.get_model_info()
        
        return {
            "success": True,
            "data": {
                "service_status": "running" if info.get("service_initialized") else "stopped",
                "model_loaded": info.get("is_loaded", False),
                "device": info.get("device"),
                "current_scheduler": info.get("current_scheduler"),
                "generation_count": info.get("generation_count", 0),
                "available_options": {
                    "targets": info.get("available_targets", []),
                    "weather": info.get("available_weather", []),
                    "scenes": info.get("available_scenes", []),
                    "schedulers": info.get("available_schedulers", [])
                }
            }
        }
        
    except Exception as e:
        logger.error(f"获取服务状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取服务状态失败: {str(e)}")

@router.get("/download/{generation_id}/{image_index}")
async def download_generated_image(
    generation_id: str,
    image_index: int,
    ai_service: AIGenerationService = Depends(get_ai_service)
):
    """
    下载生成的图像
    """
    try:
        # 这里需要根据generation_id和image_index找到对应的文件
        # 简化实现，实际应该从数据库或文件系统中查找
        filename = f"{generation_id}_{image_index:03d}.png"
        file_path = f"data/generated/ai_generated/{filename}"
        
        return FileResponse(
            path=file_path,
            filename=filename,
            media_type="image/png"
        )
        
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="图像文件未找到")
    except Exception as e:
        logger.error(f"下载图像失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"下载图像失败: {str(e)}")

# 预设配置相关的路由
@router.get("/presets")
async def get_generation_presets():
    """
    获取生成预设配置
    """
    presets = {
        "高质量": {
            "steps": 50,
            "cfg_scale": 8.0,
            "scheduler_name": "DPM++ 2M Karras",
            "style_strength": 0.8,
            "technical_detail": 0.9
        },
        "快速生成": {
            "steps": 20,
            "cfg_scale": 7.0,
            "scheduler_name": "Euler a",
            "style_strength": 0.6,
            "technical_detail": 0.7
        },
        "平衡模式": {
            "steps": 30,
            "cfg_scale": 7.5,
            "scheduler_name": "DPM++ 2M Karras",
            "style_strength": 0.7,
            "technical_detail": 0.8
        },
        "艺术风格": {
            "steps": 40,
            "cfg_scale": 9.0,
            "scheduler_name": "Heun",
            "style_strength": 0.9,
            "technical_detail": 0.6
        }
    }
    
    return {
        "success": True,
        "data": presets
    }

@router.get("/schedulers")
async def get_available_schedulers(
    ai_service: AIGenerationService = Depends(get_ai_service)
):
    """
    获取可用的采样器列表
    """
    try:
        info = ai_service.get_model_info()
        schedulers = info.get("available_schedulers", [])
        
        return {
            "success": True,
            "data": {
                "schedulers": schedulers,
                "current": info.get("current_scheduler"),
                "descriptions": {
                    "DPM++ 2M Karras": "高质量，适合大多数场景",
                    "Euler a": "快速生成，适合预览",
                    "Euler": "稳定生成，适合批量处理",
                    "LMS": "经典算法，兼容性好",
                    "Heun": "高精度，适合艺术创作",
                    "DPM2": "平衡速度和质量",
                    "DPM2 a": "改进版DPM2",
                    "DPM++ 2S a": "单步优化版本",
                    "DDIM": "确定性采样，可重现结果"
                }
            }
        }
        
    except Exception as e:
        logger.error(f"获取采样器列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取采样器列表失败: {str(e)}")

@router.get("/models")
async def get_available_models(
    ai_service: AIGenerationService = Depends(get_ai_service)
):
    """
    获取可用的AI模型列表
    """
    try:
        models_info = ai_service.get_available_models()

        return {
            "success": True,
            "data": models_info
        }

    except Exception as e:
        logger.error(f"获取模型列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取模型列表失败: {str(e)}")

@router.post("/switch-model")
async def switch_model(
    model_key: str,
    ai_service: AIGenerationService = Depends(get_ai_service)
):
    """
    切换AI模型
    """
    try:
        success = ai_service.switch_model(model_key)

        if success:
            return {
                "success": True,
                "message": f"成功切换到模型: {model_key}",
                "data": ai_service.get_available_models()
            }
        else:
            raise HTTPException(status_code=400, detail=f"模型切换失败: {model_key}")

    except Exception as e:
        logger.error(f"模型切换失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"模型切换失败: {str(e)}")

@router.post("/preload-model")
async def preload_model(
    model_key: str,
    ai_service: AIGenerationService = Depends(get_ai_service)
):
    """
    预加载AI模型到缓存中，不切换当前模型
    """
    try:
        success = ai_service.preload_model(model_key)

        if success:
            return {
                "success": True,
                "message": f"成功预加载模型: {model_key}",
                "data": {
                    "preloaded_model": model_key,
                    "cached_models": ai_service.get_cached_models(),
                    "current_model": ai_service.get_available_models().get('current_model', {})
                }
            }
        else:
            raise HTTPException(status_code=400, detail=f"模型预加载失败: {model_key}")

    except Exception as e:
        logger.error(f"模型预加载失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"模型预加载失败: {str(e)}")

@router.get("/cached-models")
async def get_cached_models(
    ai_service: AIGenerationService = Depends(get_ai_service)
):
    """
    获取已缓存的模型列表
    """
    try:
        cached_models = ai_service.get_cached_models()

        return {
            "success": True,
            "data": {
                "cached_models": cached_models,
                "cache_count": len(cached_models),
                "current_model": ai_service.get_available_models().get('current_model', {})
            }
        }

    except Exception as e:
        logger.error(f"获取缓存模型列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取缓存模型列表失败: {str(e)}")

@router.post("/analyze-prompt")
async def analyze_prompt(
    prompt: str,
    ai_service: AIGenerationService = Depends(get_ai_service)
):
    """
    分析提示词对小目标生成的有效性
    """
    try:
        analysis = ai_service.analyze_prompt_for_small_targets(prompt)

        return {
            "success": True,
            "data": analysis
        }

    except Exception as e:
        logger.error(f"提示词分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"提示词分析失败: {str(e)}")

@router.get("/optimal-params")
async def get_optimal_params(
    ai_service: AIGenerationService = Depends(get_ai_service)
):
    """
    获取当前模型的最优生成参数
    """
    try:
        params = ai_service.get_optimal_generation_params()

        return {
            "success": True,
            "data": params
        }

    except Exception as e:
        logger.error(f"获取最优参数失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取最优参数失败: {str(e)}")

@router.post("/set-proxy")
async def set_proxy_port(
    port: int,
    ai_service: AIGenerationService = Depends(get_ai_service)
):
    """
    设置模型下载代理端口
    """
    try:
        success = ai_service.set_proxy_port(port)

        if success:
            return {
                "success": True,
                "message": f"代理端口已设置为: {port}",
                "data": {
                    "proxy_port": port,
                    "proxy_enabled": port > 0
                }
            }
        else:
            raise HTTPException(status_code=400, detail=f"设置代理端口失败: {port}")

    except Exception as e:
        logger.error(f"设置代理端口失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"设置代理端口失败: {str(e)}")

@router.get("/proxy-status")
async def get_proxy_status(
    ai_service: AIGenerationService = Depends(get_ai_service)
):
    """
    获取当前代理配置状态
    """
    try:
        proxy_info = ai_service.get_proxy_status()

        return {
            "success": True,
            "data": proxy_info
        }

    except Exception as e:
        logger.error(f"获取代理状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取代理状态失败: {str(e)}")

@router.get("/model-download-status/{model_key}")
async def get_model_download_status(model_key: str):
    """获取模型下载状态"""
    try:
        # 检查本地模型文件
        from pathlib import Path
        model_dir = Path("models") / model_key
        
        status = {
            "model_key": model_key,
            "local_exists": False,
            "files_found": [],
            "download_required": True,
            "setup_required": False
        }
        
        if model_dir.exists():
            safetensors_files = list(model_dir.glob("*.safetensors"))
            if safetensors_files:
                status["local_exists"] = True
                status["files_found"] = [f.name for f in safetensors_files]
                status["download_required"] = False
                
                # 检查是否需要设置配置文件
                config_files = [
                    "model_index.json",
                    "scheduler/scheduler_config.json",
                    "tokenizer/tokenizer_config.json"
                ]
                
                missing_configs = []
                for config_file in config_files:
                    if not (model_dir / config_file).exists():
                        missing_configs.append(config_file)
                
                if missing_configs:
                    status["setup_required"] = True
                    status["missing_configs"] = missing_configs
        
        return status
        
    except Exception as e:
        logger.error(f"获取模型下载状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取模型状态失败: {str(e)}")

@router.post("/setup-model-offline/{model_key}")
async def setup_model_offline(model_key: str):
    """设置模型的离线配置文件"""
    try:
        from pathlib import Path
        import json
        
        model_dir = Path("models") / model_key
        model_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建标准diffusers目录结构
        subdirs = [
            "scheduler",
            "tokenizer", 
            "text_encoder",
            "unet",
            "vae",
            "feature_extractor",
            "safety_checker"
        ]
        
        for subdir in subdirs:
            (model_dir / subdir).mkdir(exist_ok=True)
        
        # 基础配置文件
        config_files = {
            "model_index.json": {
                "_class_name": "StableDiffusionPipeline",
                "_diffusers_version": "0.21.4",
                "feature_extractor": ["transformers", "CLIPImageProcessor"],
                "safety_checker": ["stable_diffusion", "StableDiffusionSafetyChecker"],
                "scheduler": ["diffusers", "PNDMScheduler"],
                "text_encoder": ["transformers", "CLIPTextModel"],
                "tokenizer": ["transformers", "CLIPTokenizer"],
                "unet": ["diffusers", "UNet2DConditionModel"],
                "vae": ["diffusers", "AutoencoderKL"]
            },
            "scheduler/scheduler_config.json": {
                "_class_name": "PNDMScheduler",
                "_diffusers_version": "0.21.4",
                "beta_end": 0.012,
                "beta_schedule": "scaled_linear",
                "beta_start": 0.00085,
                "num_train_timesteps": 1000,
                "set_alpha_to_one": False,
                "skip_prk_steps": True,
                "steps_offset": 1,
                "trained_betas": None
            },
            "tokenizer/tokenizer_config.json": {
                "add_prefix_space": False,
                "bos_token": "<|startoftext|>",
                "do_lower_case": True,
                "eos_token": "<|endoftext|>",
                "model_max_length": 77,
                "name_or_path": "openai/clip-vit-large-patch14",
                "pad_token": "<|endoftext|>",
                "tokenizer_class": "CLIPTokenizer",
                "unk_token": "<|endoftext|>"
            },
            "tokenizer/special_tokens_map.json": {
                "bos_token": "<|startoftext|>",
                "eos_token": "<|endoftext|>",
                "pad_token": "<|endoftext|>",
                "unk_token": "<|endoftext|>"
            },
            "text_encoder/config.json": {
                "_name_or_path": "openai/clip-vit-large-patch14",
                "architectures": ["CLIPTextModel"],
                "attention_dropout": 0.0,
                "bos_token_id": 0,
                "dropout": 0.0,
                "eos_token_id": 2,
                "hidden_act": "quick_gelu",
                "hidden_size": 768,
                "initializer_factor": 1.0,
                "initializer_range": 0.02,
                "intermediate_size": 3072,
                "layer_norm_eps": 1e-05,
                "max_position_embeddings": 77,
                "model_type": "clip_text_model",
                "num_attention_heads": 12,
                "num_hidden_layers": 12,
                "pad_token_id": 1,
                "projection_dim": 768,
                "torch_dtype": "float32",
                "transformers_version": "4.21.0.dev0",
                "vocab_size": 49408
            },
            "unet/config.json": {
                "_class_name": "UNet2DConditionModel",
                "_diffusers_version": "0.21.4",
                "act_fn": "silu",
                "attention_head_dim": 8,
                "block_out_channels": [320, 640, 1280, 1280],
                "center_input_sample": False,
                "cross_attention_dim": 768,
                "down_block_types": ["CrossAttnDownBlock2D", "CrossAttnDownBlock2D", "CrossAttnDownBlock2D", "DownBlock2D"],
                "downsample_padding": 1,
                "flip_sin_to_cos": True,
                "freq_shift": 0,
                "in_channels": 4,
                "layers_per_block": 2,
                "mid_block_scale_factor": 1,
                "norm_eps": 1e-05,
                "norm_num_groups": 32,
                "out_channels": 4,
                "sample_size": 64,
                "up_block_types": ["UpBlock2D", "CrossAttnUpBlock2D", "CrossAttnUpBlock2D", "CrossAttnUpBlock2D"]
            },
            "vae/config.json": {
                "_class_name": "AutoencoderKL",
                "_diffusers_version": "0.21.4",
                "act_fn": "silu",
                "block_out_channels": [128, 256, 512, 512],
                "down_block_types": ["DownEncoderBlock2D", "DownEncoderBlock2D", "DownEncoderBlock2D", "DownEncoderBlock2D"],
                "in_channels": 3,
                "latent_channels": 4,
                "layers_per_block": 2,
                "norm_num_groups": 32,
                "out_channels": 3,
                "sample_size": 512,
                "up_block_types": ["UpDecoderBlock2D", "UpDecoderBlock2D", "UpDecoderBlock2D", "UpDecoderBlock2D"]
            },
            "feature_extractor/preprocessor_config.json": {
                "crop_size": 224,
                "do_center_crop": True,
                "do_convert_rgb": True,
                "do_normalize": True,
                "do_resize": True,
                "feature_extractor_type": "CLIPImageProcessor",
                "image_mean": [0.48145466, 0.4578275, 0.40821073],
                "image_std": [0.26862954, 0.26130258, 0.27577711],
                "resample": 3,
                "size": 224
            },
            "safety_checker/config.json": {
                "_name_or_path": "CompVis/stable-diffusion-safety-checker",
                "architectures": ["StableDiffusionSafetyChecker"],
                "model_type": "clip_vision_model",
                "torch_dtype": "float32",
                "transformers_version": "4.21.0.dev0"
            }
        }
        
        created_files = []
        for file_path, config in config_files.items():
            full_path = model_dir / file_path
            full_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(full_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2)
            
            created_files.append(file_path)
        
        return {
            "success": True,
            "message": f"已为 {model_key} 创建离线配置文件",
            "created_files": created_files,
            "model_dir": str(model_dir)
        }
        
    except Exception as e:
        logger.error(f"设置模型离线配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"设置失败: {str(e)}")

@router.get("/model-download-info/{model_key}")
async def get_model_download_info(model_key: str):
    """获取模型下载信息和指导"""
    try:
        download_info = {
            "realistic-vision-v6": {
                "model_name": "Realistic Vision V6.0 B1",
                "description": "最新版本现实主义视觉模型，极小目标生成效果更佳",
                "file_info": {
                    "main_file": "Realistic_Vision_V6.0_NV_B1.safetensors",
                    "file_size": "4.27 GB",
                    "alternative_file": "Realistic_Vision_V6.0_NV_B1_fp16.safetensors",
                    "alternative_size": "2.13 GB"
                },
                "download_sources": [
                    {
                        "name": "Hugging Face",
                        "url": "https://huggingface.co/SG161222/Realistic_Vision_V6.0_B1_noVAE",
                        "instructions": "访问页面，点击 'Files and versions'，下载 .safetensors 文件"
                    },
                    {
                        "name": "Civitai",
                        "url": "https://civitai.com/models/4201/realistic-vision-v60-b1",
                        "instructions": "注册账号后下载模型文件"
                    }
                ],
                "local_path": "models/realistic-vision-v6/",
                "setup_instructions": [
                    "1. 创建目录: models/realistic-vision-v6/",
                    "2. 将下载的 .safetensors 文件放入该目录",
                    "3. 点击 '设置离线配置' 按钮",
                    "4. 尝试加载模型"
                ]
            },
            "stable-diffusion-v1-5": {
                "model_name": "Stable Diffusion v1.5",
                "description": "标准SD1.5模型，平衡质量和速度",
                "file_info": {
                    "note": "此模型通常会自动下载，无需手动操作"
                },
                "download_sources": [
                    {
                        "name": "Hugging Face",
                        "url": "https://huggingface.co/runwayml/stable-diffusion-v1-5",
                        "instructions": "系统会自动下载，如遇网络问题请检查代理设置"
                    }
                ]
            }
        }
        
        if model_key not in download_info:
            return {
                "model_key": model_key,
                "message": "暂无此模型的下载信息",
                "auto_download": True
            }
        
        return download_info[model_key]
        
    except Exception as e:
        logger.error(f"获取模型下载信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取下载信息失败: {str(e)}")