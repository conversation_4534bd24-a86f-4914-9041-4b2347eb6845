# -*- coding: utf-8 -*-
"""
编码配置模块 - 统一处理UTF-8编码设置
"""

import sys
import os
import logging


def setup_utf8_encoding():
    """设置UTF-8编码环境"""
    # 设置环境变量
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'
    
    # Windows系统特殊处理
    if sys.platform.startswith('win'):
        # 设置控制台编码
        if hasattr(sys.stdout, 'reconfigure'):
            sys.stdout.reconfigure(encoding='utf-8')
        if hasattr(sys.stderr, 'reconfigure'):
            sys.stderr.reconfigure(encoding='utf-8')
        
        # 设置Windows控制台代码页
        try:
            import subprocess
            subprocess.run(['chcp', '65001'], 
                         shell=True, 
                         capture_output=True, 
                         check=False)
        except Exception:
            pass  # 忽略设置失败


def setup_logging_encoding():
    """设置日志编码"""
    if sys.platform.startswith('win'):
        # 为所有现有的日志处理器设置UTF-8编码
        for handler in logging.root.handlers:
            if hasattr(handler, 'stream') and hasattr(handler.stream, 'reconfigure'):
                try:
                    handler.stream.reconfigure(encoding='utf-8')
                except Exception:
                    pass  # 忽略设置失败


def get_safe_encoding():
    """获取安全的编码格式"""
    return 'utf-8'


# 在模块导入时自动设置编码
setup_utf8_encoding() 