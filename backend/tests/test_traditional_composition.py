"""
传统图像合成功能测试
"""
import pytest
import numpy as np
from PIL import Image
import tempfile
import os
from unittest.mock import Mock, patch

from app.services.traditional.core_types import (
    ImageData, MattingConfig, BlendConfig, CompositionConfig,
    MattingMethod, BlendMode, WeatherEffect, CompositionRequest
)
from app.services.traditional.mask_compositor import (
    GrabCutMatting, WatershedMatting, ThresholdMatting, MattingFactory
)
from app.services.traditional.image_processor import AdvancedBlendProcessor
from app.services.traditional.lighting_processor import LightingProcessor
from app.services.traditional.composition_service import TraditionalCompositionService


class TestCoreTypes:
    """核心数据类型测试"""
    
    def test_image_data_creation(self):
        """测试ImageData创建"""
        image = Image.new('RGB', (100, 100), color='red')
        image_data = ImageData(image=image)
        
        assert image_data.image is not None
        assert image_data.mask is None
        assert image_data.metadata == {}
    
    def test_matting_config_defaults(self):
        """测试抠图配置默认值"""
        config = MattingConfig()
        
        assert config.method == MattingMethod.GRABCUT
        assert config.iterations == 5
        assert config.margin == 10
        assert config.feather_radius == 3
        assert config.edge_smooth is True
        assert config.auto_refine is True
    
    def test_blend_config_defaults(self):
        """测试混合配置默认值"""
        config = BlendConfig()
        
        assert config.mode == BlendMode.NORMAL
        assert config.opacity == 1.0
        assert config.color_match is True
        assert config.lighting_adjust is True
        assert config.shadow_generate is True


class TestMattingProcessors:
    """抠图处理器测试"""
    
    @pytest.fixture
    def test_image(self):
        """创建测试图像"""
        image = Image.new('RGB', (200, 200), color='white')
        # 在中心绘制一个红色矩形
        pixels = np.array(image)
        pixels[50:150, 50:150] = [255, 0, 0]  # 红色区域
        return ImageData(image=Image.fromarray(pixels))
    
    def test_grabcut_matting(self, test_image):
        """测试GrabCut抠图"""
        config = MattingConfig(method=MattingMethod.GRABCUT, iterations=3)
        processor = GrabCutMatting(config)
        
        result = processor.extract_foreground(test_image)
        
        assert result.success is True
        assert result.mask is not None
        assert result.mask.shape == (200, 200)
        assert result.processing_time > 0
    
    def test_threshold_matting(self, test_image):
        """测试阈值抠图"""
        config = MattingConfig(method=MattingMethod.THRESHOLD)
        processor = ThresholdMatting(config)
        
        result = processor.extract_foreground(test_image)
        
        assert result.success is True
        assert result.mask is not None
        assert result.mask.shape == (200, 200)
    
    def test_matting_factory(self):
        """测试抠图工厂"""
        config = MattingConfig(method=MattingMethod.GRABCUT)
        processor = MattingFactory.create_matting_processor(config)
        
        assert isinstance(processor, GrabCutMatting)
        
        # 测试不支持的方法
        with pytest.raises(ValueError):
            invalid_config = MattingConfig()
            invalid_config.method = "invalid_method"
            MattingFactory.create_matting_processor(invalid_config)


class TestBlendProcessor:
    """混合处理器测试"""
    
    @pytest.fixture
    def test_images(self):
        """创建测试图像"""
        bg = Image.new('RGB', (100, 100), color='blue')
        fg = Image.new('RGB', (100, 100), color='red')
        mask = np.ones((100, 100), dtype=np.uint8) * 255
        
        return ImageData(image=bg), ImageData(image=fg), mask
    
    def test_normal_blend(self, test_images):
        """测试正常混合"""
        bg_data, fg_data, mask = test_images
        config = BlendConfig(mode=BlendMode.NORMAL, opacity=1.0)
        processor = AdvancedBlendProcessor(config)
        
        result = processor.blend_layers(bg_data, fg_data, mask)
        
        assert result.success is True
        assert result.image is not None
        assert result.processing_time > 0
    
    def test_multiply_blend(self, test_images):
        """测试正片叠底混合"""
        bg_data, fg_data, mask = test_images
        config = BlendConfig(mode=BlendMode.MULTIPLY, opacity=0.8)
        processor = AdvancedBlendProcessor(config)
        
        result = processor.blend_layers(bg_data, fg_data, mask)
        
        assert result.success is True
        assert result.image is not None
    
    def test_color_matching(self, test_images):
        """测试色彩匹配"""
        bg_data, fg_data, mask = test_images
        config = BlendConfig(color_match=True)
        processor = AdvancedBlendProcessor(config)
        
        # 测试色彩匹配功能
        fg_array = np.array(fg_data.image)
        bg_array = np.array(bg_data.image)
        
        matched = processor.match_colors(fg_array, bg_array, mask)
        
        assert matched is not None
        assert matched.shape == fg_array.shape


class TestLightingProcessor:
    """光照处理器测试"""
    
    @pytest.fixture
    def test_image(self):
        """创建测试图像"""
        image = Image.new('RGB', (100, 100), color='gray')
        return ImageData(image=image)
    
    def test_weather_effects(self, test_image):
        """测试天气效果"""
        processor = LightingProcessor()
        
        # 测试各种天气效果
        weather_effects = [
            WeatherEffect.SUNNY,
            WeatherEffect.RAINY,
            WeatherEffect.SNOWY,
            WeatherEffect.FOGGY,
            WeatherEffect.NIGHT
        ]
        
        for weather in weather_effects:
            result = processor.apply_weather_effect(test_image, weather)
            assert result.success is True
            assert result.image is not None
    
    def test_lighting_adjustment(self, test_image):
        """测试光照调整"""
        processor = LightingProcessor()
        
        lighting_params = {
            'brightness': 10.0,
            'contrast': 1.2,
            'gamma': 0.9,
            'saturation': 1.1
        }
        
        result = processor.adjust_lighting(test_image, lighting_params)
        
        assert result.success is True
        assert result.image is not None
        assert result.metadata['lighting_params'] == lighting_params


class TestCompositionService:
    """合成服务测试"""
    
    @pytest.fixture
    def temp_images(self):
        """创建临时测试图像"""
        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        
        # 创建背景图像
        bg_image = Image.new('RGB', (200, 200), color='blue')
        bg_path = os.path.join(temp_dir, 'background.jpg')
        bg_image.save(bg_path)
        
        # 创建目标图像
        fg_image = Image.new('RGB', (100, 100), color='red')
        fg_path = os.path.join(temp_dir, 'target.jpg')
        fg_image.save(fg_path)
        
        yield bg_path, fg_path
        
        # 清理
        os.unlink(bg_path)
        os.unlink(fg_path)
        os.rmdir(temp_dir)
    
    @pytest.mark.asyncio
    async def test_service_initialization(self):
        """测试服务初始化"""
        service = TraditionalCompositionService()
        success = await service.initialize()
        
        assert success is True
        
        await service.cleanup()
    
    @pytest.mark.asyncio
    async def test_image_composition(self, temp_images):
        """测试图像合成"""
        bg_path, fg_path = temp_images
        
        service = TraditionalCompositionService()
        await service.initialize()
        
        # 创建合成请求
        request = CompositionRequest(
            background_path=bg_path,
            target_path=fg_path,
            weather_effect=WeatherEffect.SUNNY,
            military_target="tank",
            scene_type="urban",
            matting_config=MattingConfig(),
            blend_config=BlendConfig(),
            composition_config=CompositionConfig()
        )
        
        result = await service.compose_image(request)
        
        assert result.success is True
        assert result.image is not None
        assert result.processing_time > 0
        
        await service.cleanup()
    
    @pytest.mark.asyncio
    async def test_generate_images_interface(self):
        """测试生成图像接口"""
        service = TraditionalCompositionService()
        await service.initialize()
        
        # 模拟素材选择
        with patch.object(service, '_select_background', return_value='test_bg.jpg'), \
             patch.object(service, '_select_target', return_value='test_target.jpg'), \
             patch.object(service, 'compose_image') as mock_compose:
            
            # 模拟合成结果
            mock_result = Mock()
            mock_result.success = True
            mock_result.image = Mock()
            mock_result.processing_time = 1.0
            mock_result.metadata = {}
            mock_compose.return_value = mock_result
            
            result = await service.generate_images(
                military_target="tank",
                weather="sunny",
                scene="urban",
                num_images=1
            )
            
            assert result['success'] is True
            assert result['total_generated'] == 1
            assert len(result['results']) == 1
        
        await service.cleanup()


class TestIntegration:
    """集成测试"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_composition(self):
        """端到端合成测试"""
        # 创建测试图像
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建背景图像
            bg_image = Image.new('RGB', (300, 300), color='lightblue')
            bg_path = os.path.join(temp_dir, 'background.jpg')
            bg_image.save(bg_path)
            
            # 创建目标图像（中心有一个深色矩形）
            fg_image = Image.new('RGB', (200, 200), color='white')
            fg_pixels = np.array(fg_image)
            fg_pixels[50:150, 50:150] = [50, 50, 50]  # 深色区域
            fg_image = Image.fromarray(fg_pixels)
            fg_path = os.path.join(temp_dir, 'target.jpg')
            fg_image.save(fg_path)
            
            # 输出路径
            output_path = os.path.join(temp_dir, 'result.jpg')
            
            # 创建服务并初始化
            service = TraditionalCompositionService()
            await service.initialize()
            
            try:
                # 创建合成请求
                request = CompositionRequest(
                    background_path=bg_path,
                    target_path=fg_path,
                    weather_effect=WeatherEffect.SUNNY,
                    military_target="tank",
                    scene_type="urban",
                    matting_config=MattingConfig(
                        method=MattingMethod.THRESHOLD,
                        iterations=3
                    ),
                    blend_config=BlendConfig(
                        mode=BlendMode.NORMAL,
                        opacity=0.9,
                        color_match=True
                    ),
                    composition_config=CompositionConfig(
                        target_size=(300, 300)
                    ),
                    output_path=output_path
                )
                
                # 执行合成
                result = await service.compose_image(request)
                
                # 验证结果
                assert result.success is True
                assert result.image is not None
                assert os.path.exists(output_path)
                
                # 验证输出图像
                output_image = Image.open(output_path)
                assert output_image.size == (300, 300)
                
            finally:
                await service.cleanup()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
