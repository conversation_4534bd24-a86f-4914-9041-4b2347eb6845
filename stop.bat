@echo off
:: 设置代码页为UTF-8，同时处理可能的错误
chcp 65001 >nul 2>&1
if errorlevel 1 (
    echo Setting UTF-8 encoding failed, using default encoding
)

:: 设置控制台标题
title Stop Services - Military Target Dataset Generation Platform

:: 设置环境变量确保Python使用UTF-8编码
set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1
set LANG=zh_CN.UTF-8

echo ========================================
echo    Stop Military Target Dataset Generation Platform Services
echo ========================================
echo.

echo [STOPPING] Stopping backend service...
taskkill /f /im python.exe /fi "WINDOWTITLE eq Backend Service*" >nul 2>&1
taskkill /f /im uvicorn.exe >nul 2>&1

echo [STOPPING] Stopping frontend application...
taskkill /f /im python.exe /fi "WINDOWTITLE eq Frontend Application*" >nul 2>&1

:: 更彻底的清理
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq python.exe" /fo table /nh ^| findstr uvicorn') do (
    taskkill /f /pid %%i >nul 2>&1
)

for /f "tokens=2" %%i in ('tasklist /fi "imagename eq python.exe" /fo table /nh ^| findstr main_window') do (
    taskkill /f /pid %%i >nul 2>&1
)

echo.
echo [COMPLETED] All services have been stopped
echo.
pause 