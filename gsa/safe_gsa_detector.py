#!/usr/bin/env python3
"""
安全的GSA检测器 - 避免数据类型冲突
专门解决"expected scalar type Half but found Float"错误
"""

import os
import sys
import warnings
import torch
from pathlib import Path
from typing import List, Dict, Any
import requests

# 添加GroundingDINO路径
current_dir = Path(__file__).parent
grounding_dino_path = current_dir / "GroundingDINO"
if str(grounding_dino_path) not in sys.path:
    sys.path.insert(0, str(grounding_dino_path))

# 安全配置
SAFE_DEVICE = "cuda" if torch.cuda.is_available() else "cpu"
SAFE_FP16 = False  # 默认禁用FP16避免类型冲突

# 检测GPU是否真正支持FP16
if SAFE_DEVICE == "cuda":
    try:
        capability = torch.cuda.get_device_capability()
        # 只在支持Tensor Cores的GPU上考虑FP16
        if capability[0] >= 7:
            SAFE_FP16 = False  # 仍然保守，避免类型冲突
    except:
        SAFE_FP16 = False

print(f"[GSA] 安全GSA配置: 设备={SAFE_DEVICE}, FP16={SAFE_FP16}")

def download_model_with_proxy():
    """使用代理下载模型"""
    from gsa_config import CHECKPOINT_PATH,CHECKPOINT_URL,PROXIES
    if os.path.exists(CHECKPOINT_PATH):
        print(f"模型已存在: {CHECKPOINT_PATH}")
        return True
    
    print(f"正在下载模型: {CHECKPOINT_URL}")
    try:
        response = requests.get(CHECKPOINT_URL, proxies=PROXIES, stream=True)
        response.raise_for_status()
        
        with open(CHECKPOINT_PATH, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        print(f"模型下载完成: {CHECKPOINT_PATH}")
        return True
    except Exception as e:
        print(f"模型下载失败: {e}")
        return False

class SafeGSADetector:
    """安全的GSA检测器类"""
    
    def __init__(self):
        self.model = None
        self.model_dtype = torch.float32  # 默认FP32
        self.device = SAFE_DEVICE
        self.fp16_enabled = SAFE_FP16
        
    def load_model(self, config_path: str, checkpoint_path: str):
        """安全加载模型"""
        try:
            from groundingdino.util.inference import load_model
            
            print("[GSA] 安全加载GSA模型...")

            # 加载模型到指定设备
            if not os.path.exists(checkpoint_path):
                if not download_model_with_proxy():
                    raise RuntimeError("模型下载失败")

            self.model = load_model(config_path, checkpoint_path, device=self.device)

            # 获取模型的原始数据类型
            self.model_dtype = next(self.model.parameters()).dtype
            print(f"[GSA] 模型原始数据类型: {self.model_dtype}")

            # 安全的FP16处理
            if self.fp16_enabled and self.device == "cuda":
                try:
                    # 测试FP16转换
                    test_tensor = torch.randn(1, 3, 224, 224, device=self.device)
                    test_tensor_fp16 = test_tensor.half()

                    # 如果测试成功，转换模型
                    self.model = self.model.half()
                    self.model_dtype = torch.float16
                    print("[GSA] 成功启用FP16模式")
                except Exception as e:
                    print(f"[GSA] FP16转换失败，使用FP32: {e}")
                    self.model = self.model.float()
                    self.model_dtype = torch.float32
            else:
                # 确保使用FP32
                self.model = self.model.float()
                self.model_dtype = torch.float32
                print("[GSA] 使用安全的FP32模式")

            print("[GSA] GSA模型加载成功")
            return True
            
        except Exception as e:
            print(f"[GSA] 模型加载失败: {e}")
            return False
    
    def safe_image_preprocessing(self, image_tensor):
        """安全的图像预处理"""
        try:
            # 确保图像在正确的设备上
            if image_tensor.device.type != self.device:
                image_tensor = image_tensor.to(self.device)
            
            # 安全的数据类型转换
            if self.model_dtype == torch.float16:
                if image_tensor.dtype != torch.float16:
                    image_tensor = image_tensor.half()
                    print("[GSA] 图像转换为FP16")
            else:
                if image_tensor.dtype != torch.float32:
                    image_tensor = image_tensor.float()
                    print("[GSA] 图像转换为FP32")
            
            return image_tensor
            
        except Exception as e:
            print(f"[GSA] 图像预处理警告: {e}")
            # 回退到最安全的FP32
            return image_tensor.float().to(self.device)
    
    def detect(self, image_path: str, prompt: str, **kwargs):
        """安全检测"""
        try:
            from groundingdino.util.inference import load_image, predict
            
            if self.model is None:
                print("[GSA] 模型未加载")
                return []
            
            # 加载图像
            image_source, image = load_image(image_path)
            
            # 安全预处理
            image = self.safe_image_preprocessing(image)
            
            # 执行检测
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")  # 抑制已知警告
                
                boxes, logits, phrases = predict(
                    model=self.model,
                    image=image,
                    caption=prompt,
                    box_threshold=kwargs.get('box_threshold', 0.1),
                    text_threshold=kwargs.get('text_threshold', 0.05),
                    device=self.device,
                )
            
            # 处理结果
            results = []
            if len(boxes) > 0:
                for i, (box, logit, phrase) in enumerate(zip(boxes, logits, phrases)):
                    # 转换为CPU numpy数组以避免设备问题
                    box_cpu = box.cpu().numpy() if hasattr(box, 'cpu') else box
                    logit_cpu = logit.cpu().item() if hasattr(logit, 'cpu') else float(logit)
                    
                    result = {
                        'box': box_cpu.tolist(),
                        'confidence': logit_cpu,
                        'label': phrase,
                        'chinese_label': self._translate_label(phrase)
                    }
                    results.append(result)
            
            print(f"[GSA] 检测完成，找到 {len(results)} 个目标")
            return results

        except Exception as e:
            print(f"[GSA] 检测失败: {e}")
            print(f"错误类型: {type(e).__name__}")
            return []
    
    def _translate_label(self, label: str) -> str:
        """标签翻译"""
        translations = {
            'tank': '坦克',
            'aircraft': '战机',
            'airplane': '战机',
            'plane': '战机',
            'ship': '舰艇',
            'boat': '舰艇',
            'vessel': '舰艇'
        }
        return translations.get(label.lower(), label)

# 全局检测器实例
_detector = None

def get_safe_detector():
    """获取安全检测器实例"""
    global _detector
    if _detector is None:
        _detector = SafeGSADetector()
    return _detector

def safe_detect_objects(image_path: str, prompt: str = "tank", **kwargs) -> List[Dict[str, Any]]:
    """安全的目标检测函数"""
    try:
        # 获取配置
        from gsa_config import CONFIG_PATH, CHECKPOINT_PATH

        detector = get_safe_detector()

        # 加载模型（如果未加载）
        if detector.model is None:
            if not detector.load_model(CONFIG_PATH, CHECKPOINT_PATH):
                return []

        # 执行检测
        results = detector.detect(image_path, prompt, **kwargs)

        # 处理保存选项
        output_dir = kwargs.get('output_dir', 'output')
        save_annotated = kwargs.get('save_annotated', True)
        save_original = kwargs.get('save_original', True)

        if results and (save_annotated or save_original):
            _save_detection_results(image_path, results, output_dir, save_annotated, save_original)

        return results

    except Exception as e:
        print(f"[GSA] 安全检测失败: {e}")
        return []

def _save_detection_results(image_path: str, results: List[Dict], output_dir: str, save_annotated: bool, save_original: bool):
    """保存检测结果图片"""
    import cv2
    import os
    from pathlib import Path

    try:
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        # 加载图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"[GSA] 无法加载图像: {image_path}")
            return

        # 保存原始图像副本
        if save_original:
            original_path = os.path.join(output_dir, f"original_{os.path.basename(image_path)}")
            success = cv2.imwrite(original_path, image)
            if success:
                print(f"[GSA] 原始图像已保存: {original_path}")
            else:
                print(f"[GSA] 保存原始图像失败: {original_path}")

        # 保存标注图像
        if save_annotated and results:
            annotated_image = image.copy()

            # 绘制检测框
            for result in results:
                bbox = result.get('box', result.get('bbox', []))
                if len(bbox) == 4:
                    h, w = image.shape[:2]

                    # 安全检测器返回的是归一化的cxcywh格式
                    if all(0 <= coord <= 1 for coord in bbox):
                        # 归一化的cxcywh坐标，转换为像素xyxy坐标
                        cx, cy, bw, bh = bbox
                        cx_pixel = cx * w
                        cy_pixel = cy * h
                        bw_pixel = bw * w
                        bh_pixel = bh * h

                        # 转换为xyxy格式
                        x1 = int(cx_pixel - bw_pixel / 2)
                        y1 = int(cy_pixel - bh_pixel / 2)
                        x2 = int(cx_pixel + bw_pixel / 2)
                        y2 = int(cy_pixel + bh_pixel / 2)

                        # 确保坐标在图像范围内
                        x1 = max(0, min(x1, w))
                        y1 = max(0, min(y1, h))
                        x2 = max(0, min(x2, w))
                        y2 = max(0, min(y2, h))
                    else:
                        # 像素坐标
                        x1, y1, x2, y2 = [int(coord) for coord in bbox]

                    # 绘制边界框
                    cv2.rectangle(annotated_image, (x1, y1), (x2, y2), (0, 255, 0), 2)

                    # 添加标签
                    label = result.get('chinese_label', result.get('label', ''))
                    confidence = result.get('confidence', 0.0)
                    text = f"{label}: {confidence:.2f}"

                    # 计算文本位置
                    text_size = cv2.getTextSize(text, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)[0]
                    cv2.rectangle(annotated_image, (x1, y1-text_size[1]-5), (x1+text_size[0], y1), (0, 255, 0), -1)
                    cv2.putText(annotated_image, text, (x1, y1-5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)

            # 保存标注图像
            annotated_path = os.path.join(output_dir, f"annotated_{os.path.basename(image_path)}")
            success = cv2.imwrite(annotated_path, annotated_image)
            if success:
                print(f"[GSA] 标注图像已保存: {annotated_path}")
                # 验证文件是否真的存在
                if os.path.exists(annotated_path):
                    file_size = os.path.getsize(annotated_path)
                    print(f"   文件大小: {file_size} 字节")
                else:
                    print(f"[GSA] 警告：文件保存后不存在: {annotated_path}")
            else:
                print(f"[GSA] 保存标注图像失败: {annotated_path}")

    except Exception as e:
        print(f"[GSA] 保存检测结果时出错: {e}")
        import traceback
        traceback.print_exc()

def test_safe_detection():
    """测试安全检测"""
    print("[GSA] 测试安全GSA检测...")

    test_image = current_dir / "assets" / "demo7.jpg"
    if not test_image.exists():
        print("[GSA] 测试图像不存在")
        return False

    try:
        results = safe_detect_objects(str(test_image), "horse")
        print(f"[GSA] 测试成功，检测到 {len(results)} 个目标")
        for result in results:
            print(f"  - {result['chinese_label']}: {result['confidence']:.3f}")
        return True
    except Exception as e:
        print(f"[GSA] 测试失败: {e}")
        return False

if __name__ == "__main__":
    print("[GSA] 安全GSA检测器测试")
    print("=" * 40)

    # 显示配置
    print(f"设备: {SAFE_DEVICE}")
    print(f"FP16: {SAFE_FP16}")

    # 运行测试
    success = test_safe_detection()

    if success:
        print("\n[GSA] 安全检测器工作正常！")
        print("可以使用 safe_detect_objects() 函数进行检测")
    else:
        print("\n[GSA] 安全检测器测试失败")
        print("请检查模型文件和依赖")
