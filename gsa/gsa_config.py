"""
GSA独立配置文件
基于用户偏好的最佳检测参数配置
"""

import os
from pathlib import Path

# 代理配置 - 用户偏好，使用7890端口
PROXIES = {
    'http': 'http://127.0.0.1:7890',
    'https': 'http://127.0.0.1:7890'
}

# 获取当前文件所在目录（gsa目录）
GSA_DIR = Path(__file__).parent
GROUNDING_DINO_DIR = GSA_DIR / "GroundingDINO"

# 模型路径配置 - 使用绝对路径
CONFIG_PATH = str(GROUNDING_DINO_DIR / "groundingdino" / "config" / "GroundingDINO_SwinT_OGC.py")
CHECKPOINT_PATH = str(GSA_DIR / "groundingdino_swint_ogc.pth")
CHECKPOINT_URL = "https://github.com/IDEA-Research/GroundingDINO/releases/download/v0.1.0-alpha/groundingdino_swint_ogc.pth"

# 路径验证函数
def validate_paths():
    """验证所有必需的路径和文件"""
    issues = []

    if not os.path.exists(CONFIG_PATH):
        issues.append(f"配置文件不存在: {CONFIG_PATH}")

    if not os.path.exists(CHECKPOINT_PATH):
        issues.append(f"模型文件不存在: {CHECKPOINT_PATH}")

    if not os.path.exists(str(GROUNDING_DINO_DIR)):
        issues.append(f"GroundingDINO目录不存在: {GROUNDING_DINO_DIR}")

    return issues

def get_model_info():
    """获取模型信息"""
    return {
        'gsa_dir': str(GSA_DIR),
        'grounding_dino_dir': str(GROUNDING_DINO_DIR),
        'config_path': CONFIG_PATH,
        'checkpoint_path': CHECKPOINT_PATH,
        'checkpoint_url': CHECKPOINT_URL,
        'config_exists': os.path.exists(CONFIG_PATH),
        'checkpoint_exists': os.path.exists(CHECKPOINT_PATH),
        'validation_issues': validate_paths()
    }

# 设备配置 - 用户偏好CUDA 12.1，启用GPU处理
DEVICE = "cuda"
# 智能FP16配置 - 自动检测GPU兼容性
FP16_INFERENCE = False  # 默认禁用，避免数据类型冲突

# 自动检测是否启用FP16
def get_fp16_setting():
    """智能检测是否应该启用FP16"""
    try:
        import torch
        if torch.cuda.is_available():
            # 检查GPU计算能力 (7.0+支持Tensor Cores)
            capability = torch.cuda.get_device_capability()
            if capability[0] >= 7:
                return True
            else:
                print(f"⚠️ GPU计算能力 {capability[0]}.{capability[1]} 不支持高效FP16，使用FP32")
                return False
        else:
            print("⚠️ CUDA不可用，使用CPU模式")
            return False
    except Exception as e:
        print(f"⚠️ FP16检测失败: {e}，使用FP32")
        return False

# 动态设置FP16 - 暂时强制禁用以确保稳定性
if DEVICE == "cuda":
    # FP16_INFERENCE = get_fp16_setting()  # 暂时禁用自动检测
    FP16_INFERENCE = False  # 强制使用FP32确保兼容性
    print("🔧 安全GSA配置: 设备=cuda, FP16=False")

# 最佳检测参数 - 基于用户偏好的坦克检测优化
CONFIDENCE_THRESHOLD = 0.1  # 最佳置信度阈值
TEXT_THRESHOLD = 0.05       # 最佳文本阈值
BOX_THRESHOLD = 0.1         # 最佳边界框阈值

# 简化prompt配置 - 用户偏好
SIMPLE_PROMPTS = {
    "tank": "tank",
    "aircraft": "aircraft", 
    "ship": "ship",
    "all_military": "tank . aircraft . ship"
}

# 默认使用最简单的prompt
DEFAULT_PROMPT = "tank"

# 输出配置
OUTPUT_DIR = str(GSA_DIR / "output")
SAVE_ANNOTATED = True
SAVE_ORIGINAL = True

# 中文标签映射
LABEL_MAPPING = {
    "tank": "坦克",
    "aircraft": "战机", 
    "ship": "舰艇"
}

# 调试信息
if __name__ == "__main__":
    print("=== GSA配置信息 ===")
    print(f"GSA目录: {GSA_DIR}")
    print(f"GroundingDINO目录: {GROUNDING_DINO_DIR}")
    print(f"配置文件路径: {CONFIG_PATH}")
    print(f"配置文件存在: {Path(CONFIG_PATH).exists()}")
    print(f"模型文件路径: {CHECKPOINT_PATH}")
    print(f"模型文件存在: {Path(CHECKPOINT_PATH).exists()}")
    print(f"设备: {DEVICE}")
    print(f"代理: {PROXIES}")
