#!/usr/bin/env python3
"""
离线GSA检测器
避免网络连接问题的版本
"""

import os
import sys
import warnings
from pathlib import Path
from typing import List, Dict, Any, Optional

# 设置离线模式
os.environ['HF_HUB_OFFLINE'] = '1'
os.environ['TRANSFORMERS_OFFLINE'] = '1'
os.environ['HF_DATASETS_OFFLINE'] = '1'

# 清除代理设置
for proxy_var in ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']:
    if proxy_var in os.environ:
        del os.environ[proxy_var]

# 导入必要的库
import torch
import requests
from PIL import Image
import numpy as np

# 添加GroundingDINO到Python路径
current_dir = Path(__file__).parent
grounding_dino_path = current_dir / "GroundingDINO"
if grounding_dino_path.exists() and str(grounding_dino_path) not in sys.path:
    sys.path.insert(0, str(grounding_dino_path))

# 导入GSA配置
from gsa_config import (
    CONFIG_PATH, CHECKPOINT_PATH, DEVICE, FP16_INFERENCE,
    CONFIDENCE_THRESHOLD, TEXT_THRESHOLD, BOX_THRESHOLD,
    OUTPUT_DIR, SAVE_ANNOTATED, SAVE_ORIGINAL, LABEL_MAPPING
)

print(f"[离线GSA] 配置: 设备={DEVICE}, FP16={FP16_INFERENCE}")

class OfflineGSADetector:
    """离线GSA检测器类"""
    
    def __init__(self):
        self.model = None
        self.model_dtype = torch.float32
        self.device = DEVICE
        self.fp16_enabled = FP16_INFERENCE
        
    def load_model(self, config_path: str, checkpoint_path: str):
        """加载模型（离线模式）"""
        try:
            # 抑制网络相关警告
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                
                from groundingdino.util.inference import load_model
                
                print("[离线GSA] 加载GSA模型（离线模式）...")
                
                if not os.path.exists(checkpoint_path):
                    raise RuntimeError(f"模型文件不存在: {checkpoint_path}")
                
                # 加载模型
                self.model = load_model(config_path, checkpoint_path, device=self.device)
                
                # 设置数据类型
                self.model_dtype = next(self.model.parameters()).dtype
                print(f"[离线GSA] 模型数据类型: {self.model_dtype}")
                
                # 安全的FP16处理
                if self.fp16_enabled and self.device == "cuda":
                    try:
                        self.model = self.model.half()
                        self.model_dtype = torch.float16
                        print("[离线GSA] 启用FP16模式")
                    except Exception as e:
                        print(f"[离线GSA] FP16转换失败，使用FP32: {e}")
                        self.model = self.model.float()
                        self.model_dtype = torch.float32
                else:
                    self.model = self.model.float()
                    self.model_dtype = torch.float32
                    print("[离线GSA] 使用FP32模式")
                
                print("[离线GSA] 模型加载成功")
                return True
                
        except Exception as e:
            print(f"[离线GSA] 模型加载失败: {e}")
            return False
    
    def detect(self, image_path: str, prompt: str, **kwargs):
        """执行检测"""
        try:
            if self.model is None:
                print("[离线GSA] 模型未加载")
                return []
            
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                
                from groundingdino.util.inference import load_image, predict
                
                # 加载图像
                image_source, image = load_image(image_path)
                
                # 数据类型处理
                if image.device.type != self.device:
                    image = image.to(self.device)
                
                if self.model_dtype == torch.float16:
                    if image.dtype != torch.float16:
                        image = image.half()
                else:
                    if image.dtype != torch.float32:
                        image = image.float()
                
                # 执行检测
                boxes, logits, phrases = predict(
                    model=self.model,
                    image=image,
                    caption=prompt,
                    box_threshold=kwargs.get('box_threshold', CONFIDENCE_THRESHOLD),
                    text_threshold=kwargs.get('text_threshold', TEXT_THRESHOLD),
                    device=self.device,
                )
                
                # 处理结果
                results = []
                if len(boxes) > 0:
                    for i, (box, logit, phrase) in enumerate(zip(boxes, logits, phrases)):
                        box_cpu = box.cpu().numpy() if hasattr(box, 'cpu') else box
                        logit_cpu = logit.cpu().item() if hasattr(logit, 'cpu') else float(logit)
                        
                        result = {
                            'box': box_cpu.tolist(),
                            'confidence': logit_cpu,
                            'label': phrase,
                            'chinese_label': LABEL_MAPPING.get(phrase, phrase)
                        }
                        results.append(result)
                
                print(f"[离线GSA] 检测完成，找到 {len(results)} 个目标")
                return results
                
        except Exception as e:
            print(f"[离线GSA] 检测失败: {e}")
            return []

# 全局检测器实例
_offline_detector = None

def get_offline_detector():
    """获取离线检测器实例"""
    global _offline_detector
    if _offline_detector is None:
        _offline_detector = OfflineGSADetector()
    return _offline_detector

def offline_detect_objects(image_path: str, prompt: str = "tank", **kwargs) -> List[Dict[str, Any]]:
    """离线目标检测函数"""
    try:
        detector = get_offline_detector()
        
        # 加载模型（如果未加载）
        if detector.model is None:
            if not detector.load_model(CONFIG_PATH, CHECKPOINT_PATH):
                return []
        
        # 执行检测
        results = detector.detect(image_path, prompt, **kwargs)
        
        # 处理保存选项
        output_dir = kwargs.get('output_dir', OUTPUT_DIR)
        save_annotated = kwargs.get('save_annotated', SAVE_ANNOTATED)
        save_original = kwargs.get('save_original', SAVE_ORIGINAL)
        
        if results and (save_annotated or save_original):
            _save_detection_results(image_path, results, output_dir, save_annotated, save_original)
        
        return results
        
    except Exception as e:
        print(f"[离线GSA] 检测失败: {e}")
        return []

def _save_detection_results(image_path: str, results: List[Dict], output_dir: str, save_annotated: bool, save_original: bool):
    """保存检测结果图片"""
    import cv2
    import os
    from pathlib import Path

    try:
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        # 加载图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"[离线GSA] 无法加载图像: {image_path}")
            return

        # 保存原始图像副本
        if save_original:
            original_path = os.path.join(output_dir, f"original_{os.path.basename(image_path)}")
            success = cv2.imwrite(original_path, image)
            if success:
                print(f"[离线GSA] 原始图像已保存: {original_path}")
            else:
                print(f"[离线GSA] 保存原始图像失败: {original_path}")

        # 保存标注图像
        if save_annotated and results:
            annotated_image = image.copy()
            height, width = annotated_image.shape[:2]

            # 绘制检测框和标签
            for result in results:
                try:
                    # 获取边界框坐标（归一化坐标）
                    box = result['box']
                    confidence = result['confidence']
                    label = result.get('chinese_label', result.get('label', 'unknown'))

                    # 转换归一化坐标到像素坐标
                    x1 = int(box[0] * width)
                    y1 = int(box[1] * height)
                    x2 = int(box[2] * width)
                    y2 = int(box[3] * height)

                    # 绘制边界框
                    cv2.rectangle(annotated_image, (x1, y1), (x2, y2), (0, 255, 0), 2)

                    # 绘制标签和置信度
                    label_text = f"{label}: {confidence:.2f}"
                    label_size = cv2.getTextSize(label_text, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
                    
                    # 绘制标签背景
                    cv2.rectangle(annotated_image, 
                                (x1, y1 - label_size[1] - 10), 
                                (x1 + label_size[0], y1), 
                                (0, 255, 0), -1)
                    
                    # 绘制标签文本
                    cv2.putText(annotated_image, label_text, 
                              (x1, y1 - 5), 
                              cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)

                except Exception as e:
                    print(f"[离线GSA] 绘制检测框时出错: {e}")
                    continue

            # 保存标注图像
            annotated_path = os.path.join(output_dir, f"annotated_{os.path.basename(image_path)}")
            success = cv2.imwrite(annotated_path, annotated_image)
            if success:
                print(f"[离线GSA] 标注图像已保存: {annotated_path}")
                # 验证文件是否真的存在
                if os.path.exists(annotated_path):
                    file_size = os.path.getsize(annotated_path)
                    print(f"   文件大小: {file_size} 字节")
                else:
                    print(f"[离线GSA] 警告：文件保存后不存在: {annotated_path}")
            else:
                print(f"[离线GSA] 保存标注图像失败: {annotated_path}")

    except Exception as e:
        print(f"[离线GSA] 保存检测结果时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 测试离线检测器
    print("测试离线GSA检测器...")
    results = offline_detect_objects("test_image.jpg", "tank")
    print(f"检测结果: {results}")
