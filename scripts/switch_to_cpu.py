#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Switch AI mode to CPU mode script
Uninstall CUDA PyTorch and install CPU version
"""

import sys
import os
import subprocess
import argparse
from pathlib import Path

# 确保输出使用UTF-8编码（Windows系统）
if sys.platform.startswith('win'):
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'

def check_current_pytorch():
    """检查当前PyTorch状态"""
    try:
        import torch
        print(f"PyTorch version: {torch.__version__}")
        print(f"CUDA available: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"CUDA version: {torch.version.cuda}")
            print(f"GPU count: {torch.cuda.device_count()}")
            return "cuda"
        else:
            print("Running on CPU mode")
            return "cpu"
    except ImportError:
        print("PyTorch not installed")
        return "none"
    except Exception as e:
        print(f"Error checking PyTorch: {str(e)}")
        return "error"

def uninstall_pytorch():
    """卸载当前版本的PyTorch"""
    print("Uninstalling current PyTorch installation...")
    
    # PyTorch相关包列表
    pytorch_packages = [
        'torch',
        'torchvision', 
        'torchaudio',
        'torchtext',
        'torchdata',
        'xformers'  # 通常与CUDA版本一起安装
    ]
    
    # 设置环境变量
    env = os.environ.copy()
    env['PYTHONIOENCODING'] = 'utf-8'
    env['PYTHONUTF8'] = '1'
    
    success_count = 0
    for package in pytorch_packages:
        try:
            print(f"Uninstalling {package}...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "uninstall", package, "-y"
            ], check=True, env=env, capture_output=True, text=True)
            print(f"  ✓ {package} uninstalled successfully")
            success_count += 1
        except subprocess.CalledProcessError as e:
            if "not installed" in e.stdout.lower() or "not installed" in e.stderr.lower():
                print(f"  - {package} was not installed")
            else:
                print(f"  ✗ Failed to uninstall {package}: {e}")
        except Exception as e:
            print(f"  ✗ Error uninstalling {package}: {str(e)}")
    
    print(f"Uninstallation completed: {success_count}/{len(pytorch_packages)} packages processed")
    return True

def install_cpu_pytorch():
    """安装CPU版本的PyTorch"""
    print("Installing CPU version of PyTorch...")
    
    # 设置环境变量
    env = os.environ.copy()
    env['PYTHONIOENCODING'] = 'utf-8'
    env['PYTHONUTF8'] = '1'
    
    # 首先升级pip以避免SSL问题
    print("Upgrading pip to latest version...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "--upgrade", "pip", 
            "--trusted-host", "pypi.org", "--trusted-host", "pypi.python.org", "--trusted-host", "files.pythonhosted.org"
        ], check=True, env=env, timeout=300)
        print("  ✓ pip upgraded successfully")
    except Exception as e:
        print(f"  ⚠️ pip upgrade failed, continuing anyway: {e}")
    
    # CPU版本的PyTorch安装命令
    install_commands = [
        # 主要PyTorch包 (CPU版本) - 使用trusted-host避免SSL问题
        [sys.executable, "-m", "pip", "install", "torch", "torchvision", "torchaudio", 
         "-i", "https://repo.huaweicloud.com/repository/pypi/simple/", "--trusted-host", "repo.huaweicloud.com"]
    ]
    
    # 备用安装命令（如果主命令失败）
    fallback_commands = [
        # 使用默认PyPI源安装
        [sys.executable, "-m", "pip", "install", "torch", "torchvision", "torchaudio",
         "-i", "https://repo.huaweicloud.com/repository/pypi/simple/", "--trusted-host", "repo.huaweicloud.com"],
        
        # 尝试使用清华镜像源
        [sys.executable, "-m", "pip", "install", "torch", "torchvision", "torchaudio",
         "-i", "https://repo.huaweicloud.com/repository/pypi/simple/", "--trusted-host", "repo.huaweicloud.com"]
    ]
    
    success = False
    
    # 尝试主要安装命令
    for i, cmd in enumerate(install_commands, 1):
        try:
            print(f"Installing PyTorch packages ({i}/{len(install_commands)})...")
            print(f"Command: {' '.join(cmd[:8])}...")  # 只显示前几个参数避免太长
            
            result = subprocess.run(cmd, check=True, env=env, timeout=900)  # 15分钟超时
            print(f"  ✓ Installation step {i} completed successfully")
            success = True
            
        except subprocess.TimeoutExpired:
            print(f"  ✗ Installation step {i} timeout (15 minutes)")
            print("Trying fallback installation methods...")
            break
        except subprocess.CalledProcessError as e:
            print(f"  ✗ Installation step {i} failed: {e}")
            print("Trying fallback installation methods...")
            break
        except Exception as e:
            print(f"  ✗ Error in installation step {i}: {str(e)}")
            print("Trying fallback installation methods...")
            break
    
    # 如果主要安装失败，尝试备用方案
    if not success:
        print("Main installation failed, trying fallback methods...")
        for i, cmd in enumerate(fallback_commands, 1):
            try:
                print(f"Trying fallback method {i}/{len(fallback_commands)}...")
                print(f"Command: {' '.join(cmd[:6])}...")
                
                result = subprocess.run(cmd, check=True, env=env, timeout=900)
                print(f"  ✓ Fallback installation {i} completed successfully")
                success = True
                break
                
            except Exception as e:
                print(f"  ✗ Fallback method {i} failed: {str(e)}")
                continue
    
    if success:
        print("CPU PyTorch installation completed!")
        return True
    else:
        print("All installation methods failed!")
        print("\nTroubleshooting suggestions:")
        print("1. Check your internet connection")
        print("2. Try running the script with administrator privileges")
        print("3. Temporarily disable antivirus/firewall")
        print("4. Try manual installation:")
        print("   pip install torch torchvision torchaudio --trusted-host pypi.org")
        return False

def verify_cpu_installation():
    """验证CPU安装"""
    print("Verifying CPU PyTorch installation...")
    
    try:
        # 重新导入torch以获取最新版本
        if 'torch' in sys.modules:
            del sys.modules['torch']
        
        import torch
        
        print(f"PyTorch version: {torch.__version__}")
        print(f"CUDA available: {torch.cuda.is_available()}")
        
        if not torch.cuda.is_available():
            print("✓ Successfully switched to CPU mode")
            
            # 测试CPU功能
            print("Testing CPU functionality...")
            x = torch.randn(100, 100)
            y = torch.randn(100, 100)
            z = torch.mm(x, y)
            print(f"  ✓ CPU tensor operations working (result shape: {z.shape})")
            
            return True
        else:
            print("  ⚠️ CUDA is still available - this might be expected if CUDA drivers are installed")
            print("  ✓ PyTorch will use CPU by default when device is set to 'cpu'")
            return True
            
    except ImportError as e:
        print(f"  ✗ Failed to import torch: {e}")
        return False
    except Exception as e:
        print(f"  ✗ Error verifying installation: {str(e)}")
        return False

def update_config_file():
    """更新配置文件以使用CPU"""
    config_file = Path(__file__).parent.parent / "backend" / "app" / "core" / "config.py"
    
    if not config_file.exists():
        print(f"Config file not found: {config_file}")
        return False
    
    try:
        # 读取配置文件
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 更新设备配置 - 查找ai_device字段
        if 'ai_device: str = Field(\n        default="cuda"' in content:
            content = content.replace(
                'ai_device: str = Field(\n        default="cuda"',
                'ai_device: str = Field(\n        default="cpu"'
            )
            print("Updated ai_device setting from cuda to cpu")
        elif 'ai_device: str = Field(default="cuda"' in content:
            content = content.replace(
                'ai_device: str = Field(default="cuda"',
                'ai_device: str = Field(default="cpu"'
            )
            print("Updated ai_device setting from cuda to cpu")
        elif 'ai_device: str = Field(\n        default="auto"' in content:
            content = content.replace(
                'ai_device: str = Field(\n        default="auto"',
                'ai_device: str = Field(\n        default="cpu"'
            )
            print("Updated ai_device setting from auto to cpu")
        elif 'ai_device: str = Field(default="auto"' in content:
            content = content.replace(
                'ai_device: str = Field(default="auto"',
                'ai_device: str = Field(default="cpu"'
            )
            print("Updated ai_device setting from auto to cpu")
        else:
            print("ai_device setting not found in config file or already set to CPU")
        
        # 写回配置文件
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"Configuration updated: {config_file}")
        return True
        
    except Exception as e:
        print(f"Error updating config file: {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Switch AI mode to CPU")
    parser.add_argument("--skip-uninstall", action="store_true", help="Skip uninstalling current PyTorch")
    parser.add_argument("--verify-only", action="store_true", help="Only verify current installation")
    
    args = parser.parse_args()
    
    print("========================================")
    print("    Switch to CPU Mode - PyTorch")
    print("========================================")
    print()
    
    if args.verify_only:
        print("Verifying current installation...")
        check_current_pytorch()
        return 0
    
    # 检查当前PyTorch状态
    print("Step 1/4: Checking current PyTorch installation...")
    current_mode = check_current_pytorch()
    
    if current_mode == "cpu":
        print("Already running in CPU mode!")
        update_config_file()
        return 0
    
    # 卸载当前版本
    if not args.skip_uninstall:
        print("\nStep 2/4: Uninstalling current PyTorch...")
        if not uninstall_pytorch():
            print("Failed to uninstall current PyTorch")
            return 1
    else:
        print("\nStep 2/4: Skipping PyTorch uninstallation...")
    
    # 安装CPU版本
    print("\nStep 3/4: Installing CPU PyTorch...")
    if not install_cpu_pytorch():
        print("Failed to install CPU PyTorch")
        return 1
    
    # 验证安装
    print("\nStep 4/4: Verifying installation...")
    if not verify_cpu_installation():
        print("CPU PyTorch verification failed")
        return 1
    
    # 更新配置文件
    print("\nUpdating configuration...")
    update_config_file()
    
    print("\n========================================")
    print("✓ CPU mode switch completed successfully!")
    print("✓ PyTorch CPU version is ready")
    print("✓ Configuration updated")
    print()
    print("Please restart the application to use CPU mode.")
    print("========================================")
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 