#!/usr/bin/env python3
"""
模型下载脚本
用于手动下载Stable Diffusion模型，解决网络连接问题
"""

import os
import sys
import requests
import json
from pathlib import Path
from typing import Dict, Any
import logging
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 代理配置
PROXY_CONFIG = {
    'http': 'http://127.0.0.1:7890',
    'https': 'http://127.0.0.1:7890'
}

class ModelDownloader:
    """模型下载器"""
    
    def __init__(self):
        self.models_dir = Path("models")
        self.models_dir.mkdir(exist_ok=True)
        
        # 模型配置
        self.model_configs = {
            "stable-diffusion-v1-5": {
                "model_id": "runwayml/stable-diffusion-v1-5",
                "local_path": self.models_dir / "stable-diffusion-v1-5",
                "files": [
                    "model_index.json",
                    "scheduler/scheduler_config.json",
                    "text_encoder/config.json",
                    "text_encoder/pytorch_model.bin",
                    "tokenizer/merges.txt",
                    "tokenizer/special_tokens_map.json",
                    "tokenizer/tokenizer_config.json",
                    "tokenizer/vocab.json",
                    "unet/config.json",
                    "unet/diffusion_pytorch_model.bin",
                    "vae/config.json",
                    "vae/diffusion_pytorch_model.bin",
                    "feature_extractor/preprocessor_config.json",
                    "safety_checker/config.json",
                    "safety_checker/pytorch_model.bin"
                ],
                "base_url": "https://huggingface.co/runwayml/stable-diffusion-v1-5/resolve/main/"
            }
        }
    
    def test_proxy_connection(self) -> bool:
        """测试代理连接"""
        try:
            logger.info("测试代理连接...")
            test_url = "https://httpbin.org/ip"
            response = requests.get(test_url, proxies=PROXY_CONFIG, timeout=10)
            response.raise_for_status()
            logger.info("代理连接测试成功")
            return True
        except Exception as e:
            logger.warning(f"代理连接测试失败: {str(e)}")
            logger.info("尝试直接连接...")
            try:
                response = requests.get(test_url, timeout=10)
                response.raise_for_status()
                logger.info("直接连接测试成功")
                return True
            except Exception as e2:
                logger.error(f"直接连接也失败: {str(e2)}")
                return False

    def download_file(self, url: str, local_path: Path, timeout: int = 300, max_retries: int = 3) -> bool:
        """下载单个文件，支持代理和重试"""
        logger.info(f"下载文件: {url}")

        # 创建目录
        local_path.parent.mkdir(parents=True, exist_ok=True)

        for attempt in range(max_retries):
            try:
                logger.info(f"尝试下载 (第 {attempt + 1}/{max_retries} 次): {local_path.name}")

                # 首先尝试使用代理
                try:
                    response = requests.get(url, stream=True, timeout=timeout, proxies=PROXY_CONFIG)
                    response.raise_for_status()
                    logger.info("使用代理下载成功")
                except Exception as proxy_error:
                    logger.warning(f"代理下载失败: {str(proxy_error)}")
                    logger.info("尝试直接下载...")
                    response = requests.get(url, stream=True, timeout=timeout)
                    response.raise_for_status()
                    logger.info("直接下载成功")

                # 获取文件大小
                total_size = int(response.headers.get('content-length', 0))
                downloaded_size = 0

                with open(local_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            downloaded_size += len(chunk)

                            # 显示进度
                            if total_size > 0:
                                progress = (downloaded_size / total_size) * 100
                                if downloaded_size % (1024 * 1024) == 0:  # 每MB显示一次
                                    logger.info(f"下载进度: {progress:.1f}% ({downloaded_size // (1024*1024)}MB/{total_size // (1024*1024)}MB)")

                logger.info(f"下载完成: {local_path}")
                return True

            except Exception as e:
                logger.error(f"下载失败 (第 {attempt + 1} 次): {str(e)}")
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 5  # 递增等待时间
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    logger.error(f"所有重试都失败，放弃下载: {url}")

        return False
    
    def download_model(self, model_name: str) -> bool:
        """下载指定模型"""
        if model_name not in self.model_configs:
            logger.error(f"未知模型: {model_name}")
            return False

        # 测试网络连接
        if not self.test_proxy_connection():
            logger.error("网络连接测试失败，无法继续下载")
            return False

        config = self.model_configs[model_name]
        logger.info(f"开始下载模型: {config['model_id']}")
        logger.info(f"目标目录: {config['local_path']}")
        logger.info(f"需要下载 {len(config['files'])} 个文件")

        success_count = 0
        total_files = len(config['files'])

        for i, file_path in enumerate(config['files'], 1):
            url = config['base_url'] + file_path
            local_path = config['local_path'] / file_path

            logger.info(f"处理文件 {i}/{total_files}: {file_path}")

            # 检查文件是否已存在
            if local_path.exists():
                file_size = local_path.stat().st_size
                logger.info(f"文件已存在 ({file_size // 1024}KB)，跳过: {local_path.name}")
                success_count += 1
                continue

            if self.download_file(url, local_path):
                success_count += 1
                logger.info(f"✓ 成功下载: {file_path}")
            else:
                logger.error(f"✗ 下载失败: {file_path}")

        logger.info(f"下载结果: {success_count}/{total_files} 个文件成功")

        if success_count == total_files:
            logger.info("🎉 所有文件下载完成！")
            return True
        else:
            logger.warning(f"⚠️  有 {total_files - success_count} 个文件下载失败")
            return False
    
    def verify_model(self, model_name: str) -> bool:
        """验证模型完整性"""
        if model_name not in self.model_configs:
            return False
        
        config = self.model_configs[model_name]
        missing_files = []
        
        for file_path in config['files']:
            local_path = config['local_path'] / file_path
            if not local_path.exists():
                missing_files.append(file_path)
        
        if missing_files:
            logger.warning(f"缺失文件: {missing_files}")
            return False
        
        logger.info(f"模型验证通过: {model_name}")
        return True
    
    def create_symlink(self, model_name: str) -> bool:
        """创建符号链接到Hugging Face缓存目录"""
        try:
            config = self.model_configs[model_name]
            source_path = config['local_path']

            if not source_path.exists():
                logger.error(f"源模型目录不存在: {source_path}")
                return False

            # 获取Hugging Face缓存目录
            cache_dir = Path.home() / ".cache" / "huggingface" / "hub"
            cache_dir.mkdir(parents=True, exist_ok=True)

            # 创建模型缓存目录名称
            model_cache_name = f"models--{config['model_id'].replace('/', '--')}"
            model_cache_dir = cache_dir / model_cache_name

            if model_cache_dir.exists():
                logger.info(f"缓存目录已存在: {model_cache_dir}")
                return True

            # 创建符号链接或复制
            if os.name == 'nt':  # Windows
                import shutil
                shutil.copytree(source_path, model_cache_dir)
                logger.info(f"复制模型到缓存目录: {model_cache_dir}")
            else:  # Linux/Mac
                model_cache_dir.symlink_to(source_path)
                logger.info(f"创建符号链接: {model_cache_dir} -> {source_path}")

            return True

        except Exception as e:
            logger.error(f"创建缓存链接失败: {str(e)}")
            return False

def main():
    """主函数"""
    print("=== Stable Diffusion 模型下载工具 ===")
    print("配置信息:")
    print(f"- 代理服务器: {PROXY_CONFIG['http']}")
    print(f"- 模型存储目录: models/stable-diffusion-v1-5")
    print(f"- 需要下载文件数: 15个")
    print()

    downloader = ModelDownloader()

    print("可用操作:")
    print("1. 测试网络连接")
    print("2. 下载 Stable Diffusion v1.5")
    print("3. 验证模型完整性")
    print("4. 创建缓存链接")
    print("5. 全部执行")
    print("0. 退出")

    while True:
        choice = input("\n请选择操作 (0-5): ").strip()

        if choice == "0":
            print("退出程序")
            break
        elif choice == "1":
            print("\n🔍 测试网络连接...")
            if downloader.test_proxy_connection():
                print("✓ 网络连接正常")
            else:
                print("✗ 网络连接失败")
        elif choice == "2":
            print("\n📥 开始下载模型...")
            success = downloader.download_model("stable-diffusion-v1-5")
            if success:
                print("✓ 模型下载完成")
            else:
                print("✗ 模型下载失败，请检查网络连接或重试")
        elif choice == "3":
            print("\n🔍 验证模型完整性...")
            if downloader.verify_model("stable-diffusion-v1-5"):
                print("✓ 模型验证通过")
            else:
                print("✗ 模型验证失败，可能有文件缺失")
        elif choice == "4":
            print("\n🔗 创建缓存链接...")
            if downloader.create_symlink("stable-diffusion-v1-5"):
                print("✓ 缓存链接创建成功")
            else:
                print("✗ 缓存链接创建失败")
        elif choice == "5":
            print("\n🚀 执行完整流程...")

            # 测试连接
            print("0/4 测试网络连接...")
            if not downloader.test_proxy_connection():
                print("✗ 网络连接失败，无法继续")
                continue

            # 下载模型
            print("1/4 下载模型...")
            if not downloader.download_model("stable-diffusion-v1-5"):
                print("✗ 模型下载失败")
                continue

            # 验证模型
            print("2/4 验证模型...")
            if not downloader.verify_model("stable-diffusion-v1-5"):
                print("✗ 模型验证失败")
                continue

            # 创建缓存链接
            print("3/4 创建缓存链接...")
            if downloader.create_symlink("stable-diffusion-v1-5"):
                print("🎉 所有步骤完成！模型已准备就绪")
            else:
                print("⚠️  缓存链接创建失败，但模型文件已下载完成")
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main() 