#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
前端应用启动脚本
"""

import sys
import os
import subprocess
import argparse
from pathlib import Path

# 确保输出使用UTF-8编码（Windows系统）
if sys.platform.startswith('win'):
    # 设置环境变量确保Python使用UTF-8
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'

def main():
    parser = argparse.ArgumentParser(description="启动前端GUI应用")
    parser.add_argument("--backend-url", default="http://localhost:8000", 
                       help="后端服务地址")
    parser.add_argument("--debug", action="store_true", help="启用调试模式")
    
    args = parser.parse_args()
    
    # 获取项目根目录
    project_root = Path(__file__).parent.parent
    frontend_dir = project_root / "frontend"
    
    # 切换到前端目录
    os.chdir(frontend_dir)
    
    # 检查是否安装了依赖
    requirements_file = frontend_dir / "requirements.txt"
    if requirements_file.exists():
        print("Checking dependency installation...")
        try:
            # 设置环境变量确保pip使用UTF-8编码
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONUTF8'] = '1'

            # 使用华为云镜像（可通过环境变量覆盖）
            pip_index = os.getenv("PIP_INDEX_URL", "https://repo.huaweicloud.com/repository/pypi/simple/")
            pip_host = os.getenv("PIP_TRUSTED_HOST", "repo.huaweicloud.com")

            subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt",
                "-i", pip_index, "--trusted-host", pip_host
            ], check=True, env=env)
            print("Dependency check completed")
        except subprocess.CalledProcessError:
            print("Warning: Dependency installation may have issues")
    
    # 设置环境变量
    env = os.environ.copy()
    env["BACKEND_URL"] = args.backend_url
    env['PYTHONIOENCODING'] = 'utf-8'
    env['PYTHONUTF8'] = '1'
    
    if args.debug:
        env["DEBUG"] = "1"
    
    # 启动前端应用
    # 入口文件定位：先尝试绝对路径，再尝试当前工作目录，再全局搜索
    main_script = frontend_dir / "src" / "main_window.py"

    if not main_script.exists():
        alt_path = Path.cwd() / "src" / "main_window.py"
        if alt_path.exists():
            main_script = alt_path
        else:
            search_results = list(Path.cwd().rglob("main_window.py"))
            if search_results:
                main_script = search_results[0]

    if not main_script.exists():
        print(f"Error: Main program file not found: {main_script}")
        return 1
    
    print(f"Starting frontend application...")
    print(f"Backend service address: {args.backend_url}")
    print("Close window or press Ctrl+C to exit application")
    
    try:
        subprocess.run([sys.executable, str(main_script)], env=env)
    except KeyboardInterrupt:
        print("\nApplication exited")
    except Exception as e:
        print(f"Startup failed: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 