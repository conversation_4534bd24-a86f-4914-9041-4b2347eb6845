#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
后端服务启动脚本
"""

import sys
import os
import subprocess
import argparse
from pathlib import Path

# 确保输出使用UTF-8编码（Windows系统）
if sys.platform.startswith('win'):
    # 设置环境变量确保Python使用UTF-8
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'

def main():
    parser = argparse.ArgumentParser(description="启动后端AI生成服务")
    parser.add_argument("--host", default="0.0.0.0", help="服务器主机地址")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口")
    parser.add_argument("--reload", action="store_true", help="启用自动重载")
    parser.add_argument("--workers", type=int, default=1, help="工作进程数量")
    parser.add_argument("--skip-install-check", action="store_true", help="跳过依赖安装检查以加速启动")
    
    args = parser.parse_args()
    
    # 获取项目根目录
    project_root = Path(__file__).parent.parent
    backend_dir = project_root / "backend"
    
    # 切换到后端目录
    os.chdir(backend_dir)
    
    # 如果指定跳过依赖检查则直接启动
    if not args.skip_install_check:
        print("Checking key dependencies...")
        try:
            import fastapi  # type: ignore
            import uvicorn  # type: ignore
            import pydantic_settings  # type: ignore
            print("Key dependencies check passed")
        except ImportError as e:
            print(f"Missing key dependency: {e}")
            print("Installing missing dependencies...")

            # 设置环境变量确保pip使用UTF-8编码
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONUTF8'] = '1'

            # 先安装关键依赖
            try:
                subprocess.run([sys.executable, "-m", "pip", "install",
                              "pydantic-settings>=2.0.3", "accelerate>=0.20.0"],
                             check=True, env=env)
                print("Key dependencies installation completed")
            except subprocess.CalledProcessError:
                print("Warning: Key dependencies installation failed")

    # 安装完整依赖
    if not args.skip_install_check:
        requirements_file = backend_dir / "requirements.txt"
        if requirements_file.exists():
            print("Installing full dependencies...")
            try:
                env = os.environ.copy()
                env['PYTHONIOENCODING'] = 'utf-8'
                env['PYTHONUTF8'] = '1'

                subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"],
                             check=True, env=env)
                print("Full dependencies installation completed")
            except subprocess.CalledProcessError:
                print("Warning: Full dependencies installation may have issues")
    
    # 构建启动命令
    cmd = [
        sys.executable, "-m", "uvicorn",
        "app.main:app",
        "--host", args.host,
        "--port", str(args.port)
    ]
    
    if args.reload:
        cmd.append("--reload")
    
    if args.workers > 1:
        cmd.extend(["--workers", str(args.workers)])
    
    print(f"Starting backend service: {' '.join(cmd)}")
    print(f"Service address: http://{args.host}:{args.port}")
    print("Press Ctrl+C to stop service")
    
    try:
        subprocess.run(cmd)
    except KeyboardInterrupt:
        print("\nService stopped")

if __name__ == "__main__":
    main() 